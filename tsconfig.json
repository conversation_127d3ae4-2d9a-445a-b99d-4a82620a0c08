{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "node", "strict": true, "jsx": "react-jsx", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["resources/js/*"]}, "types": ["vite/client"]}, "include": ["resources/js/**/*.ts", "resources/js/**/*.tsx"], "exclude": ["node_modules"]}
# OTP Codes Cleanup System

This document describes the automated cleanup system for OTP codes in the application.

## Overview

The OTP cleanup system automatically removes old OTP codes from the database to prevent accumulation of unnecessary data. By default, it removes OTP codes older than 3 days.

## Components

### 1. Console Command: `otp:clear-old`

A Laravel Artisan command that can be run manually or scheduled.

**Usage:**
```bash
# Clear OTP codes older than 3 days (default)
php artisan otp:clear-old

# Clear OTP codes older than 7 days
php artisan otp:clear-old --days=7

# Dry run - see what would be deleted without actually deleting
php artisan otp:clear-old --dry-run

# Dry run with custom days
php artisan otp:clear-old --days=7 --dry-run
```

**Features:**
- Configurable retention period (default: 3 days)
- Dry-run mode for testing
- Detailed logging
- Production confirmation prompt
- Sample records preview in dry-run mode

### 2. Queued Job: `ClearOldOtpCodesJob`

A background job that can be dispatched to the queue system.

**Usage:**
```php
use App\Jobs\ClearOldOtpCodesJob;

// Dispatch with default 3 days retention
ClearOldOtpCodesJob::dispatch();

// Dispatch with custom retention period
ClearOldOtpCodesJob::dispatch(7); // 7 days
```

**Features:**
- Runs in background queue
- Automatic retry on failure
- Comprehensive logging
- Configurable retention period

### 3. Model Methods

The `OtpCode` model includes helper methods for cleanup operations.

**Available Methods:**
```php
// Count old OTP codes
$count = OtpCode::countOldCodes(3); // Count codes older than 3 days

// Clear old OTP codes
$deletedCount = OtpCode::clearOldCodes(3); // Delete codes older than 3 days

// Check if a specific OTP code is expired
$isExpired = $otpCode->isExpired();
```

## Scheduling

The cleanup is automatically scheduled to run every 3 days at 2:00 AM. This is configured in `app/Console/Kernel.php`.

### Current Schedule Configuration

```php
// Option 1: Run as console command (currently active)
$schedule->command('otp:clear-old')
    ->everyThreeDays()
    ->at('02:00')
    ->withoutOverlapping()
    ->runInBackground();

// Option 2: Dispatch as queued job (commented out)
// $schedule->job(new \App\Jobs\ClearOldOtpCodesJob())
//     ->everyThreeDays()
//     ->at('02:00')
//     ->withoutOverlapping();
```

### Setting Up Cron

To enable the scheduler, add this to your server's crontab:

```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

## Configuration Options

### Changing Retention Period

You can modify the default retention period in several ways:

1. **Command line option:**
   ```bash
   php artisan otp:clear-old --days=7
   ```

2. **Job constructor:**
   ```php
   ClearOldOtpCodesJob::dispatch(7);
   ```

3. **Model method:**
   ```php
   OtpCode::clearOldCodes(7);
   ```

### Switching Between Command and Job

In `app/Console/Kernel.php`, you can switch between running the cleanup as a command or as a queued job:

- **Command approach:** Runs immediately when scheduled
- **Job approach:** Dispatches to queue, better for high-load systems

## Monitoring and Logging

All cleanup operations are logged with the following information:
- Number of records deleted
- Cutoff date used
- Retention period
- Any errors encountered

**Log entries example:**
```
[INFO] OTP codes cleanup completed: deleted_count=150, cutoff_date=2024-01-15 02:00:00, days_threshold=3
[ERROR] OTP codes cleanup failed: error=Database connection timeout
```

## Testing

Run the test suite to verify the cleanup system:

```bash
php artisan test tests/Feature/OtpCleanupTest.php
```

**Test coverage includes:**
- Command functionality with different parameters
- Dry-run mode verification
- Job execution
- Model method behavior
- Data integrity (recent records preserved)

## Manual Cleanup

For immediate cleanup or troubleshooting:

```bash
# Check what would be deleted
php artisan otp:clear-old --dry-run

# Perform cleanup
php artisan otp:clear-old

# Clean with custom retention
php artisan otp:clear-old --days=1
```

## Security Considerations

- Phone numbers are masked in dry-run output for privacy
- Production environment requires confirmation before deletion
- All operations are logged for audit purposes
- No sensitive OTP codes are logged

## Troubleshooting

### Common Issues

1. **Scheduler not running:**
   - Verify cron job is set up correctly
   - Check `php artisan schedule:list` for scheduled tasks

2. **Job not processing:**
   - Ensure queue workers are running: `php artisan queue:work`
   - Check failed jobs: `php artisan queue:failed`

3. **Permission errors:**
   - Verify database permissions
   - Check log file write permissions

### Monitoring Commands

```bash
# List scheduled tasks
php artisan schedule:list

# Check queue status
php artisan queue:monitor

# View failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all
```

<?php

namespace Database\Factories;

use Illuminate\Support\Str;
use App\Models\FantasyPlayer;
use Illuminate\Database\Eloquent\Factories\Factory;

class FantasyPlayerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = FantasyPlayer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $isCaptain = fake()->boolean(10); // 10% chance of being captain

        return [
            'is_captain' => $isCaptain,
            'is_vice_captain' => $isCaptain ? false : fake()->boolean(15), // Cannot be both captain and vice-captain
            'purchase_price' => fake()->numberBetween(40, 130), // Represents price like 4.0m, 13.0m
            'fantasy_team_id' => \App\Models\FantasyTeam::factory(),
            'gameweek_id' => \App\Models\Gameweek::factory(),
            'player_id' => \App\Models\Player::factory(),
        ];
    }
}

<?php

namespace Database\Factories;

use Illuminate\Support\Str;
use App\Models\RankingJoinedGameweek;
use Illuminate\Database\Eloquent\Factories\Factory;

class RankingJoinedGameweekFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = RankingJoinedGameweek::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'rank_gameweek' => fake()->numberBetween(1, 10000),
            'rank' => fake()->unique()->numberBetween(1, 10000),
            'points' => fake()->numberBetween(0, 3000),
            'join_gameweek_id' => \App\Models\Gameweek::factory(),
            'gameweek_id' => \App\Models\Gameweek::factory(),
            'fantasy_team_id' => \App\Models\FantasyTeam::factory(),
        ];
    }
}

<?php

namespace Database\Factories;

use App\Models\FantasyTransfer;
use Illuminate\Database\Eloquent\Factories\Factory;

class FantasyTransferFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = FantasyTransfer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'tenant_id' => \App\Models\Tenant::factory(),
            'fantasy_team_id' => \App\Models\FantasyTeam::factory(),
            'player_in_id' => \App\Models\Player::factory(),
            'player_out_id' => \App\Models\Player::factory(),
            'transfer_type' => fake()->randomElement(['in', 'out', 'swap']),
            'is_free_transfer' => fake()->boolean(),
            'transfer_cost' => fake()->randomFloat(2, 0, 100),
            'gameweek_id' => \App\Models\Gameweek::factory(),

        ];
    }
}

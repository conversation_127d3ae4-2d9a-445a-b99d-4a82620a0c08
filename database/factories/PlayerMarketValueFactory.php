<?php

namespace Database\Factories;

use App\Models\Gameweek;
use App\Models\Player;
use App\Models\PlayerMarketValue;
use Illuminate\Database\Eloquent\Factories\Factory;

class PlayerMarketValueFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PlayerMarketValue::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'player_id' => Player::factory(),
            'gameweek_id' => Gameweek::factory(),
            'market_value' => fake()->numberBetween(35, 150),
        ];
    }

    /**
     * Create market value for a specific player and gameweek
     */
    public function forPlayerAndGameweek(Player $player, Gameweek $gameweek): static
    {
        return $this->state([
            'player_id' => $player->id,
            'gameweek_id' => $gameweek->id,
        ]);
    }

    /**
     * Create market value based on player position
     */
    public function forPosition(string $position): static
    {
        $marketValueRanges = [
            'GK' => [40, 120],
            'DEF' => [35, 120],
            'MID' => [45, 130],
            'FWD' => [50, 150],
        ];

        $range = $marketValueRanges[$position] ?? [35, 150];

        return $this->state([
            'market_value' => fake()->numberBetween($range[0], $range[1]),
        ]);
    }
}

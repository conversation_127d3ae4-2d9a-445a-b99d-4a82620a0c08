<?php

namespace Database\Factories;

use App\Models\LineupPlayer;
use App\Models\Lineup;
use App\Models\Player;
use App\Enums\PlayerPosition;
use App\Enums\PlayerStatus;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LineupPlayer>
 */
class LineupPlayerFactory extends Factory
{
    protected $model = LineupPlayer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $status = fake()->randomElement([
            PlayerStatus::STARTING,
            PlayerStatus::SUBSTITUTE,
            PlayerStatus::SUBSTITUTED_IN,
            PlayerStatus::SUBSTITUTED_OUT,
        ]);

        $position = fake()->randomElement(PlayerPosition::cases());
        
        return [
            'lineup_id' => Lineup::factory(),
            'player_id' => Player::factory(),
            'position' => $position,
            'status' => $status,
            'jersey_number' => $this->getJerseyNumber($position),
            'is_captain' => false, // Will be set specifically for one player per team
            'is_vice_captain' => false, // Will be set specifically for one player per team
            'substitution_minute' => $this->getSubstitutionMinute($status),
            'substituted_for' => null, // Will be set when creating substitution relationships
            'live_stats' => $this->generateLiveStats($status),
            'notes' => fake()->optional(0.2)->sentence(),
        ];
    }

    /**
     * Create a starting XI player
     */
    public function starting(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PlayerStatus::STARTING,
            'substitution_minute' => null,
        ]);
    }

    /**
     * Create a substitute player
     */
    public function substitute(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PlayerStatus::SUBSTITUTE,
            'substitution_minute' => null,
        ]);
    }

    /**
     * Create a player who was substituted in
     */
    public function substitutedIn(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PlayerStatus::SUBSTITUTED_IN,
            'substitution_minute' => fake()->numberBetween(46, 85),
        ]);
    }

    /**
     * Create a player who was substituted out
     */
    public function substitutedOut(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PlayerStatus::SUBSTITUTED_OUT,
            'substitution_minute' => fake()->numberBetween(46, 85),
        ]);
    }

    /**
     * Create a captain
     */
    public function captain(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_captain' => true,
            'is_vice_captain' => false,
            'status' => PlayerStatus::STARTING,
        ]);
    }

    /**
     * Create a vice captain
     */
    public function viceCaptain(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_captain' => false,
            'is_vice_captain' => true,
            'status' => PlayerStatus::STARTING,
        ]);
    }

    /**
     * Create player with specific position
     */
    public function position(PlayerPosition $position): static
    {
        return $this->state(fn (array $attributes) => [
            'position' => $position,
            'jersey_number' => $this->getJerseyNumber($position),
        ]);
    }

    /**
     * Create goalkeeper
     */
    public function goalkeeper(): static
    {
        return $this->state(fn (array $attributes) => [
            'position' => PlayerPosition::GOALKEEPER,
            'jersey_number' => fake()->randomElement([1, 12, 13, 21, 22]),
        ]);
    }

    /**
     * Create defender
     */
    public function defender(): static
    {
        return $this->state(fn (array $attributes) => [
            'position' => PlayerPosition::DEFENDER,
            'jersey_number' => fake()->numberBetween(2, 6),
        ]);
    }

    /**
     * Create midfielder
     */
    public function midfielder(): static
    {
        return $this->state(fn (array $attributes) => [
            'position' => PlayerPosition::MIDFIELDER,
            'jersey_number' => fake()->numberBetween(6, 11),
        ]);
    }

    /**
     * Create forward
     */
    public function forward(): static
    {
        return $this->state(fn (array $attributes) => [
            'position' => PlayerPosition::FORWARD,
            'jersey_number' => fake()->numberBetween(7, 11),
        ]);
    }

    /**
     * Create player with live stats (for active matches)
     */
    public function withLiveStats(): static
    {
        return $this->state(fn (array $attributes) => [
            'live_stats' => $this->generateDetailedLiveStats(),
        ]);
    }

    private function getJerseyNumber(PlayerPosition $position): int
    {
        return match ($position) {
            PlayerPosition::GOALKEEPER => fake()->randomElement([1, 12, 13, 21, 22, 23]),
            PlayerPosition::DEFENDER => fake()->numberBetween(2, 6),
            PlayerPosition::MIDFIELDER => fake()->numberBetween(6, 11),
            PlayerPosition::FORWARD => fake()->numberBetween(7, 11),
        };
    }

    private function getSubstitutionMinute(PlayerStatus $status): ?int
    {
        return match ($status) {
            PlayerStatus::SUBSTITUTED_IN, PlayerStatus::SUBSTITUTED_OUT => fake()->numberBetween(46, 85),
            default => null,
        };
    }

    private function generateLiveStats(PlayerStatus $status): array
    {
        if (!$status->isOnField()) {
            return [];
        }

        return [
            'goals' => $this->generateGoals(),
            'assists' => $this->generateAssists(),
            'cards' => $this->generateCards(),
            'saves' => fake()->numberBetween(0, 8), // Mainly for goalkeepers
            'passes_completed' => fake()->numberBetween(10, 80),
            'passes_attempted' => fake()->numberBetween(15, 100),
            'tackles' => fake()->numberBetween(0, 8),
            'interceptions' => fake()->numberBetween(0, 5),
            'shots' => fake()->numberBetween(0, 5),
            'shots_on_target' => fake()->numberBetween(0, 3),
        ];
    }

    private function generateDetailedLiveStats(): array
    {
        return [
            'goals' => $this->generateGoals(),
            'assists' => $this->generateAssists(),
            'cards' => $this->generateCards(),
            'saves' => fake()->numberBetween(0, 12),
            'passes_completed' => fake()->numberBetween(20, 120),
            'passes_attempted' => fake()->numberBetween(25, 150),
            'pass_accuracy' => fake()->numberBetween(70, 95),
            'tackles' => fake()->numberBetween(0, 12),
            'tackles_won' => fake()->numberBetween(0, 8),
            'interceptions' => fake()->numberBetween(0, 8),
            'clearances' => fake()->numberBetween(0, 10),
            'shots' => fake()->numberBetween(0, 8),
            'shots_on_target' => fake()->numberBetween(0, 5),
            'shots_blocked' => fake()->numberBetween(0, 3),
            'crosses' => fake()->numberBetween(0, 8),
            'crosses_accurate' => fake()->numberBetween(0, 5),
            'dribbles_attempted' => fake()->numberBetween(0, 10),
            'dribbles_successful' => fake()->numberBetween(0, 7),
            'fouls_committed' => fake()->numberBetween(0, 4),
            'fouls_suffered' => fake()->numberBetween(0, 4),
            'offsides' => fake()->numberBetween(0, 3),
            'distance_covered' => fake()->numberBetween(8000, 12000), // meters
            'sprints' => fake()->numberBetween(10, 40),
            'duels_won' => fake()->numberBetween(0, 15),
            'duels_lost' => fake()->numberBetween(0, 10),
        ];
    }

    private function generateGoals(): array
    {
        $goalCount = fake()->numberBetween(0, 3);
        $goals = [];

        for ($i = 0; $i < $goalCount; $i++) {
            $goals[] = [
                'minute' => fake()->numberBetween(1, 90),
                'type' => fake()->randomElement(['goal', 'penalty', 'free_kick', 'header']),
                'timestamp' => fake()->dateTimeBetween('-2 hours', 'now'),
            ];
        }

        return $goals;
    }

    private function generateAssists(): array
    {
        $assistCount = fake()->numberBetween(0, 2);
        $assists = [];

        for ($i = 0; $i < $assistCount; $i++) {
            $assists[] = [
                'minute' => fake()->numberBetween(1, 90),
                'type' => fake()->randomElement(['pass', 'cross', 'through_ball']),
                'timestamp' => fake()->dateTimeBetween('-2 hours', 'now'),
            ];
        }

        return $assists;
    }

    private function generateCards(): array
    {
        $cardCount = fake()->numberBetween(0, 2);
        $cards = [];

        for ($i = 0; $i < $cardCount; $i++) {
            $cards[] = [
                'type' => fake()->randomElement(['yellow', 'red']),
                'minute' => fake()->numberBetween(1, 90),
                'reason' => fake()->randomElement([
                    'Unsporting behavior',
                    'Dissent by word or action',
                    'Persistent fouling',
                    'Delaying the restart of play',
                    'Serious foul play',
                    'Violent conduct'
                ]),
                'timestamp' => fake()->dateTimeBetween('-2 hours', 'now'),
            ];
        }

        return $cards;
    }
}

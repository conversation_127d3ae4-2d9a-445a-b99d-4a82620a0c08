<?php

namespace Database\Factories;

use App\Models\Payment;
use Illuminate\Database\Eloquent\Factories\Factory;

class PaymentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Payment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'method' => fake()->randomElement(['phone', 'wallet', 'credit_card']),
            'reference' => 'PAY_'.fake()->unique()->numerify('########'),
            'status' => fake()->randomElement(['success', 'failed', 'pending']),
            'amount' => fake()->randomFloat(2, 5.00, 199.99), // £5.00 to £199.99
            'subscription_id' => \App\Models\Subscription::factory(),
        ];
    }
}

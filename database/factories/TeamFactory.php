<?php

namespace Database\Factories;

use App\Models\Team;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class TeamFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Team::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $teamNames = [
            'Arsenal', 'Chelsea', 'Liverpool', 'Manchester City', 'Manchester United',
            'Tottenham', 'Newcastle', 'Brighton', 'Aston Villa', 'West Ham',
            'Crystal Palace', 'Fulham', 'Wolves', 'Everton', 'Brentford',
            'Nottingham Forest', 'Bournemouth', 'Sheffield United', 'Burnley', 'Luton Town'
        ];
        
        $teamName = fake()->randomElement($teamNames);
        $shortName = $this->generateShortName($teamName);
        $codeName = strtoupper(substr($teamName, 0, 3));
        
        return [
            'name' => $teamName,
            'short_name' => $shortName,
            'code_name' => $codeName,
            'logo' => "/images/teams/logos/{$codeName}.png",
            'shirt' => "/images/teams/shirts/{$codeName}_home.png",
            'gk_shirt' => "/images/teams/shirts/{$codeName}_gk.png",
        ];
    }
    
    private function generateShortName(string $teamName): string
    {
        $shortNames = [
            'Arsenal' => 'ARS',
            'Chelsea' => 'CHE',
            'Liverpool' => 'LIV',
            'Manchester City' => 'MCI',
            'Manchester United' => 'MUN',
            'Tottenham' => 'TOT',
            'Newcastle' => 'NEW',
            'Brighton' => 'BHA',
            'Aston Villa' => 'AVL',
            'West Ham' => 'WHU',
            'Crystal Palace' => 'CRY',
            'Fulham' => 'FUL',
            'Wolves' => 'WOL',
            'Everton' => 'EVE',
            'Brentford' => 'BRE',
            'Nottingham Forest' => 'NFO',
            'Bournemouth' => 'BOU',
            'Sheffield United' => 'SHU',
            'Burnley' => 'BUR',
            'Luton Town' => 'LUT'
        ];
        
        return $shortNames[$teamName] ?? strtoupper(substr($teamName, 0, 3));
    }
}

<?php

namespace Database\Factories;

use App\Models\FantasyTeam;
use App\Models\FantasyTeamGameweek;
use App\Models\Gameweek;
use Illuminate\Database\Eloquent\Factories\Factory;

class FantasyTeamGameweekFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = FantasyTeamGameweek::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'fantasy_team_id' => FantasyTeam::factory(),
            'gameweek_id' => Gameweek::factory(),
            'transfer_cost_penalty' => fake()->randomFloat(2, 0, 20),
            'total_points' => fake()->numberBetween(0, 150),
            'transfers_made' => fake()->numberBetween(0, 5),
            'free_transfers_used' => fake()->numberBetween(0, 2),
            'points_deducted' => fake()->numberBetween(0, 20),
        ];
    }
}

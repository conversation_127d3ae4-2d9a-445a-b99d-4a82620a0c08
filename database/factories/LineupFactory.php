<?php

namespace Database\Factories;

use App\Models\Lineup;
use App\Models\LiveGame;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Lineup>
 */
class LineupFactory extends Factory
{
    protected $model = Lineup::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $formations = [
            '4-4-2', '4-3-3', '3-5-2', '4-5-1', '3-4-3', 
            '5-3-2', '4-2-3-1', '3-4-1-2', '4-1-4-1', '5-4-1'
        ];

        $isConfirmed = fake()->boolean(70); // 70% chance of being confirmed

        return [
            'live_game_id' => LiveGame::factory(),
            'team_id' => Team::factory(),
            'formation' => fake()->randomElement($formations),
            'is_confirmed' => $isConfirmed,
            'confirmed_at' => $isConfirmed ? fake()->dateTimeBetween('-2 hours', 'now') : null,
            'confirmed_by' => $isConfirmed ? User::factory() : null,
            'notes' => fake()->optional(0.3)->sentence(),
        ];
    }

    /**
     * Create a confirmed lineup
     */
    public function confirmed(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_confirmed' => true,
            'confirmed_at' => fake()->dateTimeBetween('-2 hours', 'now'),
            'confirmed_by' => User::factory(),
        ]);
    }

    /**
     * Create an unconfirmed lineup
     */
    public function unconfirmed(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_confirmed' => false,
            'confirmed_at' => null,
            'confirmed_by' => null,
        ]);
    }

    /**
     * Create lineup with specific formation
     */
    public function withFormation(string $formation): static
    {
        return $this->state(fn (array $attributes) => [
            'formation' => $formation,
        ]);
    }

    /**
     * Create lineup for specific team and live game
     */
    public function forTeamAndGame(int $teamId, int $liveGameId): static
    {
        return $this->state(fn (array $attributes) => [
            'team_id' => $teamId,
            'live_game_id' => $liveGameId,
        ]);
    }

    /**
     * Create lineup with notes
     */
    public function withNotes(string $notes): static
    {
        return $this->state(fn (array $attributes) => [
            'notes' => $notes,
        ]);
    }

    /**
     * Create lineup for a specific formation style
     */
    public function defensive(): static
    {
        $defensiveFormations = ['5-4-1', '5-3-2', '4-5-1', '4-1-4-1'];
        
        return $this->state(fn (array $attributes) => [
            'formation' => fake()->randomElement($defensiveFormations),
            'notes' => 'Defensive setup to secure the result',
        ]);
    }

    /**
     * Create lineup for an attacking formation style
     */
    public function attacking(): static
    {
        $attackingFormations = ['4-3-3', '3-4-3', '4-2-3-1', '3-4-1-2'];
        
        return $this->state(fn (array $attributes) => [
            'formation' => fake()->randomElement($attackingFormations),
            'notes' => 'Attacking formation to score goals',
        ]);
    }

    /**
     * Create lineup for a balanced formation style
     */
    public function balanced(): static
    {
        $balancedFormations = ['4-4-2', '3-5-2', '4-3-3'];
        
        return $this->state(fn (array $attributes) => [
            'formation' => fake()->randomElement($balancedFormations),
            'notes' => 'Balanced formation for control',
        ]);
    }
}

<?php

namespace Database\Factories;

use App\Models\Season;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class SeasonFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Season::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Generate realistic season years
        $startYear = fake()->numberBetween(2020, 2025);
        $endYear = $startYear + 1;
        $seasonName = "{$startYear}/{$endYear}";
        
        // Generate realistic season dates (August to May)
        $startDate = fake()->dateTimeBetween("{$startYear}-08-01", "{$startYear}-09-30");
        $endDate = fake()->dateTimeBetween("{$endYear}-05-01", "{$endYear}-06-30");
        
        return [
            'name' => $seasonName,
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'competition_id' => \App\Models\Competition::factory(),
        ];
    }
}

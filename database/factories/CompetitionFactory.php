<?php

namespace Database\Factories;

use App\Models\Competition;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompetitionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Competition::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->word(); // Fallback if not overridden
        $slug = strtolower(str_replace(' ', '-', $name));

        // Determine competition type
        $type = 'league';
        if (str_contains($name, 'Cup') || str_contains($name, 'Shield')) {
            $type = 'cup';
        } elseif (str_contains($name, 'UEFA') || str_contains($name, 'Champions')) {
            $type = 'mixed';
        }

        return [
            'name' => $name,
            'slug' => $slug,
            'logo' => "/images/competitions/{$slug}.png",
            'type' => $type,
            'status' => fake()->randomElement(['upcoming', 'ongoing', 'completed', 'archived']),
            'current_season_id' => null,
        ];
    }

    public function withName(string $name)
    {
        return $this->state(function () use ($name) {
            $slug = strtolower(str_replace(' ', '-', $name));
            $type = 'league';
            if (str_contains($name, 'Cup') || str_contains($name, 'Shield')) {
                $type = 'cup';
            } elseif (str_contains($name, 'UEFA') || str_contains($name, 'Champions')) {
                $type = 'mixed';
            }

            return [
                'name' => $name,
                'slug' => $slug,
                'logo' => "/images/competitions/{$slug}.png",
                'type' => $type,
            ];
        });
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function configure()
    {
        return $this->afterCreating(function (\App\Models\Competition $competition) {
            if ($competition->current_season_id === null) {
                $season = \App\Models\Season::factory()->create([
                    'competition_id' => $competition->id,
                ]);
                $competition->current_season_id = $season->id;
                $competition->save();
            }
        });
    }
}

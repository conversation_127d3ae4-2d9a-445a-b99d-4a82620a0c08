<?php

namespace Database\Factories;

use App\Models\Referee;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Referee>
 */
class RefereeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Referee::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'name_ar' => $this->faker->optional()->name(),
            'birthday' => $this->faker->optional()->date('Y-m-d', '-25 years'),
            'country' => $this->faker->optional()->country(),
            'image' => $this->faker->optional()->imageUrl(200, 200, 'people'),
            'experience_years' => $this->faker->numberBetween(0, 25),
        ];
    }

    /**
     * Indicate that the referee is experienced (10+ years).
     */
    public function experienced(): static
    {
        return $this->state(fn (array $attributes) => [
            'experience_years' => $this->faker->numberBetween(10, 25),
        ]);
    }

    /**
     * Indicate that the referee is a beginner (0-3 years).
     */
    public function beginner(): static
    {
        return $this->state(fn (array $attributes) => [
            'experience_years' => $this->faker->numberBetween(0, 3),
        ]);
    }

    /**
     * Indicate that the referee is from a specific country.
     */
    public function fromCountry(string $country): static
    {
        return $this->state(fn (array $attributes) => [
            'country' => $country,
        ]);
    }
}

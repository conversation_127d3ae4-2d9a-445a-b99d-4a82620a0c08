<?php

namespace Database\Factories;

use App\Models\SeasonPhase;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class SeasonPhaseFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SeasonPhase::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $phaseNames = [
            'Regular Season',
            'Playoffs',
            'Championship',
            'Group Stage',
            'Knockout Stage',
            'Quarter Finals',
            'Semi Finals',
            'Final'
        ];
        
        $phaseName = fake()->randomElement($phaseNames);
        $teamsCount = fake()->numberBetween(8, 20);
        
        return [
            'name' => $phaseName,
            'format' => fake()->randomElement(['league', 'cup']),
            'status' => fake()->randomElement(['upcoming', 'ongoing', 'completed']),
            'config' => json_encode([
                'matches_per_team' => fake()->numberBetween(1, 38),
                'points_for_win' => 3,
                'points_for_draw' => 1,
                'points_for_loss' => 0
            ]),
            'teams_count' => $teamsCount,
            'season_id' => \App\Models\Season::factory(),
        ];
    }
}

<?php

namespace Database\Factories;

use App\Models\FantasyTeam;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class FantasyTeamFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = FantasyTeam::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $fantasyTeamNames = [
            'Dream Team FC', 'Goal Getters', 'Fantasy Legends', 'The Chosen Ones',
            'Victory Squad', 'Elite Eleven', 'Champions United', 'Star Strikers',
            'Magic Midfield', 'Defensive Dynasty', 'Goal Rush', 'Premier Picks',
            'Fantasy Force', 'Ultimate XI', 'Power Players', 'Tactical Titans',
            'Score Seekers', 'Match Winners', 'Field Masters', 'Game Changers'
        ];
        
        $shirtColors = [
            '#FF0000', '#0000FF', '#00FF00', '#FFFF00', '#FF00FF', '#00FFFF',
            '#800080', '#FFA500', '#000000', '#FFFFFF', '#808080', '#800000',
            '#008000', '#000080', '#808000', '#800080'
        ];
        
        $stripColors = [
            '#FFFFFF', '#000000', '#FFFF00', '#FF0000', '#0000FF', '#00FF00',
            '#FFA500', '#800080', '#808080', '#800000', '#008000', '#000080'
        ];
        
        return [
            'name' => fake()->randomElement($fantasyTeamNames),
            'avatar' => '/images/avatars/team_' . fake()->numberBetween(1, 20) . '.png',
            'shirt_type' => fake()->randomElement(['striped', 'filled']),
            'shirt_color' => fake()->randomElement($shirtColors),
            'strip_color' => fake()->randomElement($stripColors),
            'budget' => fake()->numberBetween(800, 1000), // £80.0m - £100.0m budget
            'total_points' => fake()->numberBetween(0, 2500),
            'user_id' => \App\Models\User::factory(),
            'season_id' => \App\Models\Season::factory(),
        ];
    }
}

<?php

namespace Database\Factories;

use App\Models\Booster;
use Illuminate\Database\Eloquent\Factories\Factory;

class BoosterFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Booster::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $boosters = [
            [
                'title' => 'Double Captain Points',
                'effect' => 'double_captain_points',
                'description' => 'Your captain earns double points this gameweek. A smart play when your star player faces a weak team.',
                'price' => 500,
                'image' => 'boosters/double-captain.png',
            ],
            [
                'title' => 'Extra Transfer',
                'effect' => 'extra_transfer',
                'description' => 'Grants you one additional free transfer for the current gameweek. Perfect for injury crises or fixture swings.',
                'price' => 300,
                'image' => 'boosters/extra-transfer.png',
            ],
            [
                'title' => 'Bench Boost',
                'effect' => 'bench_boost',
                'description' => 'All points from your bench players will be counted this week. Use it when your entire squad is in good form.',
                'price' => 450,
                'image' => 'boosters/bench-boost.png',
            ],
            [
                'title' => 'Triple Captain',
                'effect' => 'triple_captain',
                'description' => 'Your captain scores triple points this gameweek. Save it for a double-fixture gameweek!',
                'price' => 600,
                'image' => 'boosters/triple-captain.png',
            ],
            [
                'title' => 'Wildcard',
                'effect' => 'wildcard',
                'description' => 'Make unlimited transfers this week without any point deductions. Reshape your team entirely.',
                'price' => 700,
                'image' => 'boosters/wildcard.png',
            ],
            [
                'title' => 'Injury Shield',
                'effect' => 'injury_shield',
                'description' => 'If up to 3 of your players miss the match due to injury, they’ll be automatically replaced by bench players.',
                'price' => 400,
                'image' => 'boosters/injury-shield.png',
            ],
            [
                'title' => 'Budget Boost',
                'effect' => 'budget_boost',
                'description' => 'Temporarily increases your squad budget by 2.5M for the next transfer. Use it wisely!',
                'price' => 350,
                'image' => 'boosters/budget-boost.png',
            ],
            [
                'title' => 'Opponent Score Halved',
                'effect' => 'opponent_score_halved',
                'description' => 'In a head-to-head match, your opponent’s final score will be halved. Brutal but effective.',
                'price' => 800,
                'image' => 'boosters/opponent-halved.png',
            ],
        ];

        $booster = fake()->randomElement($boosters);

        return [
            'title' => $booster['title'],
            'description' => $booster['description'],
            'effect' => $booster['effect'],
            'price' => $booster['price'],
            'image' => $booster['image'],
            'tenant_id' => \App\Models\Tenant::factory(),
        ];
    }
}

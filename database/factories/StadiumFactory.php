<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Stadium>
 */
class StadiumFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $stadiumNames = [
            'Emirates Stadium', 'Old Trafford', 'Anfield', 'Etihad Stadium', 'Stamford Bridge',
            'Tottenham Hotspur Stadium', 'London Stadium', 'Villa Park', 'St. James\' Park',
            'Goodison Park', 'Selhurst Park', 'Craven Cottage', 'Molineux Stadium',
            'Amex Stadium', 'Vitality Stadium', 'Bramall Lane', 'Turf Moor', 'Kenilworth Road',
        ];

        $cities = [
            'London', 'Manchester', 'Liverpool', 'Birmingham', 'Newcastle', 'Brighton',
            'Bournemouth', 'Sheffield', 'Burnley', 'Luton', 'Wolverhampton',
        ];

        $stadiumName = fake()->randomElement($stadiumNames);
        $city = fake()->randomElement($cities);

        return [
            'name' => $stadiumName,
            'location' => null, // Point data would need special handling
            'city' => $city,
            'country' => 'England',
            'capacity' => fake()->numberBetween(20000, 80000),
            'year_opened' => fake()->numberBetween(1900, 2023),
            'covered' => fake()->boolean(80), // 80% chance of being covered
            'image_url' => '/images/stadiums/'.strtolower(str_replace([' ', '\''], ['_', ''], $stadiumName)).'.jpg',
        ];
    }
}

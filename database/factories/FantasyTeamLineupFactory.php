<?php

namespace Database\Factories;

use Illuminate\Support\Str;
use App\Models\FantasyTeamLineup;
use Illuminate\Database\Eloquent\Factories\Factory;

class FantasyTeamLineupFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = FantasyTeamLineup::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $position = fake()->randomElement(['starting', 'bench']);

        return [
            'position' => $position,
            'sub_priority' => $position === 'bench' ? fake()->numberBetween(1, 4) : 0,
            'fantasy_player_id' => \App\Models\FantasyPlayer::factory(),
        ];
    }
}

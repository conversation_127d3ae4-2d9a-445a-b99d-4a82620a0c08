<?php

namespace Database\Factories;

use App\Models\Player;
use App\Models\PlayerMarketValue;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class PlayerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Player::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $positions = ['GK', 'DEF', 'MID', 'FWD'];
        $position = fake()->randomElement($positions);
        
        // Generate realistic age (18-38 years old)
        $age = fake()->numberBetween(18, 38);
        $birthday = now()->subYears($age)->subDays(fake()->numberBetween(0, 365));
        
        $firstName = fake()->firstName();
        $lastName = fake()->lastName();
        $fullName = $firstName . ' ' . $lastName;
        
        return [
            'name' => $fullName,
            'birthday' => $birthday->format('Y-m-d'),
            'country' => fake()->randomElement([
                'England', 'Spain', 'France', 'Germany', 'Italy', 'Brazil', 'Argentina',
                'Portugal', 'Netherlands', 'Belgium', 'Croatia', 'Poland', 'Ukraine',
                'Serbia', 'Denmark', 'Sweden', 'Norway', 'Scotland', 'Wales', 'Ireland'
            ]),
            'position' => $position,
            'image' => "/images/players/" . strtolower(str_replace(' ', '_', $fullName)) . ".jpg",
        ];
    }

    /**
     * Create a player with market values for specific gameweeks
     */
    public function withMarketValues(array $gameweekIds = []): static
    {
        return $this->afterCreating(function (Player $player) use ($gameweekIds) {
            if (empty($gameweekIds)) {
                // If no specific gameweeks provided, create for current gameweek
                $gameweekIds = [1]; // Default to gameweek 1
            }
            
            foreach ($gameweekIds as $gameweekId) {
                PlayerMarketValue::factory()
                    ->forPlayerAndGameweek($player, \App\Models\Gameweek::find($gameweekId))
                    ->forPosition($player->position->value)
                    ->create();
            }
        });
    }
}

<?php

namespace Database\Factories;

use Illuminate\Support\Str;
use App\Models\FantasyPoint;
use Illuminate\Database\Eloquent\Factories\Factory;

class FantasyPointFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = FantasyPoint::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Generate realistic fantasy points based on typical scoring system
        // Goals: 4-6 points, Assists: 3 points, Clean sheets: 4 points, etc.
        // Typical range: -2 to 15 points per game
        $points = fake()->numberBetween(-2, 15);
        
        return [
            'points' => $points,
            'game_id' => \App\Models\Game::factory(),
            'player_id' => \App\Models\Player::factory(),
        ];
    }
}

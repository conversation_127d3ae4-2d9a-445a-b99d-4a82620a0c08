<?php

namespace Database\Factories;

use App\Models\League;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class LeagueFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = League::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = fake()->randomElement(['public', 'private']);

        return [
            'name' => fake()->name(),
            'type' => $type,
            'invite_code' => $type === 'private' ? Str::random(8) : null,
            'season_id' => \App\Models\Season::factory(),
            'owner_id' => \App\Models\User::factory(),
        ];
    }
}

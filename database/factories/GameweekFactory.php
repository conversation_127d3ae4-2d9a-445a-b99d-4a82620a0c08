<?php

namespace Database\Factories;

use App\Models\Gameweek;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class GameweekFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Gameweek::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Generate realistic gameweek numbers (1-38 for Premier League)
        $gameweekNumber = fake()->numberBetween(1, 38);
        $gameweekName = "Gameweek {$gameweekNumber}";
        
        // Generate realistic gameweek dates (typically Friday to Monday)
        $startDate = fake()->dateTimeBetween('-6 months', '+6 months');
        $endDate = clone $startDate;
        $endDate->modify('+3 days'); // Gameweek spans 3-4 days
        
        return [
            'name' => $gameweekName,
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'rules' => json_encode([
                'transfer_deadline' => $startDate->format('Y-m-d H:i:s'),
                'max_transfers' => fake()->numberBetween(1, 5),
                'wildcard_available' => fake()->boolean(20) // 20% chance
            ]),
            'status' => fake()->randomElement(['upcoming', 'ongoing', 'completed']),
            'season_phase_id' => \App\Models\SeasonPhase::factory(),
        ];
    }
}

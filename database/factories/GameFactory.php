<?php

namespace Database\Factories;

use App\Models\Game;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class GameFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Game::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Generate realistic match date (within current season)
        $gameDate = fake()->dateTimeBetween('-6 months', '+6 months');
        
        // Generate realistic football scores (0-5 goals per team)
        $homeScore = fake()->numberBetween(0, 5);
        $awayScore = fake()->numberBetween(0, 5);
        
        $status = $gameDate < now() ? 'finished' : 'scheduled';

        return [
            'game_date' => $gameDate->format('Y-m-d H:i:s'),
            'home_score' => $status === 'finished' ? $homeScore : null,
            'away_score' => $status === 'finished' ? $awayScore : null,
            'status' => $status,
            'gameweek_id' => \App\Models\Gameweek::factory(),
            'home_team_id' => \App\Models\Team::factory(),
            'away_team_id' => \App\Models\Team::factory(),
        ];
    }
}

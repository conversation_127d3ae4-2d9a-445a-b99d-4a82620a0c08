<?php

namespace Database\Factories;

use Illuminate\Support\Str;
use App\Models\PlayerPerformance;
use Illuminate\Database\Eloquent\Factories\Factory;

class PlayerPerformanceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PlayerPerformance::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Generate realistic minutes played (0-90 for outfield, 0-90 for GK)
        $minutesPlayed = fake()->numberBetween(0, 90);
        
        // If player didn't play, most stats should be 0
        if ($minutesPlayed === 0) {
            return [
                'minutes_played' => 0,
                'goals_scored' => 0,
                'assists' => 0,
                'clean_sheet' => 0,
                'goals_conceded' => 0,
                'own_goals' => 0,
                'penalities_saved' => 0,
                'penalities_missed' => 0,
                'penalties_caused' => 0,
                'penalties_committed' => 0,
                'saves' => 0,
                'yellow_cards' => 0,
                'red_cards' => 0,
                'game_id' => \App\Models\Game::factory(),
                'player_id' => \App\Models\Player::factory(),
                'team_id' => \App\Models\Team::factory(),
            ];
        }
        
        // Generate realistic performance stats
        $goalsScored = fake()->numberBetween(0, 3); // 0-3 goals per game
        $assists = fake()->numberBetween(0, 2); // 0-2 assists per game
        $yellowCards = fake()->numberBetween(0, 1); // 0-1 yellow cards
        $redCards = $yellowCards > 0 ? fake()->numberBetween(0, 1) : 0; // Red card only if yellow exists
        
        // Clean sheet is binary (0 or 1)
        $cleanSheet = fake()->numberBetween(0, 1);
        $goalsConceeded = $cleanSheet ? 0 : fake()->numberBetween(1, 4);
        
        return [
            'minutes_played' => $minutesPlayed,
            'goals_scored' => $goalsScored,
            'assists' => $assists,
            'clean_sheet' => $cleanSheet,
            'goals_conceded' => $goalsConceeded,
            'own_goals' => fake()->numberBetween(0, 1), // Rare occurrence
            'penalities_saved' => fake()->numberBetween(0, 1), // For goalkeepers mainly
            'penalities_missed' => fake()->numberBetween(0, 1), // Rare occurrence
            'penalties_caused' => fake()->numberBetween(0, 1), // Fouls leading to penalties
            'penalties_committed' => fake()->numberBetween(0, 1), // Committing penalty fouls
            'saves' => fake()->numberBetween(0, 8), // For goalkeepers mainly
            'yellow_cards' => $yellowCards,
            'red_cards' => $redCards,
            'game_id' => \App\Models\Game::factory(),
            'player_id' => \App\Models\Player::factory(),
            'team_id' => \App\Models\Team::factory(),
        ];
    }
}

<?php

namespace Database\Factories;

use Illuminate\Support\Str;
use App\Models\RankingGlobal;
use Illuminate\Database\Eloquent\Factories\Factory;

class RankingGlobalFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = RankingGlobal::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'rank' => fake()->unique()->numberBetween(1, 100000),
            'rank_gameweek' => fake()->numberBetween(1, 100000),
            'point' => fake()->numberBetween(0, 3000),
            'fantasy_team_id' => \App\Models\FantasyTeam::factory(),
            'gameweek_id' => \App\Models\Gameweek::factory(),
        ];
    }
}

<?php

namespace Database\Factories;

use App\Models\LiveGame;
use App\Models\Game;
use App\Enums\LiveGameStatus;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LiveGame>
 */
class LiveGameFactory extends Factory
{
    protected $model = LiveGame::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $status = fake()->randomElement(LiveGameStatus::cases());
        $currentMinute = $this->getCurrentMinuteForStatus($status);
        
        return [
            'game_id' => Game::factory(),
            'status' => $status,
            'current_minute' => $currentMinute,
            'added_time' => $status->isActive() ? fake()->numberBetween(0, 5) : 0,
            'kick_off_time' => $this->getKickOffTime($status),
            'half_time_start' => $this->getHalfTimeStart($status),
            'half_time_end' => $this->getHalfTimeEnd($status),
            'full_time' => $this->getFullTime($status),
            'events' => $this->generateEvents($status, $currentMinute),
            'statistics' => $this->generateStatistics(),
            'notes' => fake()->optional(0.3)->sentence(),
        ];
    }

    /**
     * Create a pre-match live game
     */
    public function preMatch(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => LiveGameStatus::PRE_MATCH,
            'current_minute' => 0,
            'added_time' => 0,
            'kick_off_time' => null,
            'half_time_start' => null,
            'half_time_end' => null,
            'full_time' => null,
            'events' => [],
        ]);
    }

    /**
     * Create a live game in progress
     */
    public function live(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => LiveGameStatus::LIVE,
            'current_minute' => fake()->numberBetween(1, 45),
            'added_time' => fake()->numberBetween(0, 3),
            'kick_off_time' => now()->subMinutes(fake()->numberBetween(1, 45)),
            'half_time_start' => null,
            'half_time_end' => null,
            'full_time' => null,
        ]);
    }

    /**
     * Create a finished live game
     */
    public function finished(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => LiveGameStatus::FINISHED,
            'current_minute' => 90,
            'added_time' => fake()->numberBetween(3, 7),
            'kick_off_time' => now()->subMinutes(120),
            'half_time_start' => now()->subMinutes(75),
            'half_time_end' => now()->subMinutes(60),
            'full_time' => now()->subMinutes(15),
        ]);
    }

    private function getCurrentMinuteForStatus(LiveGameStatus $status): int
    {
        return match ($status) {
            LiveGameStatus::PRE_MATCH => 0,
            LiveGameStatus::LIVE => fake()->numberBetween(1, 45),
            LiveGameStatus::HALF_TIME => 45,
            LiveGameStatus::SECOND_HALF => fake()->numberBetween(46, 90),
            LiveGameStatus::FULL_TIME, LiveGameStatus::FINISHED => 90,
            LiveGameStatus::EXTRA_TIME => fake()->numberBetween(91, 120),
            default => fake()->numberBetween(0, 90),
        };
    }

    private function getKickOffTime(LiveGameStatus $status): ?\Carbon\Carbon
    {
        if ($status === LiveGameStatus::PRE_MATCH) {
            return null;
        }
        return now()->subMinutes(fake()->numberBetween(10, 120));
    }

    private function getHalfTimeStart(LiveGameStatus $status): ?\Carbon\Carbon
    {
        if (in_array($status, [LiveGameStatus::PRE_MATCH, LiveGameStatus::LIVE])) {
            return null;
        }
        return now()->subMinutes(fake()->numberBetween(30, 90));
    }

    private function getHalfTimeEnd(LiveGameStatus $status): ?\Carbon\Carbon
    {
        if (!in_array($status, [LiveGameStatus::SECOND_HALF, LiveGameStatus::FULL_TIME, LiveGameStatus::FINISHED])) {
            return null;
        }
        return now()->subMinutes(fake()->numberBetween(15, 60));
    }

    private function getFullTime(LiveGameStatus $status): ?\Carbon\Carbon
    {
        if (!$status->isCompleted()) {
            return null;
        }
        return now()->subMinutes(fake()->numberBetween(5, 30));
    }

    private function generateEvents(LiveGameStatus $status, int $currentMinute): array
    {
        if ($status === LiveGameStatus::PRE_MATCH) {
            return [];
        }

        $events = [
            [
                'type' => 'kick_off',
                'minute' => 0,
                'description' => 'Match started',
                'team_id' => null,
                'player_id' => null,
                'timestamp' => now()->subMinutes($currentMinute),
            ]
        ];

        // Add some random events
        $eventCount = fake()->numberBetween(0, min(8, $currentMinute / 10));
        for ($i = 0; $i < $eventCount; $i++) {
            $minute = fake()->numberBetween(1, $currentMinute);
            $eventType = fake()->randomElement(['goal', 'card', 'substitution']);
            
            $events[] = [
                'type' => $eventType,
                'minute' => $minute,
                'description' => $this->getEventDescription($eventType),
                'team_id' => fake()->numberBetween(1, 20), // Assuming team IDs 1-20
                'player_id' => fake()->numberBetween(1, 500), // Assuming player IDs 1-500
                'timestamp' => now()->subMinutes($currentMinute - $minute),
            ];
        }

        // Sort events by minute
        usort($events, fn($a, $b) => $a['minute'] <=> $b['minute']);

        return $events;
    }

    private function generateStatistics(): array
    {
        return [
            'home_possession' => fake()->numberBetween(35, 65),
            'away_possession' => fake()->numberBetween(35, 65),
            'home_shots' => fake()->numberBetween(0, 15),
            'away_shots' => fake()->numberBetween(0, 15),
            'home_shots_on_target' => fake()->numberBetween(0, 8),
            'away_shots_on_target' => fake()->numberBetween(0, 8),
            'home_corners' => fake()->numberBetween(0, 10),
            'away_corners' => fake()->numberBetween(0, 10),
            'home_fouls' => fake()->numberBetween(0, 20),
            'away_fouls' => fake()->numberBetween(0, 20),
            'home_yellow_cards' => fake()->numberBetween(0, 5),
            'away_yellow_cards' => fake()->numberBetween(0, 5),
            'home_red_cards' => fake()->numberBetween(0, 2),
            'away_red_cards' => fake()->numberBetween(0, 2),
        ];
    }

    private function getEventDescription(string $eventType): string
    {
        return match ($eventType) {
            'goal' => fake()->name() . ' scored',
            'card' => fake()->name() . ' received ' . fake()->randomElement(['yellow', 'red']) . ' card',
            'substitution' => fake()->name() . ' substituted in for ' . fake()->name(),
            default => 'Event occurred',
        };
    }
}

<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('player_performances', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('game_id')->unsigned();
            $table->bigInteger('player_id')->unsigned();
            $table->tinyInteger('minutes_played')->unsigned();
            $table->tinyInteger('goals_scored')->unsigned();
            $table->tinyInteger('assists')->unsigned();
            $table->boolean('clean_sheet')->unsigned();
            $table->tinyInteger('goals_conceded')->unsigned();
            $table->tinyInteger('own_goals')->unsigned();
            $table->tinyInteger('penalities_saved')->unsigned();
            $table->tinyInteger('penalities_missed')->unsigned();
            $table->tinyInteger('penalties_caused')->unsigned();
            $table->tinyInteger('penalties_committed')->unsigned();
            $table->tinyInteger('saves')->unsigned();
            $table->tinyInteger('yellow_cards');
            $table->tinyInteger('red_cards')->unsigned();
            $table->bigInteger('team_id')->unsigned();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();

            $table
                ->foreign('game_id')
                ->references('id')
                ->on('games')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table
                ->foreign('player_id')
                ->references('id')
                ->on('players')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table
                ->foreign('team_id')
                ->references('id')
                ->on('teams')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('player_performances');
    }
};

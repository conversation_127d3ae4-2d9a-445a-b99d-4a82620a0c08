<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fantasy_team_lineups', function (Blueprint $table) {
            $table->bigInteger('tenant_id')->unsigned()->nullable()->after('id');
        });

        if (DB::table('tenants')->count() > 0) {
            $defaultTenantId = DB::table('tenants')->first()->id;
            DB::table('fantasy_team_lineups')->update(['tenant_id' => $defaultTenantId]);
        }

        Schema::table('fantasy_team_lineups', function (Blueprint $table) {
            $table->bigInteger('tenant_id')->unsigned()->nullable(false)->change();
            $table->foreign('tenant_id')
                ->references('id')
                ->on('tenants')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fantasy_team_lineups', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
            $table->dropColumn('tenant_id');
        });
    }
};

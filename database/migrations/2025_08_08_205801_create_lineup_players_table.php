<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lineup_players', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lineup_id')->constrained('lineups')->onDelete('cascade');
            $table->foreignId('player_id')->constrained('players')->onDelete('cascade');
            $table->string('position'); // LineupPosition enum
            $table->string('status')->default('starting'); // PlayerStatus enum
            $table->integer('jersey_number')->nullable();
            $table->boolean('is_captain')->default(false);
            $table->boolean('is_vice_captain')->default(false);
            $table->integer('substitution_minute')->nullable(); // When player was substituted
            $table->foreignId('substituted_for')->nullable()->constrained('lineup_players')->onDelete('set null'); // Player they replaced
            $table->json('live_stats')->nullable(); // Real-time stats during the match
            $table->text('notes')->nullable(); // Player-specific notes
            $table->timestamps();

            // Ensure unique player per lineup
            $table->unique(['lineup_id', 'player_id']);

            // Ensure only one captain per lineup
            $table->index(['lineup_id', 'is_captain']);
            $table->index(['lineup_id', 'is_vice_captain']);

            // Indexes for performance
            $table->index(['lineup_id', 'position']);
            $table->index(['lineup_id', 'status']);
            $table->index('jersey_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lineup_players');
    }
};

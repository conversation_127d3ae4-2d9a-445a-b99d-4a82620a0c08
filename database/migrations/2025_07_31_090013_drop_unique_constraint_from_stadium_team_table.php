<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('stadium_team', function (Blueprint $table) {
            $table->dropUnique('stadium_team_team_id_is_home_unique'); // adjust the constraint name if different
        });
    }

    public function down(): void
    {
        Schema::table('stadium_team', function (Blueprint $table) {
            $table->unique(['team_id', 'is_home']);
        });
    }
};

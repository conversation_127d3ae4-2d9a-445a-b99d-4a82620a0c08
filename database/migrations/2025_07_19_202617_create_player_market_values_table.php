<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('player_market_values', function (Blueprint $table) {
            $table->id();
            $table->foreignId('player_id')->constrained('players')->onDelete('cascade');
            $table->foreignId('gameweek_id')->constrained('gameweeks')->onDelete('cascade');
            $table->tinyInteger('market_value')->unsigned();
            $table->timestamps();
            
            // Ensure unique combination of player and gameweek
            $table->unique(['player_id', 'gameweek_id']);
            
            // Index for efficient queries
            $table->index(['player_id', 'gameweek_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('player_market_values');
    }
};

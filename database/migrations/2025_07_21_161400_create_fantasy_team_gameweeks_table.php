<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fantasy_team_gameweeks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('fantasy_team_id')->constrained()->onDelete('cascade');
            $table->foreignId('gameweek_id')->constrained()->onDelete('cascade');
            $table->decimal('transfer_cost_penalty', 10, 2)->default(0);
            $table->integer('total_points')->default(0);
            $table->integer('transfers_made')->default(0);
            $table->integer('free_transfers_used')->default(0);
            $table->integer('points_deducted')->default(0);
            $table->timestamps();

            $table->unique(['fantasy_team_id', 'gameweek_id']);
            $table->index(['fantasy_team_id', 'gameweek_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fantasy_team_gameweeks');
    }
};

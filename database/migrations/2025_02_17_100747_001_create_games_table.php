<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('games', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('gameweek_id')->unsigned();
            $table->bigInteger('home_team_id')->unsigned();
            $table->bigInteger('away_team_id')->unsigned();
            $table->dateTime('game_date')->nullable();
            $table
                ->tinyInteger('home_score')
                ->unsigned()
                ->nullable();
            $table
                ->tinyInteger('away_score')
                ->unsigned()
                ->nullable();
            $table->enum('status', [
                'scheduled',
                'in_progress',
                'finished',
                'postponed',
                'canceled',
            ]);
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();

            $table
                ->foreign('gameweek_id')
                ->references('id')
                ->on('gameweeks')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table
                ->foreign('home_team_id')
                ->references('id')
                ->on('teams')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table
                ->foreign('away_team_id')
                ->references('id')
                ->on('teams')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('games');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ranking_league', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('league_id')->unsigned();
            $table->bigInteger('gameweek_id')->unsigned();
            $table->bigInteger('fantasy_team_id')->unsigned();
            $table->integer('rank_gameweek')->unsigned();
            $table->integer('rank')->unsigned();
            $table->integer('points_gameweek');
            $table->integer('points');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();

            $table
                ->foreign('league_id')
                ->references('id')
                ->on('leagues')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table
                ->foreign('gameweek_id')
                ->references('id')
                ->on('gameweeks')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table
                ->foreign('fantasy_team_id')
                ->references('id')
                ->on('fantasy_teams')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ranking_league');
    }
};

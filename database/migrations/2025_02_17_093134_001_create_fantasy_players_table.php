<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fantasy_players', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('fantasy_team_id')->unsigned();
            $table->bigInteger('gameweek_id')->unsigned();
            $table->boolean('is_captain');
            $table->boolean('is_vice_captain');
            $table->tinyInteger('purchase_price')->unsigned();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();

            $table
                ->foreign('fantasy_team_id')
                ->references('id')
                ->on('fantasy_teams')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table
                ->foreign('gameweek_id')
                ->references('id')
                ->on('gameweeks')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fantasy_players');
    }
};

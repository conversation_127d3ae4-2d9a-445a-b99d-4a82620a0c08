<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ranking_global', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('fantasy_team_id')->unsigned();
            $table->bigInteger('gameweek_id')->unsigned();
            $table
                ->integer('rank')
                ->unsigned();
            $table->integer('point');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();

            $table
                ->foreign('fantasy_team_id')
                ->references('id')
                ->on('fantasy_teams')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table
                ->foreign('gameweek_id')
                ->references('id')
                ->on('gameweeks')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ranking_global');
    }
};

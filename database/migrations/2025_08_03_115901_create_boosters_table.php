<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('boosters', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('description');
            $table->enum('effect', [
                'double_captain_points',
                'extra_transfer',
                'bench_boost',
                'triple_captain',
                'wildcard',
                'injury_shield',
                'budget_boost',
                'opponent_score_halved',
            ]);
            $table->integer('price');
            $table->string('image')->nullable();
            $table->foreignId('tenant_id')->constrained('tenants')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('boosters');
    }
};

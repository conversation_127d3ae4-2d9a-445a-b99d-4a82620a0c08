<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('last_name')->after('first_name');
            $table->string('phone')->after('last_name');
            $table
                ->string('email', 255)
                ->nullable()
                ->change();
            $table->renameColumn('name', 'first_name');
            $table->dropColumn('password');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('last_name');
            $table->dropColumn('phone');
            $table
                ->string('email', 255)
                ->unique()
                ->change();
            $table->renameColumn('first_name', 'name');
            $table->string('password', 255)->after('phone');
        });
    }
};

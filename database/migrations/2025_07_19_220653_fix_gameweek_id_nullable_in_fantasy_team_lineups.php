<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fantasy_team_lineups', function (Blueprint $table) {
            // Make gameweek_id nullable to fix NOT NULL constraint violation
            $table->bigInteger('gameweek_id')->unsigned()->nullable()->change();
        });

        // Update existing records to have a default gameweek_id
        // Get the first available gameweek ID as default
        $firstGameweek = \App\Models\Gameweek::first();
        if ($firstGameweek) {
            \DB::table('fantasy_team_lineups')
                ->whereNull('gameweek_id')
                ->update(['gameweek_id' => $firstGameweek->id]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fantasy_team_lineups', function (Blueprint $table) {
            // Revert back to NOT NULL (this might fail if there are NULL values)
            $table->bigInteger('gameweek_id')->unsigned()->change();
        });
    }
};

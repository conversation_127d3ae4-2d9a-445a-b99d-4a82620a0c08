<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('fantasy_players', function (Blueprint $table) {
            $table
                ->bigInteger('player_id')
                ->unsigned()
                ->after('purchase_price');
            $table
                ->foreign('player_id')
                ->references('id')
                ->on('players')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('fantasy_players', function (Blueprint $table) {
            $table->dropColumn('player_id');
            $table->dropForeign('fantasy_players_player_id_foreign');
        });
    }
};

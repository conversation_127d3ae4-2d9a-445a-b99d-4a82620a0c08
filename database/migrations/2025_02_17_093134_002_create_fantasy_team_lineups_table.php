<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fantasy_team_lineups', function (Blueprint $table) {
            $table->id();
            $table->enum('position', ['starting', 'bench']);
            $table->tinyInteger('sub_priority')->unsigned();
            $table->bigInteger('fantasy_player_id')->unsigned();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();

            $table
                ->foreign('fantasy_player_id')
                ->references('id')
                ->on('fantasy_players')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fantasy_team_lineups');
    }
};

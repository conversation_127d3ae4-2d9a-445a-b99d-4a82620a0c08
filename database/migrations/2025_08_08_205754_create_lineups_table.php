<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lineups', function (Blueprint $table) {
            $table->id();
            $table->foreignId('live_game_id')->constrained('live_games')->onDelete('cascade');
            $table->foreignId('team_id')->constrained('teams')->onDelete('cascade');
            $table->string('formation')->default('4-4-2'); // Formation like 4-4-2, 3-5-2, etc.
            $table->boolean('is_confirmed')->default(false); // Whether lineup is finalized
            $table->timestamp('confirmed_at')->nullable();
            $table->foreignId('confirmed_by')->nullable()->constrained('users')->onDelete('set null'); // Admin who confirmed
            $table->text('notes')->nullable(); // Lineup notes
            $table->timestamps();

            // Ensure unique lineup per team per live game
            $table->unique(['live_game_id', 'team_id']);

            // Indexes for performance
            $table->index(['live_game_id', 'team_id']);
            $table->index('is_confirmed');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lineups');
    }
};

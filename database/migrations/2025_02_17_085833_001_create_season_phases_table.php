<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('season_phases', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('format', ['league', 'cup']);
            $table->json('config');
            $table->tinyInteger('teams_count')->unsigned();
            $table->enum('status', ['upcoming', 'ongoing', 'completed']);
            $table->bigInteger('season_id')->unsigned();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();

            $table
                ->foreign('season_id')
                ->references('id')
                ->on('seasons')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('season_phases');
    }
};

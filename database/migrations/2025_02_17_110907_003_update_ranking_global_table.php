<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ranking_global', function (Blueprint $table) {
            $table->integer('rank_gameweek')->unsigned()->after('rank');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ranking_global', function (Blueprint $table) {
            $table->dropColumn('rank');
            $table
                ->integer('point')
                ->nullable()
                ->change();
            $table->renameColumn('rank_gameweek', 'rank');
        });
    }
};

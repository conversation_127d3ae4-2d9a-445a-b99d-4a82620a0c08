<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('game_referee', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('game_id')->unsigned();
            $table->bigInteger('referee_id')->unsigned();
            $table->enum('role', [
                'main_referee',
                'assistant_referee_1',
                'assistant_referee_2'
            ]);
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();

            // Foreign key constraints
            $table->foreign('game_id')
                  ->references('id')
                  ->on('games')
                  ->onDelete('cascade')
                  ->onUpdate('cascade');

            $table->foreign('referee_id')
                  ->references('id')
                  ->on('referees')
                  ->onDelete('cascade')
                  ->onUpdate('cascade');

            // Ensure unique role per game (one main referee, one assistant 1, one assistant 2)
            $table->unique(['game_id', 'role']);
            
            // Ensure a referee can't have multiple roles in the same game
            $table->unique(['game_id', 'referee_id']);

            // Add indexes for better performance
            $table->index(['game_id', 'role']);
            $table->index('referee_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('game_referee');
    }
};

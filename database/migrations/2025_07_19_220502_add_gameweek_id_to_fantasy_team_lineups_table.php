<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fantasy_team_lineups', function (Blueprint $table) {
            $table->bigInteger('gameweek_id')->unsigned()->after('fantasy_player_id');
            
            $table->foreign('gameweek_id')
                  ->references('id')
                  ->on('gameweeks')
                  ->onDelete('cascade')
                  ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fantasy_team_lineups', function (Blueprint $table) {
            $table->dropForeign(['gameweek_id']);
            $table->dropColumn('gameweek_id');
        });
    }
};

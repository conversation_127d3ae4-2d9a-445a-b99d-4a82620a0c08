<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gameweeks', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('season_phase_id')->unsigned();
            $table->string('name');
            $table->dateTime('start_date');
            $table->dateTime('end_date');
            $table->json('rules');
            $table->enum('status', ['upcoming', 'ongoing', 'completed']);
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();

            $table
                ->foreign('season_phase_id')
                ->references('id')
                ->on('season_phases')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gameweeks');
    }
};

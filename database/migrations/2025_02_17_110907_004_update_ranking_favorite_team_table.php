<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ranking_favorite_team', function (Blueprint $table) {
            $table
                ->integer('rank_gameweek')
                ->unsigned()
                ->after('fantasy_team_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ranking_favorite_team', function (Blueprint $table) {
            $table->dropColumn('rank_gameweek');
        });
    }
};

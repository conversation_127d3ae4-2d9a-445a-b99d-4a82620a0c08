<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fantasy_transfers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('fantasy_team_id')->constrained()->onDelete('cascade');
            $table->foreignId('gameweek_id')->constrained()->onDelete('cascade');
            $table->foreignId('player_in_id')->nullable()->constrained('players')->onDelete('cascade');
            $table->foreignId('player_out_id')->nullable()->constrained('players')->onDelete('cascade');
            $table->decimal('transfer_cost', 10, 2)->default(0);
            $table->boolean('is_free_transfer')->default(false);
            $table->enum('transfer_type', ['in', 'out', 'swap'])->default('swap');
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->index(['fantasy_team_id', 'gameweek_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fantasy_transfers');
    }
};

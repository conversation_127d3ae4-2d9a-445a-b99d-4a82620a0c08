<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('live_games', function (Blueprint $table) {
            $table->id();
            $table->foreignId('game_id')->constrained('games')->onDelete('cascade');
            $table->string('status')->default('pre_match'); // LiveGameStatus enum
            $table->integer('current_minute')->default(0);
            $table->integer('added_time')->default(0);
            $table->timestamp('kick_off_time')->nullable();
            $table->timestamp('half_time_start')->nullable();
            $table->timestamp('half_time_end')->nullable();
            $table->timestamp('full_time')->nullable();
            $table->json('events')->nullable(); // Store live events like goals, cards, substitutions
            $table->json('statistics')->nullable(); // Store match statistics
            $table->text('notes')->nullable(); // Admin notes
            $table->timestamps();

            // Indexes for performance
            $table->index(['game_id', 'status']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('live_games');
    }
};

<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fantasy_team_league', function (Blueprint $table) {
            $table->bigInteger('league_id')->unsigned();
            $table->bigInteger('fantasy_team_id')->unsigned();

            $table
                ->foreign('league_id')
                ->references('id')
                ->on('leagues')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table
                ->foreign('fantasy_team_id')
                ->references('id')
                ->on('fantasy_teams')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fantasy_team_league');
    }
};

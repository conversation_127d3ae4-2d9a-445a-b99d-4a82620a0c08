<?php

namespace Database\Seeders;

use App\Models\Competition;
use App\Models\Gameweek;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class GameweekSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the specific competitions and their season phases
        $ligue1Tunisie = Competition::where('slug', 'ligue-1-tunisie')
            ->with('seasons.seasonPhases')
            ->first();
        $premierLeague = Competition::where('slug', 'premier-league')
            ->with('seasons.seasonPhases')
            ->first();

        // Create 2 ongoing gameweeks for each Ligue 1 Tunisie season phase
        if ($ligue1Tunisie && $ligue1Tunisie->seasons->count() > 0) {
            foreach ($ligue1Tunisie->seasons as $season) {
                foreach ($season->seasonPhases as $seasonPhase) {
                    if ($seasonPhase->status === 'ongoing') {
                        // Previous Gameweeks (completed)
                        Gameweek::factory()->create([
                            'season_phase_id' => $seasonPhase->id,
                            'name' => 'Gameweek 13',
                            'start_date' => Carbon::now()->subDays(17)->format('Y-m-d H:i:s'),
                            'end_date' => Carbon::now()->subDays(10)->format('Y-m-d H:i:s'),
                            'rules' => [
                                'transfers_allowed' => 2,
                                'captain_multiplier' => 2,
                                'vice_captain_multiplier' => 1.5,
                                'bench_boost' => false,
                                'triple_captain' => false,
                                'wildcard' => false,
                            ],
                            'status' => 'completed',
                        ]);

                        Gameweek::factory()->create([
                            'season_phase_id' => $seasonPhase->id,
                            'name' => 'Gameweek 14',
                            'start_date' => Carbon::now()->subDays(10)->format('Y-m-d H:i:s'),
                            'end_date' => Carbon::now()->subDays(3)->format('Y-m-d H:i:s'),
                            'rules' => [
                                'transfers_allowed' => 2,
                                'captain_multiplier' => 2,
                                'vice_captain_multiplier' => 1.5,
                                'bench_boost' => false,
                                'triple_captain' => false,
                                'wildcard' => false,
                            ],
                            'status' => 'completed',
                        ]);

                        // Current Gameweek
                        $currentGameweek = Gameweek::factory()->create([
                            'season_phase_id' => $seasonPhase->id,
                            'name' => 'Gameweek 15',
                            'start_date' => Carbon::now()->subDays(3)->format('Y-m-d H:i:s'),
                            'end_date' => Carbon::now()->addDays(4)->format('Y-m-d H:i:s'),
                            'rules' => [
                                'transfers_allowed' => 2,
                                'captain_multiplier' => 2,
                                'vice_captain_multiplier' => 1.5,
                                'bench_boost' => false,
                                'triple_captain' => false,
                                'wildcard' => false,
                            ],
                            'status' => 'ongoing',
                        ]);

                        // Next Gameweeks
                        Gameweek::factory()->create([
                            'season_phase_id' => $seasonPhase->id,
                            'name' => 'Gameweek 16',
                            'start_date' => Carbon::now()->addDays(7)->format('Y-m-d H:i:s'),
                            'end_date' => Carbon::now()->addDays(14)->format('Y-m-d H:i:s'),
                            'rules' => [
                                'transfers_allowed' => 2,
                                'captain_multiplier' => 2,
                                'vice_captain_multiplier' => 1.5,
                                'bench_boost' => true,
                                'triple_captain' => false,
                                'wildcard' => false,
                            ],
                            'status' => 'upcoming',
                        ]);

                        Gameweek::factory()->create([
                            'season_phase_id' => $seasonPhase->id,
                            'name' => 'Gameweek 17',
                            'start_date' => Carbon::now()->addDays(14)->format('Y-m-d H:i:s'),
                            'end_date' => Carbon::now()->addDays(21)->format('Y-m-d H:i:s'),
                            'rules' => [
                                'transfers_allowed' => 2,
                                'captain_multiplier' => 2,
                                'vice_captain_multiplier' => 1.5,
                                'bench_boost' => false,
                                'triple_captain' => true,
                                'wildcard' => false,
                            ],
                            'status' => 'upcoming',
                        ]);
                    }
                }
            }
        }

        // Create 2 ongoing gameweeks for each Premier League season phase
        if ($premierLeague && $premierLeague->seasons->count() > 0) {
            foreach ($premierLeague->seasons as $season) {
                foreach ($season->seasonPhases as $seasonPhase) {
                    if ($seasonPhase->status === 'ongoing') {
                        // Previous Gameweeks (completed)
                        Gameweek::factory()->create([
                            'season_phase_id' => $seasonPhase->id,
                            'name' => 'Gameweek 18',
                            'start_date' => Carbon::now()->subDays(16)->format('Y-m-d H:i:s'),
                            'end_date' => Carbon::now()->subDays(9)->format('Y-m-d H:i:s'),
                            'rules' => [
                                'transfers_allowed' => 1,
                                'captain_multiplier' => 2,
                                'vice_captain_multiplier' => 1.5,
                                'bench_boost' => false,
                                'triple_captain' => false,
                                'wildcard' => false,
                            ],
                            'status' => 'completed',
                        ]);

                        Gameweek::factory()->create([
                            'season_phase_id' => $seasonPhase->id,
                            'name' => 'Gameweek 19',
                            'start_date' => Carbon::now()->subDays(9)->format('Y-m-d H:i:s'),
                            'end_date' => Carbon::now()->subDays(2)->format('Y-m-d H:i:s'),
                            'rules' => [
                                'transfers_allowed' => 1,
                                'captain_multiplier' => 2,
                                'vice_captain_multiplier' => 1.5,
                                'bench_boost' => true,
                                'triple_captain' => false,
                                'wildcard' => false,
                            ],
                            'status' => 'completed',
                        ]);

                        // Current Gameweek
                        $currentGameweek = Gameweek::factory()->create([
                            'season_phase_id' => $seasonPhase->id,
                            'name' => 'Gameweek 20',
                            'start_date' => Carbon::now()->subDays(2)->format('Y-m-d H:i:s'),
                            'end_date' => Carbon::now()->addDays(5)->format('Y-m-d H:i:s'),
                            'rules' => [
                                'transfers_allowed' => 1,
                                'captain_multiplier' => 2,
                                'vice_captain_multiplier' => 1.5,
                                'bench_boost' => false,
                                'triple_captain' => false,
                                'wildcard' => false,
                            ],
                            'status' => 'ongoing',
                        ]);

                        // Next Gameweeks
                        Gameweek::factory()->create([
                            'season_phase_id' => $seasonPhase->id,
                            'name' => 'Gameweek 21',
                            'start_date' => Carbon::now()->addDays(7)->format('Y-m-d H:i:s'),
                            'end_date' => Carbon::now()->addDays(14)->format('Y-m-d H:i:s'),
                            'rules' => [
                                'transfers_allowed' => 1,
                                'captain_multiplier' => 2,
                                'vice_captain_multiplier' => 1.5,
                                'bench_boost' => false,
                                'triple_captain' => true,
                                'wildcard' => false,
                            ],
                            'status' => 'upcoming',
                        ]);

                        Gameweek::factory()->create([
                            'season_phase_id' => $seasonPhase->id,
                            'name' => 'Gameweek 22',
                            'start_date' => Carbon::now()->addDays(14)->format('Y-m-d H:i:s'),
                            'end_date' => Carbon::now()->addDays(21)->format('Y-m-d H:i:s'),
                            'rules' => [
                                'transfers_allowed' => 1,
                                'captain_multiplier' => 2,
                                'vice_captain_multiplier' => 1.5,
                                'bench_boost' => false,
                                'triple_captain' => false,
                                'wildcard' => true,
                            ],
                            'status' => 'upcoming',
                        ]);
                    }
                }
            }
        }

        // Create additional random gameweeks
        Gameweek::factory()
            ->count(3)
            ->create();
    }
}

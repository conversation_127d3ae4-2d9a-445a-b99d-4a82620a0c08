<?php

namespace Database\Seeders;

use App\Models\RankingLeague;
use App\Models\Tenant;
use Illuminate\Database\Seeder;

class RankingLeagueSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenant = Tenant::first();

        if ($tenant) {
            $tenant->execute(function () {
                RankingLeague::factory()
                    ->count(5)
                    ->create();
            });
        }
    }
}

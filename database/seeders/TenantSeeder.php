<?php

namespace Database\Seeders;

use App\Models\Tenant;
use Illuminate\Database\Seeder;

class TenantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create the specific "Coaching Foot" tenant
        Tenant::factory()->create([
            'name' => 'Coaching Foot',
            'domain' => 'fantasy.test',
            'database' => 'fantasy',
            'is_active' => true,
            'primary_color' => '#1a73e8',
            'secondary_color' => '#ffffff',
            'available_languages' => ['fr', 'ar'],
        ]);

        // Create additional random tenants
        Tenant::factory()
            ->count(4)
            ->create();
    }
}

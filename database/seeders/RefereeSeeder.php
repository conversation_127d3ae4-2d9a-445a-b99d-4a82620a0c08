<?php

namespace Database\Seeders;

use App\Models\Referee;
use Illuminate\Database\Seeder;

class RefereeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some experienced referees
        Referee::factory()
            ->experienced()
            ->count(5)
            ->create();

        // Create some beginner referees
        Referee::factory()
            ->beginner()
            ->count(3)
            ->create();

        // Create some additional referees
        Referee::factory()
            ->count(2)
            ->create();

        // Create some specific referees with known data
        $specificReferees = [
            [
                'name' => '<PERSON>',
                'name_ar' => 'أحمد حسن',
                'country' => 'Egypt',
                'experience_years' => 15,
            ],
            [
                'name' => '<PERSON>',
                'name_ar' => 'محمد علي',
                'country' => 'Egypt',
                'experience_years' => 12,
            ],
            [
                'name' => '<PERSON>',
                'name_ar' => 'عمر محمود',
                'country' => 'Egypt',
                'experience_years' => 8,
            ],
        ];

        foreach ($specificReferees as $refereeData) {
            Referee::factory()->create($refereeData);
        }
    }
}

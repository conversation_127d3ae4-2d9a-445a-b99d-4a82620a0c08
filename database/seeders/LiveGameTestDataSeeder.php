<?php

namespace Database\Seeders;

use App\Enums\GameStatus;
use App\Enums\LiveGameStatus;
use App\Enums\PlayerPosition;
use App\Enums\PlayerStatus;
use App\Models\Game;
use App\Models\Lineup;
use App\Models\LineupPlayer;
use App\Models\LiveGame;
use App\Models\Player;
use App\Models\Team;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LiveGameTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Creating comprehensive live game test data...');

        DB::transaction(function () {
            // Create different scenarios for testing
            $this->createPreMatchScenario();
            $this->createLiveMatchScenario();
            $this->createHalfTimeScenario();
            $this->createSecondHalfScenario();
            $this->createFinishedMatchScenario();
        });

        $this->displayTestDataSummary();
        $this->command->info('✅ Live game test data creation completed!');
    }

    private function createPreMatchScenario(): void
    {
        $this->command->info('📋 Creating pre-match scenario...');

        $game = Game::factory()->create([
            'status' => GameStatus::SCHEDULED,
            'game_date' => now()->addHours(2),
        ]);

        $liveGame = LiveGame::factory()->preMatch()->create([
            'game_id' => $game->id,
        ]);

        $this->createCompleteLineups($liveGame, $game);
        $this->command->info('✓ Pre-match scenario created');
    }

    private function createLiveMatchScenario(): void
    {
        $this->command->info('⚽ Creating live match scenario...');

        $game = Game::factory()->create([
            'status' => GameStatus::IN_PROGRESS,
            'home_score' => 1,
            'away_score' => 0,
            'game_date' => now()->subMinutes(30),
        ]);

        $liveGame = LiveGame::factory()->live()->create([
            'game_id' => $game->id,
            'current_minute' => 32,
        ]);

        $this->createCompleteLineups($liveGame, $game);
        $this->addLiveEvents($liveGame);
        $this->command->info('✓ Live match scenario created');
    }

    private function createHalfTimeScenario(): void
    {
        $this->command->info('⏸️ Creating half-time scenario...');

        $game = Game::factory()->create([
            'status' => GameStatus::IN_PROGRESS,
            'home_score' => 2,
            'away_score' => 1,
            'game_date' => now()->subMinutes(60),
        ]);

        $liveGame = LiveGame::factory()->create([
            'game_id' => $game->id,
            'status' => LiveGameStatus::HALF_TIME,
            'current_minute' => 45,
            'kick_off_time' => now()->subMinutes(60),
            'half_time_start' => now()->subMinutes(15),
        ]);

        $this->createCompleteLineups($liveGame, $game);
        $this->addFirstHalfEvents($liveGame);
        $this->command->info('✓ Half-time scenario created');
    }

    private function createSecondHalfScenario(): void
    {
        $this->command->info('🔄 Creating second half scenario...');

        $game = Game::factory()->create([
            'status' => GameStatus::IN_PROGRESS,
            'home_score' => 2,
            'away_score' => 2,
            'game_date' => now()->subMinutes(75),
        ]);

        $liveGame = LiveGame::factory()->create([
            'game_id' => $game->id,
            'status' => LiveGameStatus::SECOND_HALF,
            'current_minute' => 67,
            'kick_off_time' => now()->subMinutes(75),
            'half_time_start' => now()->subMinutes(30),
            'half_time_end' => now()->subMinutes(15),
        ]);

        $this->createCompleteLineups($liveGame, $game);
        $this->addFullMatchEvents($liveGame);
        $this->addSubstitutions($liveGame);
        $this->command->info('✓ Second half scenario created');
    }

    private function createFinishedMatchScenario(): void
    {
        $this->command->info('🏁 Creating finished match scenario...');

        $game = Game::factory()->create([
            'status' => GameStatus::FINISHED,
            'home_score' => 3,
            'away_score' => 1,
            'game_date' => now()->subHours(2),
        ]);

        $liveGame = LiveGame::factory()->finished()->create([
            'game_id' => $game->id,
        ]);

        $this->createCompleteLineups($liveGame, $game);
        $this->addCompleteMatchEvents($liveGame);
        $this->addSubstitutions($liveGame);
        $this->syncPlayerPerformances($liveGame);
        $this->command->info('✓ Finished match scenario created');
    }

    private function createCompleteLineups(LiveGame $liveGame, Game $game): void
    {
        // Create lineups for both teams
        $homeTeam = $game->homeTeam;
        $awayTeam = $game->awayTeam;

        // Ensure teams have enough players
        $this->ensureTeamHasPlayers($homeTeam);
        $this->ensureTeamHasPlayers($awayTeam);

        // Create home team lineup
        $homeLineup = Lineup::factory()->confirmed()->create([
            'live_game_id' => $liveGame->id,
            'team_id' => $homeTeam->id,
            'formation' => '4-4-2',
        ]);

        // Create away team lineup
        $awayLineup = Lineup::factory()->confirmed()->create([
            'live_game_id' => $liveGame->id,
            'team_id' => $awayTeam->id,
            'formation' => '4-3-3',
        ]);

        $this->createRealisticLineupPlayers($homeLineup);
        $this->createRealisticLineupPlayers($awayLineup);
    }

    private function ensureTeamHasPlayers(Team $team): void
    {
        $currentPlayerCount = $team->players()->count();

        if ($currentPlayerCount < 18) {
            $playersNeeded = 18 - $currentPlayerCount;

            $newPlayers = Player::factory()->count($playersNeeded)->create();
            $team->players()->attach($newPlayers->pluck('id'));
        }
    }

    private function createRealisticLineupPlayers(Lineup $lineup): void
    {
        $team = $lineup->team;
        $players = $team->players()->get();

        // Create starting XI with realistic positions
        $formations = [
            'GK' => 1,
            'DEF' => 4,
            'MID' => 4,
            'FWD' => 2,
        ];

        $usedPlayers = collect();
        $jerseyNumber = 1;

        // Create starting XI
        foreach ($formations as $positionCode => $count) {
            $position = PlayerPosition::from($positionCode);
            $positionPlayers = $players->where('position', $position)
                ->whereNotIn('id', $usedPlayers->pluck('id'))
                ->take($count);

            foreach ($positionPlayers as $player) {
                LineupPlayer::factory()->starting()->create([
                    'lineup_id' => $lineup->id,
                    'player_id' => $player->id,
                    'position' => $position,
                    'jersey_number' => $jerseyNumber++,
                    'is_captain' => $usedPlayers->isEmpty(),
                    'is_vice_captain' => $usedPlayers->count() === 1,
                ]);

                $usedPlayers->push($player);
            }
        }

        // Create substitutes
        $substitutes = $players->whereNotIn('id', $usedPlayers->pluck('id'))->take(7);
        foreach ($substitutes as $player) {
            LineupPlayer::factory()->substitute()->create([
                'lineup_id' => $lineup->id,
                'player_id' => $player->id,
                'position' => $player->position,
                'jersey_number' => $jerseyNumber++,
            ]);
        }
    }

    private function addLiveEvents(LiveGame $liveGame): void
    {
        $events = [
            [
                'type' => 'kick_off',
                'minute' => 0,
                'description' => 'Match started',
                'timestamp' => $liveGame->kick_off_time,
            ],
            [
                'type' => 'goal',
                'minute' => 15,
                'description' => 'Goal scored',
                'team_id' => $liveGame->game->home_team_id,
                'timestamp' => $liveGame->kick_off_time->addMinutes(15),
            ],
        ];

        $liveGame->update(['events' => $events]);
    }

    private function addFirstHalfEvents(LiveGame $liveGame): void
    {
        $events = [
            [
                'type' => 'kick_off',
                'minute' => 0,
                'description' => 'Match started',
                'timestamp' => $liveGame->kick_off_time,
            ],
            [
                'type' => 'goal',
                'minute' => 12,
                'description' => 'Goal scored',
                'team_id' => $liveGame->game->home_team_id,
                'timestamp' => $liveGame->kick_off_time->addMinutes(12),
            ],
            [
                'type' => 'goal',
                'minute' => 28,
                'description' => 'Goal scored',
                'team_id' => $liveGame->game->away_team_id,
                'timestamp' => $liveGame->kick_off_time->addMinutes(28),
            ],
            [
                'type' => 'goal',
                'minute' => 43,
                'description' => 'Goal scored',
                'team_id' => $liveGame->game->home_team_id,
                'timestamp' => $liveGame->kick_off_time->addMinutes(43),
            ],
            [
                'type' => 'half_time',
                'minute' => 45,
                'description' => 'Half time',
                'timestamp' => $liveGame->half_time_start,
            ],
        ];

        $liveGame->update(['events' => $events]);
    }

    private function addFullMatchEvents(LiveGame $liveGame): void
    {
        // Add comprehensive events for a full match
        $events = [
            ['type' => 'kick_off', 'minute' => 0, 'description' => 'Match started'],
            ['type' => 'goal', 'minute' => 12, 'description' => 'Goal scored'],
            ['type' => 'card', 'minute' => 25, 'description' => 'Yellow card'],
            ['type' => 'goal', 'minute' => 34, 'description' => 'Goal scored'],
            ['type' => 'half_time', 'minute' => 45, 'description' => 'Half time'],
            ['type' => 'second_half', 'minute' => 46, 'description' => 'Second half started'],
            ['type' => 'goal', 'minute' => 52, 'description' => 'Goal scored'],
            ['type' => 'substitution', 'minute' => 58, 'description' => 'Substitution'],
            ['type' => 'goal', 'minute' => 65, 'description' => 'Goal scored'],
        ];

        $liveGame->update(['events' => $events]);
    }

    private function addCompleteMatchEvents(LiveGame $liveGame): void
    {
        $this->addFullMatchEvents($liveGame);

        // Add final events
        $events = $liveGame->events;
        $events[] = [
            'type' => 'full_time',
            'minute' => 90,
            'description' => 'Match ended',
            'timestamp' => $liveGame->full_time,
        ];

        $liveGame->update(['events' => $events]);
    }

    private function addSubstitutions(LiveGame $liveGame): void
    {
        foreach ($liveGame->lineups as $lineup) {
            $startingPlayers = $lineup->players()->where('status', PlayerStatus::STARTING)->limit(2)->get();
            $substitutes = $lineup->players()->where('status', PlayerStatus::SUBSTITUTE)->limit(2)->get();

            foreach ($startingPlayers as $index => $playerOut) {
                if (isset($substitutes[$index])) {
                    $playerIn = $substitutes[$index];
                    $minute = 60 + ($index * 10);

                    $playerOut->update([
                        'status' => PlayerStatus::SUBSTITUTED_OUT,
                        'substitution_minute' => $minute,
                    ]);

                    $playerIn->update([
                        'status' => PlayerStatus::SUBSTITUTED_IN,
                        'substitution_minute' => $minute,
                        'substituted_for' => $playerOut->id,
                    ]);
                }
            }
        }
    }

    private function syncPlayerPerformances(LiveGame $liveGame): void
    {
        // This would typically be done by the LiveGameService
        // For now, just add some sample live stats
        foreach ($liveGame->lineups as $lineup) {
            foreach ($lineup->players as $lineupPlayer) {
                if ($lineupPlayer->status->isOnField()) {
                    $lineupPlayer->update([
                        'live_stats' => [
                            'goals' => fake()->numberBetween(0, 2),
                            'assists' => fake()->numberBetween(0, 1),
                            'cards' => [],
                            'minutes_played' => 90,
                        ],
                    ]);
                }
            }
        }
    }

    private function displayTestDataSummary(): void
    {
        $this->command->info("\n📊 Test Data Summary:");

        $liveGameCount = LiveGame::count();
        $lineupCount = Lineup::count();
        $lineupPlayerCount = LineupPlayer::count();

        $this->command->info("  Live Games: {$liveGameCount}");
        $this->command->info("  Lineups: {$lineupCount}");
        $this->command->info("  Lineup Players: {$lineupPlayerCount}");

        $statusSummary = LiveGame::selectRaw('status, count(*) as count')
            ->groupBy('status')
            ->get();

        $this->command->info("\n  Status Distribution:");
        foreach ($statusSummary as $item) {
            $statusLabel = $item->status instanceof LiveGameStatus ? $item->status->getLabel() : $item->status;
            $this->command->info("    {$statusLabel}: {$item->count}");
        }
    }
}

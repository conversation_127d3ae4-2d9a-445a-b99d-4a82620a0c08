<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\RankingJoinedGameweek;
use App\Models\Tenant;

class RankingJoinedGameweekSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenant = Tenant::first();

        if ($tenant) {
            $tenant->execute(function () {
                RankingJoinedGameweek::factory()
                    ->count(5)
                    ->create();
            });
        }
    }
}

<?php

namespace Database\Seeders;

use App\Models\RankingGlobal;
use App\Models\Tenant;
use Illuminate\Database\Seeder;

class RankingGlobalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenant = Tenant::first();

        if ($tenant) {
            $tenant->execute(function () {
                RankingGlobal::factory()
                    ->count(5)
                    ->create();
            });
        }
    }
}

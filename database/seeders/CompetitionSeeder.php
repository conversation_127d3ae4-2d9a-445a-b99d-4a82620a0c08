<?php

namespace Database\Seeders;

use App\Models\Competition;
use App\Models\Tenant;
use Illuminate\Database\Seeder;

class CompetitionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $coachingFootTenant = Tenant::where('domain', 'fantasy.test')->first();

        if ($coachingFootTenant) {
            $ligue1Tunisie = Competition::create([
                'name' => 'Ligue 1 Tunisie',
                'slug' => 'ligue-1-tunisie',
                'logo' => '/images/competitions/ligue-1-tunisie.png',
                'type' => 'league',
                'status' => 'ongoing',
                'current_season_id' => null,
            ]);

            $premierLeague = Competition::create([
                'name' => 'Premier League',
                'slug' => 'premier-league',
                'logo' => '/images/competitions/premier-league.png',
                'type' => 'league',
                'status' => 'ongoing',
                'current_season_id' => null,
            ]);

            $coachingFootTenant->competitions()->attach([
                $ligue1Tunisie->id,
                $premierLeague->id,
            ]);
        }

        // Ensure unique competitions
        $uniqueCompetitions = [
            'Championship',
            'League One',
            'League Two',
            'FA Cup',
            'EFL Cup',
            'Community Shield',
            'UEFA Champions League',
            'UEFA Europa League',
            'UEFA Conference League',
        ];

        foreach ($uniqueCompetitions as $competitionName) {
            \App\Models\Competition::factory()
                ->withName($competitionName)
                ->create();
        }
    }
}

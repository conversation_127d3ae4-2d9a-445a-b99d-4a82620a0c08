<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class StadiumSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create specific Premier League stadiums
        $premierLeagueStadiums = [
            ['name' => 'Emirates Stadium', 'city' => 'London', 'capacity' => 60704],
            ['name' => 'Old Trafford', 'city' => 'Manchester', 'capacity' => 74310],
            ['name' => 'Anfield', 'city' => 'Liverpool', 'capacity' => 53394],
            ['name' => 'Etihad Stadium', 'city' => 'Manchester', 'capacity' => 55017],
            ['name' => 'Stamford Bridge', 'city' => 'London', 'capacity' => 40341],
            ['name' => 'Tottenham Hotspur Stadium', 'city' => 'London', 'capacity' => 62850],
            ['name' => 'London Stadium', 'city' => 'London', 'capacity' => 66000],
            ['name' => 'Villa Park', 'city' => 'Birmingham', 'capacity' => 42095],
        ];

        foreach ($premierLeagueStadiums as $stadiumData) {
            \App\Models\Stadium::factory()->create([
                'name' => $stadiumData['name'],
                'city' => $stadiumData['city'],
                'capacity' => $stadiumData['capacity'],
                'country' => 'England',
                'covered' => true,
            ]);
        }

        // Create additional random stadiums
        \App\Models\Stadium::factory()
            ->count(12)
            ->create();
    }
}

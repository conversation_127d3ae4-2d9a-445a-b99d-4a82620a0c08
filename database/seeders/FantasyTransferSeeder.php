<?php

namespace Database\Seeders;

use App\Models\FantasyTransfer;
use App\Models\Tenant;
use Illuminate\Database\Seeder;

class FantasyTransferSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenant = Tenant::first();

        if ($tenant) {
            $tenant->execute(function () {
                FantasyTransfer::factory()
                    ->count(5)
                    ->create();
            });
        }
    }
}

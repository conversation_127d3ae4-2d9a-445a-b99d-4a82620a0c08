<?php

namespace Database\Seeders;

use App\Enums\GameStatus;
use App\Enums\LiveGameStatus;
use App\Enums\PlayerPosition;
use App\Enums\PlayerStatus;
use App\Models\Game;
use App\Models\Lineup;
use App\Models\LineupPlayer;
use App\Models\LiveGame;
use App\Models\Player;
use App\Models\Team;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LiveGameSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting LiveGame seeding...');

        // Get existing data to work with
        $games = Game::with(['homeTeam', 'awayTeam', 'gameweek'])->limit(15)->get();
        $teams = Team::with('players')->get();

        if ($games->isEmpty()) {
            $this->command->warn('No games found. Please seed games first.');

            return;
        }

        if ($teams->isEmpty()) {
            $this->command->warn('No teams found. Please seed teams first.');

            return;
        }

        DB::transaction(function () use ($games, $teams) {
            // Create different types of live games for testing
            $gameIndex = 0;

            foreach ($games as $game) {
                $this->createLiveGameWithLineups($game, $teams);
                $gameIndex++;
            }
        });

        $this->command->info('LiveGame seeding completed!');
        $this->displaySeedingSummary();
    }

    private function getScenarioForIndex(int $index): string
    {
        $scenarios = [
            'pre_match',    // Games ready to start
            'live_first',   // First half in progress
            'half_time',    // Half time break
            'live_second',  // Second half in progress
            'finished',     // Completed games
        ];

        return $scenarios[$index % count($scenarios)];
    }

    private function displaySeedingSummary(): void
    {
        $summary = LiveGame::selectRaw('status, count(*) as count')
            ->groupBy('status')
            ->get();

        $this->command->info("\n📊 Live Games Summary:");
        foreach ($summary as $item) {
            $this->command->info("  {$item->status}: {$item->count} games");
        }
    }

    private function createLiveGameWithLineups(Game $game, $teams): void
    {
        // Skip if live game already exists
        if ($game->liveGame) {
            return;
        }

        $this->command->info("Creating live game for: {$game->game_display_name}");

        // Create live game with random status
        $status = fake()->randomElement([
            LiveGameStatus::PRE_MATCH,
            LiveGameStatus::LIVE,
            LiveGameStatus::HALF_TIME,
            LiveGameStatus::SECOND_HALF,
            LiveGameStatus::FINISHED,
        ]);

        $liveGame = LiveGame::factory()
            ->state(['game_id' => $game->id, 'status' => $status])
            ->create();

        // Update game status to match live game status
        $gameStatus = match ($status) {
            LiveGameStatus::PRE_MATCH => GameStatus::SCHEDULED,
            LiveGameStatus::LIVE, LiveGameStatus::HALF_TIME, LiveGameStatus::SECOND_HALF => GameStatus::IN_PROGRESS,
            LiveGameStatus::FINISHED => GameStatus::FINISHED,
            default => GameStatus::SCHEDULED,
        };
        $game->update(['status' => $gameStatus]);

        // Create lineups for both teams
        $homeTeam = $teams->find($game->home_team_id);
        $awayTeam = $teams->find($game->away_team_id);

        if ($homeTeam && $awayTeam) {
            $this->createTeamLineup($liveGame, $homeTeam);
            $this->createTeamLineup($liveGame, $awayTeam);
        }

        $this->command->info("✓ Created live game and lineups for {$game->game_display_name}");
    }

    private function createTeamLineup(LiveGame $liveGame, Team $team): void
    {
        // Get team players
        $teamPlayers = $team->players()->get();

        if ($teamPlayers->count() < 15) {
            $this->command->warn("Team {$team->name} has insufficient players. Skipping lineup creation.");

            return;
        }

        // Create lineup
        $formations = ['4-4-2', '4-3-3', '3-5-2', '4-5-1', '4-2-3-1'];
        $lineup = Lineup::factory()
            ->confirmed()
            ->state([
                'live_game_id' => $liveGame->id,
                'team_id' => $team->id,
                'formation' => fake()->randomElement($formations),
            ])
            ->create();

        // Create starting XI with proper formation
        $this->createStartingXI($lineup, $teamPlayers);

        // Create substitutes
        $this->createSubstitutes($lineup, $teamPlayers);

        // Add some substitutions if game is in progress
        if ($liveGame->status->isActive() || $liveGame->status->isCompleted()) {
            $this->addSubstitutions($lineup, $liveGame->current_minute);
        }
    }

    private function createStartingXI(Lineup $lineup, $players): void
    {
        // Define formation requirements
        $positions = [
            PlayerPosition::GOALKEEPER => 1,
            PlayerPosition::DEFENDER => 4,
            PlayerPosition::MIDFIELDER => 4,
            PlayerPosition::FORWARD => 2,
        ];

        $usedPlayers = collect();
        $jerseyNumbers = range(1, 11);
        shuffle($jerseyNumbers);
        $jerseyIndex = 0;

        foreach ($positions as $position => $count) {
            $positionPlayers = $players->where('position', $position)
                ->whereNotIn('id', $usedPlayers->pluck('id'))
                ->take($count);

            foreach ($positionPlayers as $index => $player) {
                $isCaptain = $usedPlayers->isEmpty(); // First player is captain
                $isViceCaptain = $usedPlayers->count() === 1; // Second player is vice captain

                LineupPlayer::factory()
                    ->starting()
                    ->state([
                        'lineup_id' => $lineup->id,
                        'player_id' => $player->id,
                        'position' => $position,
                        'jersey_number' => $jerseyNumbers[$jerseyIndex++],
                        'is_captain' => $isCaptain,
                        'is_vice_captain' => $isViceCaptain,
                    ])
                    ->create();

                $usedPlayers->push($player);
            }
        }
    }

    private function createSubstitutes(Lineup $lineup, $players): void
    {
        $startingPlayerIds = $lineup->players()->pluck('player_id');
        $availablePlayers = $players->whereNotIn('id', $startingPlayerIds)->take(7);

        $jerseyNumbers = range(12, 23);
        shuffle($jerseyNumbers);

        foreach ($availablePlayers as $index => $player) {
            LineupPlayer::factory()
                ->substitute()
                ->state([
                    'lineup_id' => $lineup->id,
                    'player_id' => $player->id,
                    'position' => $player->position,
                    'jersey_number' => $jerseyNumbers[$index] ?? (12 + $index),
                ])
                ->create();
        }
    }

    private function addSubstitutions(Lineup $lineup, int $currentMinute): void
    {
        if ($currentMinute < 30) {
            return; // Too early for substitutions
        }

        $substitutionCount = fake()->numberBetween(0, 3);
        $startingPlayers = $lineup->players()->where('status', PlayerStatus::STARTING)->get();
        $substitutes = $lineup->players()->where('status', PlayerStatus::SUBSTITUTE)->get();

        for ($i = 0; $i < $substitutionCount && $startingPlayers->isNotEmpty() && $substitutes->isNotEmpty(); $i++) {
            $playerOut = $startingPlayers->random();
            $playerIn = $substitutes->random();
            $minute = fake()->numberBetween(30, min($currentMinute, 85));

            // Update player statuses
            $playerOut->update([
                'status' => PlayerStatus::SUBSTITUTED_OUT,
                'substitution_minute' => $minute,
            ]);

            $playerIn->update([
                'status' => PlayerStatus::SUBSTITUTED_IN,
                'substitution_minute' => $minute,
                'substituted_for' => $playerOut->id,
            ]);

            // Remove from collections to avoid duplicate substitutions
            $startingPlayers = $startingPlayers->reject(fn ($p) => $p->id === $playerOut->id);
            $substitutes = $substitutes->reject(fn ($p) => $p->id === $playerIn->id);
        }
    }
}

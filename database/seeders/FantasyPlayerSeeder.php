<?php

namespace Database\Seeders;

use App\Models\FantasyPlayer;
use App\Models\Tenant;
use Illuminate\Database\Seeder;

class FantasyPlayerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenant = Tenant::first();

        if ($tenant) {
            $tenant->execute(function () {
                FantasyPlayer::factory()
                    ->count(5)
                    ->create();
            });
        }
    }
}

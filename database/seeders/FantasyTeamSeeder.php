<?php

namespace Database\Seeders;

use App\Models\FantasyTeam;
use App\Models\Tenant;
use Illuminate\Database\Seeder;

class FantasyTeamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenant = Tenant::first();

        if ($tenant) {
            $tenant->execute(function () {
                FantasyTeam::factory()
                    ->count(5)
                    ->create();
            });
        }
    }
}

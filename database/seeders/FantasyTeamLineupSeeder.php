<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\FantasyTeamLineup;
use App\Models\Tenant;

class FantasyTeamLineupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenant = Tenant::first();

        if ($tenant) {
            $tenant->execute(function () {
                $fantasyPlayers = \App\Models\FantasyPlayer::factory()->count(5)->create();
        
                // Get the first available gameweek for seeding
                $firstGameweek = \App\Models\Gameweek::first();
                if (!$firstGameweek) {
                    // If no gameweeks exist, create one for seeding purposes
                    $firstGameweek = \App\Models\Gameweek::factory()->create();
                }

                foreach ($fantasyPlayers as $player) {
                    $position = fake()->randomElement(['starting', 'bench']);
                    FantasyTeamLineup::factory()->create([
                        'fantasy_player_id' => $player->id,
                        'gameweek_id' => $firstGameweek->id,
                        'position' => $position,
                        'sub_priority' => $position === 'bench' ? fake()->numberBetween(1, 4) : 0,
                    ]);
                }
            });
        }
    }
}

<?php

namespace Database\Seeders;

use App\Models\Competition;
use App\Models\Season;
use Illuminate\Database\Seeder;

class SeasonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the specific competitions
        $ligue1Tunisie = Competition::where('slug', 'ligue-1-tunisie')->first();
        $premierLeague = Competition::where('slug', 'premier-league')->first();

        // Create 2 ongoing seasons for Ligue 1 Tunisie
        if ($ligue1Tunisie) {
            $ligue1Season1 = Season::factory()->create([
                'competition_id' => $ligue1Tunisie->id,
                'name' => '2024-25',
                'start_date' => '2024-08-01',
                'end_date' => '2025-05-31',
            ]);

            $ligue1Season2 = Season::factory()->create([
                'competition_id' => $ligue1Tunisie->id,
                'name' => '2023-24',
                'start_date' => '2023-08-01',
                'end_date' => '2024-05-31',
            ]);

            // Set the current season for the competition
            $ligue1Tunisie->update(['current_season_id' => $ligue1Season1->id]);
        }

        // Create 2 ongoing seasons for Premier League
        if ($premierLeague) {
            $plSeason1 = Season::factory()->create([
                'competition_id' => $premierLeague->id,
                'name' => '2024-25',
                'start_date' => '2024-08-01',
                'end_date' => '2025-05-31',
            ]);

            $plSeason2 = Season::factory()->create([
                'competition_id' => $premierLeague->id,
                'name' => '2023-24',
                'start_date' => '2023-08-01',
                'end_date' => '2024-05-31',
            ]);

            // Set the current season for the competition
            $premierLeague->update(['current_season_id' => $plSeason1->id]);
        }

        // Create additional random seasons
        Season::factory()
            ->count(3)
            ->create();
    }
}

<?php

namespace Database\Seeders;

use App\Models\Competition;
use App\Models\Player;
use App\Models\Team;
use Illuminate\Database\Seeder;

class PlayerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the specific competitions
        $ligue1Tunisie = Competition::where('slug', 'ligue-1-tunisie')->first();
        $premierLeague = Competition::where('slug', 'premier-league')->first();

        // Create players for Tunisian teams (at least 11 per team)
        if ($ligue1Tunisie && $ligue1Tunisie->currentSeason) {
            $tunisianTeams = $ligue1Tunisie->currentSeason->teams;

            $tunisianPlayerNames = [
                '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
                '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>',
                '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
                '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
                '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>',
                '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>',
                '<PERSON> <PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>',
                '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON> <PERSON>',
                '<PERSON> <PERSON>', '<PERSON> <PERSON>', 'Haythem Jouini', 'Amor Layouni',
                'Khalil Chemmam', 'Aymen Balbouli', 'Mortadha Ben Ouanes', 'Raed Fadaa',
                'Hazem Haj Hassen', 'Iheb Mbarki', 'Mohamed Amine Tougai', 'Yassine Chikhaoui',
                'Hamdi Harbaoui', 'Ahmed Akaichi', 'Saber Khalifa', 'Ben Youssef Becha',
                'Moez Ben Cherifia', 'Aymen Tahar', 'Ghazi Ayadi', 'Houssem Tka',
                'Chamseddine Dhaouadi', 'Mohamed Wael Derbali', 'Oussama Bouguerra', 'Yassine Khenissi',
            ];

            $positions = ['GK', 'GK', 'DEF', 'DEF', 'DEF', 'DEF', 'DEF', 'MID', 'MID', 'MID', 'MID', 'FWD', 'FWD', 'FWD', 'FWD'];

            foreach ($tunisianTeams as $team) {
                // Create exactly 15 players per team (11 starters + 4 subs)
                for ($i = 0; $i < 15; $i++) {
                    $playerName = $tunisianPlayerNames[array_rand($tunisianPlayerNames)] ?? fake()->name();
                    $position = $positions[$i % count($positions)];

                    // Generate realistic age for Tunisian players
                    $age = fake()->numberBetween(18, 35);
                    $birthday = now()->subYears($age)->subDays(fake()->numberBetween(0, 365));

                    $player = Player::factory()->create([
                        'name' => $playerName,
                        'birthday' => $birthday->format('Y-m-d'),
                        'country' => 'Tunisia',
                        'position' => $position,
                        'image' => '/images/players/'.strtolower(str_replace(' ', '_', $playerName)).'.jpg',
                    ]);

                    // Attach player to the team using pivot table
                    $team->players()->attach($player->id);
                }
            }
        }

        // Create players for Premier League teams (at least 11 per team)
        if ($premierLeague && $premierLeague->currentSeason) {
            $premierLeagueTeams = $premierLeague->currentSeason->teams;

            $premierLeaguePlayerNames = [
                'Harry Kane', 'Erling Haaland', 'Mohamed Salah', 'Kevin De Bruyne', 'Bruno Fernandes',
                'Son Heung-min', 'Bukayo Saka', 'Phil Foden', 'Marcus Rashford', 'Raheem Sterling',
                'Virgil van Dijk', 'Ruben Dias', 'William Saliba', 'Declan Rice', 'Casemiro',
                'Martin Odegaard', 'James Maddison', 'Jack Grealish', 'Jadon Sancho', 'Gabriel Jesus',
                'Darwin Nunez', 'Cody Gakpo', 'Luis Diaz', 'Diogo Jota', 'Roberto Firmino',
                'Alisson Becker', 'Ederson', 'Aaron Ramsdale', 'Jordan Pickford', 'Hugo Lloris',
                'Thiago Silva', 'John Stones', 'Harry Maguire', 'Luke Shaw', 'Trent Alexander-Arnold',
                'Andy Robertson', 'Kyle Walker', 'Joao Cancelo', 'Ben Chilwell', 'Reece James',
                'N\'Golo Kante', 'Fabinho', 'Jordan Henderson', 'Mason Mount', 'Kalvin Phillips',
                'Bernardo Silva', 'Riyad Mahrez', 'Gabriel Martinelli', 'Emile Smith Rowe', 'Mason Greenwood',
                'Ivan Toney', 'Ollie Watkins', 'Callum Wilson', 'Dominic Calvert-Lewin', 'Michail Antonio',
                'Jarrod Bowen', 'Said Benrahma', 'Wilfried Zaha', 'Eberechi Eze', 'James Ward-Prowse',
                'Yves Bissouma', 'Moises Caicedo', 'Alexis Mac Allister', 'Brighton Dunk', 'Joel Veltman',
                'Kaoru Mitoma', 'Solly March', 'Pascal Gross', 'Danny Welbeck', 'Neal Maupay',
            ];

            $countries = ['England', 'Brazil', 'Argentina', 'Spain', 'France', 'Germany', 'Portugal', 'Netherlands', 'Belgium', 'Italy'];
            $positions = ['GK', 'GK', 'DEF', 'DEF', 'DEF', 'DEF', 'DEF', 'MID', 'MID', 'MID', 'MID', 'FWD', 'FWD', 'FWD', 'FWD'];

            foreach ($premierLeagueTeams as $team) {
                // Create exactly 15 players per team (11 starters + 4 subs)
                for ($i = 0; $i < 15; $i++) {
                    $playerName = $premierLeaguePlayerNames[array_rand($premierLeaguePlayerNames)] ?? fake()->name();
                    $position = $positions[$i % count($positions)];
                    $country = $countries[array_rand($countries)];

                    // Generate realistic age for Premier League players
                    $age = fake()->numberBetween(18, 38);
                    $birthday = now()->subYears($age)->subDays(fake()->numberBetween(0, 365));

                    $player = Player::factory()->create([
                        'name' => $playerName,
                        'birthday' => $birthday->format('Y-m-d'),
                        'country' => $country,
                        'position' => $position,
                        'image' => '/images/players/'.strtolower(str_replace(' ', '_', $playerName)).'.jpg',
                    ]);

                    // Attach player to the team using pivot table
                    $team->players()->attach($player->id);
                }
            }
        }

        // Create additional random players
        Player::factory()
            ->count(10)
            ->create();
    }
}

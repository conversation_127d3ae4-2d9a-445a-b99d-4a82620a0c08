<?php

namespace Database\Seeders;

use App\Models\Game;
use App\Models\Gameweek;
use App\Models\Stadium;
use App\Models\Team;
use Illuminate\Database\Seeder;

class GameSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get gameweeks from current competitions (completed, ongoing, and upcoming)
        $gameweeks = Gameweek::whereHas('seasonPhase', function ($query) {
            $query->where('status', 'ongoing')
                ->whereHas('season', function ($seasonQuery) {
                    $seasonQuery->whereHas('competition', function ($competitionQuery) {
                        $competitionQuery->whereIn('slug', ['ligue-1-tunisie', 'premier-league']);
                    });
                });
        })->whereIn('status', ['completed', 'ongoing', 'upcoming'])->get();

        if ($gameweeks->isEmpty()) {
            // Fallback: create games with factory-generated gameweeks
            Game::factory()
                ->count(5)
                ->create();

            return;
        }

        // Get teams from the current competitions
        $teams = Team::whereHas('seasons', function ($query) {
            $query->whereHas('competition', function ($competitionQuery) {
                $competitionQuery->whereIn('slug', ['ligue-1-tunisie', 'premier-league']);
            });
        })->get();

        $stadiums = Stadium::all();

        if ($teams->count() < 2) {
            // Not enough teams, use all teams
            $teams = Team::all();
        }

        // Create games for each gameweek
        foreach ($gameweeks as $gameweek) {
            // Create different amounts of games based on gameweek status
            $gamesCount = match ($gameweek->status) {
                'completed' => fake()->numberBetween(4, 6),
                'ongoing' => fake()->numberBetween(5, 8),
                'upcoming' => fake()->numberBetween(3, 5),
                default => fake()->numberBetween(3, 5)
            };

            for ($i = 0; $i < $gamesCount; $i++) {
                // Select random teams (ensure home and away are different)
                $homeTeam = $teams->random();
                $awayTeam = $teams->where('id', '!=', $homeTeam->id)->random();

                // Generate realistic match date within gameweek period
                $gameDate = fake()->dateTimeBetween(
                    $gameweek->start_date,
                    $gameweek->end_date
                );

                // Determine status based on gameweek status
                if ($gameweek->status === 'completed') {
                    $status = 'finished';
                    $homeScore = fake()->numberBetween(0, 5);
                    $awayScore = fake()->numberBetween(0, 5);
                } elseif ($gameweek->status === 'upcoming') {
                    $status = 'scheduled';
                    $homeScore = null;
                    $awayScore = null;
                } else { // ongoing
                    $status = $gameDate < now() ? 'finished' : 'scheduled';
                    $homeScore = $status === 'finished' ? fake()->numberBetween(0, 5) : null;
                    $awayScore = $status === 'finished' ? fake()->numberBetween(0, 5) : null;
                }

                Game::create([
                    'gameweek_id' => $gameweek->id,
                    'home_team_id' => $homeTeam->id,
                    'away_team_id' => $awayTeam->id,
                    'stadium_id' => $stadiums->isNotEmpty() ? $stadiums->random()->id : null,
                    'game_date' => $gameDate->format('Y-m-d H:i:s'),
                    'home_score' => $homeScore,
                    'away_score' => $awayScore,
                    'status' => $status,
                ]);
            }
        }
    }
}

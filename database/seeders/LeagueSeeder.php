<?php

namespace Database\Seeders;

use App\Models\League;
use App\Models\Tenant;
use Illuminate\Database\Seeder;

class LeagueSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenant = Tenant::first();

        if ($tenant) {
            $tenant->execute(function () {
                League::factory()
                    ->count(5)
                    ->create();
            });
        }
    }
}

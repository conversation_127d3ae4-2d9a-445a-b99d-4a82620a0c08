<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\RankingFavoriteTeam;
use App\Models\Tenant;

class RankingFavoriteTeamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenant = Tenant::first();

        if ($tenant) {
            $tenant->execute(function () {
                RankingFavoriteTeam::factory()
                    ->count(5)
                    ->create();
            });
        }
    }
}

<?php

namespace Database\Seeders;

use App\Models\SeasonPhase;
use App\Models\Season;
use App\Models\Competition;
use Illuminate\Database\Seeder;

class SeasonPhaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the specific competitions and their seasons
        $ligue1Tunisie = Competition::where('slug', 'ligue-1-tunisie')->with('seasons')->first();
        $premierLeague = Competition::where('slug', 'premier-league')->with('seasons')->first();
        
        // Create 2 ongoing season phases for each Ligue 1 Tunisie season
        if ($ligue1Tunisie && $ligue1Tunisie->seasons->count() > 0) {
            foreach ($ligue1Tunisie->seasons as $season) {
                // Regular Season Phase
                $regularPhase = SeasonPhase::factory()->create([
                    'season_id' => $season->id,
                    'name' => 'Regular Season',
                    'format' => 'league',
                    'config' => [
                        'rounds' => 30,
                        'home_away' => true,
                        'points_win' => 3,
                        'points_draw' => 1,
                        'points_loss' => 0
                    ],
                    'teams_count' => 16,
                    'status' => 'ongoing',
                ]);
                
                // Playoffs Phase
                SeasonPhase::factory()->create([
                    'season_id' => $season->id,
                    'name' => 'Playoffs',
                    'format' => 'cup',
                    'config' => [
                        'rounds' => 3,
                        'home_away' => true,
                        'extra_time' => true,
                        'penalties' => true
                    ],
                    'teams_count' => 8,
                    'status' => 'upcoming',
                ]);
            }
        }
        
        // Create 2 ongoing season phases for each Premier League season
        if ($premierLeague && $premierLeague->seasons->count() > 0) {
            foreach ($premierLeague->seasons as $season) {
                // Regular Season Phase
                $regularPhase = SeasonPhase::factory()->create([
                    'season_id' => $season->id,
                    'name' => 'Regular Season',
                    'format' => 'league',
                    'config' => [
                        'rounds' => 38,
                        'home_away' => true,
                        'points_win' => 3,
                        'points_draw' => 1,
                        'points_loss' => 0
                    ],
                    'teams_count' => 20,
                    'status' => 'ongoing',
                ]);
                
                // Cup Phase (FA Cup style)
                SeasonPhase::factory()->create([
                    'season_id' => $season->id,
                    'name' => 'Cup Competition',
                    'format' => 'cup',
                    'config' => [
                        'rounds' => 6,
                        'home_away' => false,
                        'extra_time' => true,
                        'penalties' => true,
                        'replays' => true
                    ],
                    'teams_count' => 20,
                    'status' => 'upcoming',
                ]);
            }
        }

        // Create additional random season phases
        SeasonPhase::factory()
            ->count(3)
            ->create();
    }
}

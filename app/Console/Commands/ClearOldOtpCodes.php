<?php

namespace App\Console\Commands;

use App\Models\OtpCode;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ClearOldOtpCodes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otp:clear-old 
                            {--days=3 : Number of days to keep OTP records}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear old OTP codes from the database';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $days = (int) $this->option('days');
        $dryRun = $this->option('dry-run');

        $cutoffDate = Carbon::now()->subDays($days);

        $this->info("Clearing OTP codes older than {$days} days (before {$cutoffDate->format('Y-m-d H:i:s')})");

        // Get count of records to be deleted
        $count = OtpCode::countOldCodes($days);

        if ($count === 0) {
            $this->info('No old OTP codes found to delete.');

            return self::SUCCESS;
        }

        if ($dryRun) {
            $this->warn("DRY RUN: Would delete {$count} OTP code records.");

            // Show some sample records that would be deleted
            $sampleRecords = OtpCode::where('created_at', '<', $cutoffDate)
                ->limit(5)
                ->get(['id', 'phone', 'created_at', 'used']);
            if ($sampleRecords->isNotEmpty()) {
                $this->info('Sample records that would be deleted:');
                $headers = ['ID', 'Phone', 'Created At', 'Used'];
                $rows = $sampleRecords->map(function ($record) {
                    return [
                        $record->id,
                        substr($record->phone, 0, 3).'****'.substr($record->phone, -2), // Mask phone number
                        $record->created_at->format('Y-m-d H:i:s'),
                        $record->used ? 'Yes' : 'No',
                    ];
                });
                $this->table($headers, $rows);
            }

            return self::SUCCESS;
        }

        // Confirm deletion in production
        if ($this->laravel->environment('production')) {
            if (! $this->confirm("Are you sure you want to delete {$count} OTP code records?")) {
                $this->info('Operation cancelled.');

                return self::SUCCESS;
            }
        }

        try {
            // Delete old OTP codes
            $deletedCount = OtpCode::clearOldCodes($days);

            $this->info("Successfully deleted {$deletedCount} old OTP code records.");

            // Log the cleanup operation
            Log::info('OTP codes cleanup completed', [
                'deleted_count' => $deletedCount,
                'cutoff_date' => $cutoffDate->toDateTimeString(),
                'days_threshold' => $days,
            ]);

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error("Failed to delete OTP codes: {$e->getMessage()}");

            Log::error('OTP codes cleanup failed', [
                'error' => $e->getMessage(),
                'cutoff_date' => $cutoffDate->toDateTimeString(),
                'days_threshold' => $days,
            ]);

            return self::FAILURE;
        }
    }
}

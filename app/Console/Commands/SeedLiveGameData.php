<?php

namespace App\Console\Commands;

use Database\Seeders\LiveGameSeeder;
use Database\Seeders\LiveGameTestDataSeeder;
use Illuminate\Console\Command;

class SeedLiveGameData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seed:live-games {--test : Create test scenarios} {--fresh : Clear existing live game data first}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed live game data with realistic test scenarios';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting Live Game Data Seeding...');

        if ($this->option('fresh')) {
            $this->info('🧹 Clearing existing live game data...');
            $this->clearExistingData();
        }

        if ($this->option('test')) {
            $this->info('📊 Creating comprehensive test scenarios...');
            $this->call('db:seed', ['--class' => LiveGameTestDataSeeder::class]);
        } else {
            $this->info('📋 Creating live games from existing games...');
            $this->call('db:seed', ['--class' => LiveGameSeeder::class]);
        }

        $this->displaySummary();
        $this->info('✅ Live game data seeding completed!');

        return Command::SUCCESS;
    }

    private function clearExistingData(): void
    {
        \App\Models\LineupPlayer::truncate();
        \App\Models\Lineup::truncate();
        \App\Models\LiveGame::truncate();
        
        $this->info('✓ Cleared existing live game data');
    }

    private function displaySummary(): void
    {
        $liveGameCount = \App\Models\LiveGame::count();
        $lineupCount = \App\Models\Lineup::count();
        $lineupPlayerCount = \App\Models\LineupPlayer::count();

        $this->info("\n📊 Final Summary:");
        $this->info("  Live Games: {$liveGameCount}");
        $this->info("  Lineups: {$lineupCount}");
        $this->info("  Lineup Players: {$lineupPlayerCount}");

        // Show status distribution
        $statusSummary = \App\Models\LiveGame::selectRaw('status, count(*) as count')
            ->groupBy('status')
            ->get();

        if ($statusSummary->isNotEmpty()) {
            $this->info("\n  Status Distribution:");
            foreach ($statusSummary as $item) {
                $statusLabel = $item->status instanceof \App\Enums\LiveGameStatus 
                    ? $item->status->getLabel() 
                    : $item->status;
                $this->info("    {$statusLabel}: {$item->count}");
            }
        }

        $this->info("\n🎯 You can now test the live game management system at:");
        $this->info("   http://localhost:8000/admin/game-management");
    }
}

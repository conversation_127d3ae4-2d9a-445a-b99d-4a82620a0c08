<?php

namespace App\Jobs;

use App\Models\OtpCode;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ClearOldOtpCodesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Number of days to keep OTP records
     */
    private int $daysToKeep;

    /**
     * Create a new job instance.
     */
    public function __construct(int $daysToKeep = 3)
    {
        $this->daysToKeep = $daysToKeep;

        // Set job to run on a specific queue if needed
        $this->onQueue('maintenance');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $cutoffDate = Carbon::now()->subDays($this->daysToKeep);

        Log::info('Starting OTP codes cleanup job', [
            'days_to_keep' => $this->daysToKeep,
            'cutoff_date' => $cutoffDate->toDateTimeString(),
        ]);

        try {
            // Get count before deletion for logging
            $count = OtpCode::countOldCodes($this->daysToKeep);

            if ($count === 0) {
                Log::info('No old OTP codes found to delete');

                return;
            }

            // Delete old OTP codes
            $deletedCount = OtpCode::clearOldCodes($this->daysToKeep);

            Log::info('OTP codes cleanup job completed successfully', [
                'deleted_count' => $deletedCount,
                'cutoff_date' => $cutoffDate->toDateTimeString(),
                'days_threshold' => $this->daysToKeep,
            ]);

        } catch (\Exception $e) {
            Log::error('OTP codes cleanup job failed', [
                'error' => $e->getMessage(),
                'cutoff_date' => $cutoffDate->toDateTimeString(),
                'days_threshold' => $this->daysToKeep,
            ]);

            // Re-throw the exception to mark the job as failed
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('OTP codes cleanup job failed permanently', [
            'error' => $exception->getMessage(),
            'days_threshold' => $this->daysToKeep,
        ]);
    }
}

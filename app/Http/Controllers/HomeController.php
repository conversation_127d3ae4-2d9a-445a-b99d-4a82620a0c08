<?php

namespace App\Http\Controllers;

use App\Models\FantasyTeam;
use App\Models\FantasyTransfer;
use App\Models\RankingGlobal;
use App\Services\FantasyContextService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class HomeController extends Controller
{
    protected $fantasyContextService;

    public function __construct(FantasyContextService $fantasyContextService)
    {
        $this->fantasyContextService = $fantasyContextService;
    }

    /**
     * Display the welcome/dashboard page with dynamic data
     */
    public function index(Request $request)
    {
        $contextResult = $this->fantasyContextService->getFantasyContextWithErrors($request);

        // Default values in case of errors
        $dashboardData = [
            'teamValue' => '£0.0m',
            'totalPoints' => 0,
            'globalRank' => 'N/A',
            'currentWeek' => 'N/A',
            'recentActivity' => [],
            'upcomingFixtures' => [],
        ];

        if ($contextResult['success']) {
            $context = $contextResult['data'];
            $currentGameweek = $context['currentGameweek'];
            $currentSeason = $context['currentSeason'];

            // Get user's fantasy team for current context
            $user = Auth::user();
            $fantasyTeam = null;

            if ($user && $currentSeason) {
                $fantasyTeam = FantasyTeam::where('user_id', $user->id)
                    ->where('season_id', $currentSeason->id)
                    ->first();
            }

            // Team value
            $squadValue = 0;
            if ($fantasyTeam) {
                $squadPlayers = $fantasyTeam->fantasyPlayers
                    ->load('player');

                $squadValue = $squadPlayers->sum(function ($fantasyPlayer) {
                    return $fantasyPlayer ? $fantasyPlayer->purchase_price : 0;
                });
            }

            // Get total points
            $totalPoints = RankingGlobal::getTotalPointsForCurrentContext();

            // Get global rank
            $globalRank = RankingGlobal::getCurrentGlobalRank();

            // Current gameweek
            $currentWeekName = $currentGameweek ? $currentGameweek->name : 'N/A';

            // Recent activity (transfers)
            $recentTransfers = FantasyTransfer::getRecentTransfers(3);

            // Get fixtures data for the widget (current gameweek only)
            $fixtures = [];
            $gameweeks = [];
            $allGameweeks = [];
            $pagination = [
                'currentIndex' => 0,
                'totalGameweeks' => 1,
                'hasNext' => false,
                'hasPrevious' => false,
            ];

            if ($currentGameweek) {
                // Get games for current gameweek
                $games = $currentGameweek->games()
                    ->with([
                        'homeTeam:id,name,short_name,logo,shirt',
                        'awayTeam:id,name,short_name,logo,shirt',
                        'stadium:id,name',
                        'referees:id,name',
                    ])
                    ->orderBy('game_date')
                    ->get([
                        'id',
                        'gameweek_id',
                        'home_team_id',
                        'away_team_id',
                        'stadium_id',
                        'game_date',
                        'home_score',
                        'away_score',
                        'status',
                    ]);

                $fixtures[$currentGameweek->id] = $games->map(function ($game) {
                    // Get main referee
                    $mainReferee = $game->referees->where('pivot.role', 'main_referee')->first();

                    return [
                        'id' => $game->id,
                        'gameweek_id' => $game->gameweek_id,
                        'homeTeam' => [
                            'id' => $game->homeTeam->id,
                            'name' => $game->homeTeam->name,
                            'short_name' => $game->homeTeam->short_name,
                            'logo' => $this->getTeamLogoUrl($game->homeTeam->logo),
                            'shirt' => $game->homeTeam->shirt,
                        ],
                        'awayTeam' => [
                            'id' => $game->awayTeam->id,
                            'name' => $game->awayTeam->name,
                            'short_name' => $game->awayTeam->short_name,
                            'logo' => $this->getTeamLogoUrl($game->awayTeam->logo),
                            'shirt' => $game->awayTeam->shirt,
                        ],
                        'game_date' => $game->game_date,
                        'home_score' => $game->home_score,
                        'away_score' => $game->away_score,
                        'status' => $game->status->value,
                        'stadium' => $game->stadium ? [
                            'id' => $game->stadium->id,
                            'name' => $game->stadium->name,
                        ] : null,
                        'referee' => $mainReferee ? [
                            'id' => $mainReferee->id,
                            'name' => $mainReferee->name,
                        ] : null,
                    ];
                })->groupBy(function ($game) {
                    return (new \DateTime($game['game_date']))->format('Y-m-d');
                })->toArray();

                $gameweeks = [[
                    'id' => $currentGameweek->id,
                    'name' => $currentGameweek->name,
                    'startDate' => $currentGameweek->start_date,
                    'endDate' => $currentGameweek->end_date,
                    'status' => $currentGameweek->status,
                ]];

                $allGameweeks = $gameweeks;
            }

            $dashboardData = [
                'teamValue' => '£'.number_format($squadValue, 1).'m',
                'totalPoints' => $totalPoints,
                'globalRank' => $globalRank,
                'currentWeek' => $currentWeekName,
                'recentActivity' => $recentTransfers,
            ];
        }

        return Inertia::render('Welcome', [
            'dashboardData' => $dashboardData,
            'fixtures' => $fixtures,
            'gameweeks' => $gameweeks,
            'allGameweeks' => $allGameweeks,
            'currentGameweek' => $currentGameweek ? [
                'id' => $currentGameweek->id,
                'name' => $currentGameweek->name,
                'startDate' => $currentGameweek->start_date,
                'endDate' => $currentGameweek->end_date,
                'status' => $currentGameweek->status,
            ] : null,
            'pagination' => $pagination,
            'competition' => $context['competition'] ?? null,
            'season' => $context['currentSeason'] ?? null,
        ]);
    }

    /**
     * Get the proper URL for a team logo
     */
    private function getTeamLogoUrl(?string $logoPath): ?string
    {
        if (! $logoPath) {
            return null;
        }

        // If the logo path starts with '/', it's an old format (public path)
        if (str_starts_with($logoPath, '/')) {
            return asset($logoPath);
        }

        // Otherwise, it's a storage path
        return asset('storage/'.$logoPath);
    }
}

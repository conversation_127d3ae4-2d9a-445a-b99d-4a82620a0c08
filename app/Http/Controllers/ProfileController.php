<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class ProfileController extends Controller
{
    /**
     * Show the profile edit form.
     */
    public function edit()
    {
        $user = Auth::user();
        
        // Mock teams data - in a real app, this would come from a teams table
        $teams = [
            ['id' => 'arsenal', 'name' => 'Arsenal'],
            ['id' => 'chelsea', 'name' => 'Chelsea'],
            ['id' => 'liverpool', 'name' => 'Liverpool'],
            ['id' => 'manchester-city', 'name' => 'Manchester City'],
            ['id' => 'manchester-united', 'name' => 'Manchester United'],
            ['id' => 'tottenham', 'name' => 'Tottenham Hotspur'],
            ['id' => 'newcastle', 'name' => 'Newcastle United'],
            ['id' => 'brighton', 'name' => 'Brighton & Hove Albion'],
            ['id' => 'west-ham', 'name' => 'West Ham United'],
            ['id' => 'aston-villa', 'name' => 'Aston Villa'],
            ['id' => 'crystal-palace', 'name' => 'Crystal Palace'],
            ['id' => 'fulham', 'name' => 'Fulham'],
            ['id' => 'brentford', 'name' => 'Brentford'],
            ['id' => 'wolves', 'name' => 'Wolverhampton Wanderers'],
            ['id' => 'everton', 'name' => 'Everton'],
            ['id' => 'nottingham-forest', 'name' => 'Nottingham Forest'],
            ['id' => 'burnley', 'name' => 'Burnley'],
            ['id' => 'sheffield-united', 'name' => 'Sheffield United'],
            ['id' => 'luton', 'name' => 'Luton Town'],
            ['id' => 'bournemouth', 'name' => 'AFC Bournemouth'],
        ];

        return Inertia::render('Profile/Edit', [
            'user' => $user,
            'teams' => $teams,
        ]);
    }

    /**
     * Update the user's profile.
     */
    public function update(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:20'],
            'date_of_birth' => ['nullable', 'date', 'before:today'],
            'favorite_team' => ['nullable', 'string', 'max:255'],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }

            // Store new avatar
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $validated['avatar'] = $avatarPath;
        }

        // Update user
        $user->update($validated);

        return redirect()->back()->with('success', 'Profile updated successfully!');
    }

    /**
     * Delete the user's avatar.
     */
    public function deleteAvatar()
    {
        $user = Auth::user();

        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
            $user->update(['avatar' => null]);
        }

        return redirect()->back()->with('success', 'Avatar deleted successfully!');
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Player;
use App\Models\PlayerPerformance;
use App\Enums\PlayerPosition;
use App\Services\FantasyContextService;
use Illuminate\Http\Request;

class PlayerController extends Controller
{
    private FantasyContextService $contextService;

    public function __construct(FantasyContextService $contextService)
    {
        $this->contextService = $contextService;
    }

    public function playerPerformance(Request $request, int $playerId)
    {
        $contextResult = $this->contextService->getFantasyContextWithErrors($request);
        if (! $contextResult['success']) {
            return response()->json(['error' => $contextResult['error']], 400);
        }

        $context = $contextResult['data'];
        $currentSeason = $context['currentSeason'];

        $player = Player::find($playerId);
        if (! $player) {
            return response()->json(['error' => 'Player not found'], 404);
        }

        $performances = PlayerPerformance::query()
            ->where('player_id', $player->id)
            ->whereHas('game.gameweek.seasonPhase', function ($q) use ($currentSeason) {
                $q->where('season_id', $currentSeason->id);
            })
            ->get();

        // Aggregate stats
        $gamesPlayed = $performances->where('minutes_played', '>', 0)->count();
        $isGoalkeeper = $player->position === PlayerPosition::GOALKEEPER;

        if ($isGoalkeeper) {
            $stats = [
                'games_played' => $gamesPlayed,
                'own_goals' => (int) $performances->sum('own_goals'),
                'penalties_committed' => (int) $performances->sum('penalties_committed'),
                'penalties_caused' => (int) $performances->sum('penalties_caused'),
                'goals_conceded' => (int) $performances->sum('goals_conceded'),
                'penalties_saved' => (int) $performances->sum('penalities_saved'), // note column spelling
                'saves' => (int) $performances->sum('saves'),
                'red_cards' => (int) $performances->sum('red_cards'),
                'yellow_cards' => (int) $performances->sum('yellow_cards'),
            ];
        } else {
            $stats = [
                'games_played' => $gamesPlayed,
                'goals_scored' => (int) $performances->sum('goals_scored'),
                'own_goals' => (int) $performances->sum('own_goals'),
                'penalties_missed' => (int) $performances->sum('penalities_missed'), // note column spelling
                'penalties_committed' => (int) $performances->sum('penalties_committed'),
                'penalties_caused' => (int) $performances->sum('penalties_caused'),
                'red_cards' => (int) $performances->sum('red_cards'),
                'yellow_cards' => (int) $performances->sum('yellow_cards'),
            ];
        }

        return response()->json([
            'success' => true,
            'data' => [
                'performances' => $performances,
                'stats' => $stats,
            ],
        ]);
    }
}

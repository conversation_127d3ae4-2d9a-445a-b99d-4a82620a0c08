<?php

namespace App\Http\Controllers;

use App\Models\Booster;
use App\Models\FantasyPlayer;
use App\Models\FantasyTeamLineup;
use App\Services\FantasyContextService;
use App\Services\FantasyStatsService;
use App\Services\PlayerDataService;
use App\Services\PlayerPerformanceService;
use App\Services\SquadManagementService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class GameController extends Controller
{
    protected FantasyContextService $contextService;

    protected PlayerDataService $playerDataService;

    protected SquadManagementService $squadService;

    protected FantasyStatsService $statsService;

    protected PlayerPerformanceService $performanceService;

    public function __construct(
        FantasyContextService $contextService,
        PlayerDataService $playerDataService,
        SquadManagementService $squadService,
        FantasyStatsService $statsService,
        PlayerPerformanceService $performanceService
    ) {
        $this->contextService = $contextService;
        $this->playerDataService = $playerDataService;
        $this->squadService = $squadService;
        $this->statsService = $statsService;
        $this->performanceService = $performanceService;
    }

    /**
     * Show the my team page with current squad data
     */
    public function myTeam(Request $request)
    {
        // Get fantasy context using service
        $contextResult = $this->contextService->getFantasyContextWithErrors($request);
        if (! $contextResult['success']) {
            return redirect()->route('home')->with('error', $contextResult['error']);
        }

        $context = $contextResult['data'];
        $currentGameweek = $context['currentGameweek'];

        $boosters = Booster::where('tenant_id', $context['tenant']->id)->get();

        // Get user's fantasy team for current season
        $fantasyTeam = $this->contextService->getUserFantasyTeam($request);

        if (! $fantasyTeam) {
            return redirect()->route('fantasy-team.create')
                ->with('error', 'Please create your fantasy team first.');
        }

        // Get squad players using service
        $squadPlayers = $this->playerDataService->getSquadPlayersData($fantasyTeam->id, $currentGameweek);

        // Get current lineup - starting players only
        $startingPlayerIds = FantasyTeamLineup::where('gameweek_id', $currentGameweek->id)
            ->where('position', 'starting')
            ->whereHas('fantasyPlayer', function ($query) use ($fantasyTeam) {
                $query->where('fantasy_team_id', $fantasyTeam->id);
            })
            ->with('fantasyPlayer')
            ->get()
            ->pluck('fantasyPlayer.player_id')
            ->toArray();

        // Calculate team stats
        $squadValue = $squadPlayers->sum('price');
        $budget = $fantasyTeam->budget; // Use raw budget value from database
        $balance = $budget; // Budget represents the current available budget

        return Inertia::render('FantasyTeam/Game', [
            'teamData' => [
                'budget' => $budget,
                'squadValue' => $squadValue,
                'balance' => $balance,
                'freeTransfers' => $currentGameweek->transfers_allowed ?? 1,
            ],
            'squadPlayers' => $squadPlayers,
            'startingPlayerIds' => $startingPlayerIds,
            'fantasyTeam' => [
                'id' => $fantasyTeam->id,
                'name' => $fantasyTeam->name,
                'kit_type' => $fantasyTeam->kit_type,
                'kit_primary_color' => $fantasyTeam->kit_primary_color,
                'kit_secondary_color' => $fantasyTeam->kit_secondary_color,
            ],
            'currentGameweek' => [
                'id' => $currentGameweek->id,
                'name' => $currentGameweek->name,
                'deadline' => $currentGameweek->deadline,
            ],
            'boosters' => $boosters,
        ]);
    }

    /**
     * Update team lineup and captain selections
     */
    public function updateTeam(Request $request)
    {
        $validated = $request->validate([
            'lineup_players' => 'required|array|size:11',
            'lineup_players.*' => 'required|integer|exists:players,id',
            'captain_id' => 'required|integer|exists:players,id',
            'vice_captain_id' => 'required|integer|exists:players,id',
            'formation' => 'required|string|in:4-4-2,4-3-3,3-5-2,5-3-2,4-5-1,5-4-1,3-4-3',
        ]);

        // Get fantasy context using service
        $contextResult = $this->contextService->getFantasyContextWithErrors($request);
        if (! $contextResult['success']) {
            return response()->json(['error' => $contextResult['error']], 400);
        }
        $context = $contextResult['data'];
        $currentGameweek = $context['currentGameweek'];

        $fantasyTeam = $this->contextService->getUserFantasyTeam($request);

        if (! $fantasyTeam) {
            return response()->json(['error' => 'Fantasy team not found'], 404);
        }

        // Validate that all players belong to the user's squad
        $squadPlayerIds = FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->pluck('player_id')
            ->toArray();

        $invalidPlayers = array_diff($validated['lineup_players'], $squadPlayerIds);
        if (! empty($invalidPlayers)) {
            return response()->json(['error' => 'Some players are not in your squad'], 400);
        }

        if (! in_array($validated['captain_id'], $validated['lineup_players'])) {
            return response()->json(['error' => 'Captain must be in starting lineup'], 400);
        }

        if (! in_array($validated['vice_captain_id'], $validated['lineup_players'])) {
            return response()->json(['error' => 'Vice captain must be in starting lineup'], 400);
        }

        try {
            // Use squad management service to handle lineup updates
            $this->squadService->updateLineupOnly($fantasyTeam, $currentGameweek, $validated);

            return response()->json([
                'message' => 'Team updated successfully',
                'success' => true,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to update team: '.$e->getMessage(),
                'success' => false,
            ], 500);
        }
    }

    public function points(Request $request)
    {
        // Get fantasy context using service
        $contextResult = $this->contextService->getFantasyContextWithErrors($request);
        if (! $contextResult['success']) {
            return redirect()->route('home')->with('error', $contextResult['error']);
        }

        $context = $contextResult['data'];
        $currentGameweek = $context['currentGameweek'];
        $currentSeason = $context['currentSeason'];

        // Get user's fantasy team for current season
        $fantasyTeam = $this->contextService->getUserFantasyTeam($request);

        if (! $fantasyTeam) {
            return redirect()->route('fantasy-team.create')
                ->with('error', 'Please create your fantasy team first.');
        }

        // Get all gameweeks for the current season
        $gameweeks = $currentSeason->gameweeks()->orderBy('start_date', 'desc')->get();

        // Determine the gameweek to show points for
        $selectedGameweekId = $request->input('gameweek_id', $currentGameweek->id);
        $selectedGameweek = $gameweeks->firstWhere('id', $selectedGameweekId) ?? $currentGameweek;

        // Get squad players with points for the selected gameweek
        $squadPlayers = $this->playerDataService->getSquadPlayersData($fantasyTeam->id, $selectedGameweek);

        // Get current lineup - starting players only
        $startingPlayerIds = FantasyTeamLineup::where('gameweek_id', $selectedGameweek->id)
            ->where('position', 'starting')
            ->whereHas('fantasyPlayer', function ($query) use ($fantasyTeam) {
                $query->where('fantasy_team_id', $fantasyTeam->id);
            })
            ->with('fantasyPlayer')
            ->get()
            ->pluck('fantasyPlayer.player_id')
            ->toArray();

        // Get fantasy team statistics for the selected gameweek
        $fantasyStats = $this->statsService->getFantasyTeamStats($fantasyTeam, $selectedGameweek);

        // Get player performance data for all squad players in this gameweek
        $playerPerformances = [];
        foreach ($squadPlayers as $squadPlayer) {
            $player = \App\Models\Player::find($squadPlayer['id']);
            if ($player) {
                $performanceData = $this->performanceService->getPlayerPerformanceForGameweek($player, $selectedGameweek);
                if ($performanceData) {
                    $playerPerformances[$squadPlayer['id']] = $performanceData;
                }
            }
        }

        // Get fixtures for the selected gameweek
        $gameweekFixtures = $selectedGameweek->games()
            ->with([
                'homeTeam:id,name,short_name,logo,shirt',
                'awayTeam:id,name,short_name,logo,shirt',
                'stadium:id,name',
                'referees:id,name',
            ])
            ->orderBy('game_date')
            ->get([
                'id',
                'gameweek_id',
                'home_team_id',
                'away_team_id',
                'stadium_id',
                'game_date',
                'home_score',
                'away_score',
                'status',
            ])
            ->map(function ($game) {
                // Get main referee
                $mainReferee = $game->referees->where('pivot.role', 'main_referee')->first();

                // Get team logo URL helper function
                $getTeamLogoUrl = function (?string $logoPath): ?string {
                    if (! $logoPath) {
                        return null;
                    }

                    // If the logo path starts with '/', it's an old format (public path)
                    if (str_starts_with($logoPath, '/')) {
                        return asset($logoPath);
                    }

                    // Otherwise, it's a storage path
                    return asset('storage/'.$logoPath);
                };

                return [
                    'id' => $game->id,
                    'gameweek_id' => $game->gameweek_id,
                    'homeTeam' => [
                        'id' => $game->homeTeam->id,
                        'name' => $game->homeTeam->name,
                        'short_name' => $game->homeTeam->short_name,
                        'logo' => $getTeamLogoUrl($game->homeTeam->logo),
                        'shirt' => $game->homeTeam->shirt,
                    ],
                    'awayTeam' => [
                        'id' => $game->awayTeam->id,
                        'name' => $game->awayTeam->name,
                        'short_name' => $game->awayTeam->short_name,
                        'logo' => $getTeamLogoUrl($game->awayTeam->logo),
                        'shirt' => $game->awayTeam->shirt,
                    ],
                    'game_date' => $game->game_date,
                    'home_score' => $game->home_score,
                    'away_score' => $game->away_score,
                    'status' => $game->status->value,
                    'stadium' => $game->stadium ? [
                        'id' => $game->stadium->id,
                        'name' => $game->stadium->name,
                    ] : null,
                    'referee' => $mainReferee ? [
                        'id' => $mainReferee->id,
                        'name' => $mainReferee->name,
                    ] : null,
                ];
            });

        return Inertia::render('FantasyTeam/Points', [
            'squadPlayers' => $squadPlayers,
            'startingPlayerIds' => $startingPlayerIds,
            'fantasyTeam' => [
                'id' => $fantasyTeam->id,
                'name' => $fantasyTeam->name,
                'kit_type' => $fantasyTeam->kit_type,
                'kit_primary_color' => $fantasyTeam->kit_primary_color,
                'kit_secondary_color' => $fantasyTeam->kit_secondary_color,
            ],
            'gameweeks' => $gameweeks->map(fn ($gw) => [
                'id' => $gw->id,
                'name' => $gw->name,
            ]),
            'selectedGameweek' => [
                'id' => $selectedGameweek->id,
                'name' => $selectedGameweek->name,
            ],
            'fixtures' => $gameweekFixtures,
            'fantasyStats' => $fantasyStats,
            'playerPerformances' => $playerPerformances,
        ]);
    }
}

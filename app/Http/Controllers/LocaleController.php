<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use App\Models\Tenant;

class LocaleController extends Controller
{
    public function setLocale(Request $request)
    {
        $request->validate([
            'locale' => 'required|string',
        ]);

        $locale = $request->input('locale');
        $tenant = Tenant::current();

        if ($tenant && in_array($locale, $tenant->available_languages)) {
            session(['locale' => $locale]);
        }

        return Redirect::back();
    }
    //
}

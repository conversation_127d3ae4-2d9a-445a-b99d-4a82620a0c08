<?php

namespace App\Http\Controllers;

use App\Models\OtpCode;
use App\Models\User;
use App\Services\SmsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class AuthController extends Controller
{
    protected $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * Show the onboarding screen
     */
    public function showOnboarding()
    {
        return Inertia::render('Auth/Onboarding');
    }

    /**
     * Handle phone number submission
     */
    public function submitPhone(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^[0-9+\-\s()]+$/',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $phone = $this->smsService->formatPhoneNumber($request->phone);
        $user = User::where('phone', $phone)->first();

        if ($user && $user->profile_completed) {
            // Existing user with completed profile - send OTP
            $otpCode = OtpCode::generateForPhone($phone);
            $this->smsService->sendOtp($phone, $otpCode->code);

            return redirect()->route('auth.otp.show')->with([
                'phone' => $phone,
                'message' => 'OTP code sent to your phone number.',
            ]);
        } else {
            // New user or incomplete profile - redirect to profile completion
            return redirect()->route('auth.profile.show')->with([
                'phone' => $phone,
                'is_new_user' => ! $user,
            ]);
        }
    }

    /**
     * Show profile completion screen
     */
    public function showProfile()
    {
        if (! session('phone')) {
            return redirect()->route('auth.onboarding');
        }

        session()->reflash();

        return Inertia::render('Auth/ProfileCompletion', [
            'phone' => session('phone'),
            'is_new_user' => session('is_new_user', false),
        ]);
    }

    /**
     * Handle profile completion
     */
    public function storeProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'date_of_birth' => 'required|date|before_or_equal:today',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $phone = session('phone');

        if (! $phone) {
            return redirect()->route('auth.onboarding');
        }

        // Create or update user
        $user = User::updateOrCreate(
            ['phone' => $phone],
            [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'date_of_birth' => $request->date_of_birth,
                'profile_completed' => true,
            ]
        );

        // Generate and send OTP
        $otpCode = OtpCode::generateForPhone($phone);
        $this->smsService->sendOtp($phone, $otpCode->code);

        return redirect()->route('auth.otp.show')->with([
            'phone' => $phone,
            'message' => 'Profile completed! OTP code sent to your phone number.',
        ]);
    }

    /**
     * Show OTP verification screen
     */
    public function showOtp()
    {
        if (! session('phone')) {
            return redirect()->route('auth.onboarding');
        }

        // Keep session data available for subsequent requests
        session()->keep(['phone', 'message', 'is_new_user']);

        return Inertia::render('Auth/OtpVerification', [
            'phone' => session('phone'),
            'message' => session('message'),
        ]);
    }

    /**
     * Handle OTP verification
     */
    public function verifyOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'otp' => 'required|string|size:6',
        ]);
        $phone = session('phone');

        if (! $phone) {
            return redirect()->route('auth.onboarding');
        }

        if ($validator->fails()) {
            // Ensure session data persists for next request
            session()->reflash();

            return Inertia::render('Auth/OtpVerification', [
                'phone' => $phone,
                'message' => session('message'),
                'errors' => $validator->errors(),
            ]);
        }

        if (OtpCode::verify($phone, $request->otp)) {
            $user = User::where('phone', $phone)->first();

            if ($user) {
                $user->update(['phone_verified_at' => now()]);
                Auth::login($user);

                // Clear session data
                session()->forget(['phone', 'message', 'is_new_user']);

                return redirect()->intended(route('home'));
            }
        }

        // Ensure session data persists for next request
        session()->reflash();

        return Inertia::render('Auth/OtpVerification', [
            'phone' => $phone,
            'message' => session('message'),
            'errors' => ['otp' => 'Invalid or expired OTP code.'],
        ]);
    }

    public function resendOtp(Request $request)
    {
        $phone = session('phone');
        if (! $phone) {
            return redirect()->route('auth.onboarding');
        }

        // Ensure session data persists for next request
        session()->reflash();

        $otpCode = OtpCode::generateForPhone($phone);
        $this->smsService->sendOtp($phone, $otpCode->code);

        return back()->with('message', 'New OTP code sent to your phone number.');
    }

    /**
     * Logout user
     */
    public function logout()
    {
        Auth::logout();

        return redirect()->route('auth.onboarding');
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\Competition;
use App\Models\Tenant;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class CompetitionController extends Controller
{
    /**
     * Set the current competition in the session
     */
    public function setCurrent(Request $request, Competition $competition): RedirectResponse
    {
        $tenant = Tenant::current();

        // Verify the competition belongs to the current tenant
        if (! $tenant || ! $tenant->competitions()->where('id', $competition->id)->exists()) {
            return back()->with('error', 'Competition not found or not accessible.');
        }

        // Set the current competition in session
        $request->session()->put('current_competition_id', $competition->id);

        return back()->with('success', "Competition switched to {$competition->name}");
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\Player;
use App\Services\FantasyContextService;
use App\Services\PlayerDataService;
use App\Services\SquadManagementService;
use App\Services\TransferService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class TransferController extends Controller
{
    protected FantasyContextService $contextService;

    protected PlayerDataService $playerDataService;

    protected SquadManagementService $squadService;

    protected TransferService $transferService;

    public function __construct(
        FantasyContextService $contextService,
        PlayerDataService $playerDataService,
        SquadManagementService $squadService,
        TransferService $transferService
    ) {
        $this->contextService = $contextService;
        $this->playerDataService = $playerDataService;
        $this->squadService = $squadService;
        $this->transferService = $transferService;
    }

    /**
     * Show the transfers page with current squad data and available players
     */
    public function index(Request $request)
    {
        // Get fantasy context using service
        $contextResult = $this->contextService->getFantasyContextWithErrors($request);
        if (! $contextResult['success']) {
            return redirect()->route('home')->with('error', $contextResult['error']);
        }

        $context = $contextResult['data'];
        $competition = $context['competition'];
        $currentGameweek = $context['currentGameweek'];
        $currentSeason = $context['currentSeason'];

        // Get user's fantasy team for current season
        $fantasyTeam = $this->contextService->getUserFantasyTeam($request);

        if (! $fantasyTeam) {
            return redirect()->route('fantasy-team.create')
                ->with('error', 'Please create your fantasy team first.');
        }

        // Get squad players using service
        $squadPlayers = $this->playerDataService->getSquadPlayersData($fantasyTeam->id, $currentGameweek);

        // Note: Available players are now loaded via AJAX for pagination efficiency

        // Calculate team stats
        $squadValue = $squadPlayers->sum('price');
        $budget = $fantasyTeam->budget; // Use raw budget value from database

        // Get number of free transfers for this gameweek
        $freeTransfers = $currentGameweek->free_transfers ?? 1;

        // Get starting players from the squad data to avoid an extra query
        $squadFantasyPlayers = $this->playerDataService->getSquadPlayers($fantasyTeam->id, $currentGameweek);
        $startingPlayerIds = $squadFantasyPlayers->filter(function ($fp) {
            return $fp->fantasyTeamLineups->isNotEmpty() && $fp->fantasyTeamLineups->first()->position === 'starting';
        })->pluck('player_id')->toArray();

        return Inertia::render('FantasyTeam/Transfers', [
            'competition' => $competition,
            'season' => $currentSeason,
            'gameweek' => $currentGameweek,
            'fantasyTeam' => $fantasyTeam,
            'currentSquad' => $squadPlayers,
            'budget' => $budget,
            'freeTransfers' => $freeTransfers,
            'startingPlayerIds' => $startingPlayerIds,
        ]);
    }

    /**
     * AJAX endpoint for paginated available players (for transfers)
     */
    public function availablePlayers(Request $request)
    {
        try {

            $user = Auth::user();

            if (! $user) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }

            // Get fantasy context using service
            $contextResult = $this->contextService->getFantasyContextWithErrors($request);
            if (! $contextResult['success']) {
                return response()->json(['error' => $contextResult['error']], 400);
            }

            $context = $contextResult['data'];
            $competition = $context['competition'];
            $currentGameweek = $context['currentGameweek'];
            $currentSeason = $context['currentSeason'];

            // Get user's fantasy team
            $fantasyTeam = $this->contextService->getUserFantasyTeam($request);
            if (! $fantasyTeam) {
                return response()->json(['error' => 'Fantasy team not found'], 404);
            }

            // Set locale for translations
            if ($request->has('locale')) {
                app()->setLocale($request->get('locale'));
            }

            // Get filters from request
            $position = $request->get('position', 'all');
            $team = $request->get('team', 'all');
            $sort = $request->get('sort', 'name_asc');
            $maxPrice = $request->get('maxPrice');

            // Get current squad player IDs to exclude from available players
            $squadPlayers = $this->playerDataService->getSquadPlayers($fantasyTeam->id, $currentGameweek);
            $currentSquadPlayerIds = $squadPlayers->pluck('player_id')->toArray();

            // Build query for available players (excluding current squad)
            $query = \App\Models\Player::whereHas('teams', function ($teamQuery) use ($currentSeason) {
                $teamQuery->whereHas('seasons', function ($seasonQuery) use ($currentSeason) {
                    $seasonQuery->where('seasons.id', $currentSeason->id);
                });
            })
                ->whereNotIn('id', $currentSquadPlayerIds) // Exclude current squad players
                ->with(['teams', 'marketValues' => function ($query) {
                    $query->latest('created_at')->limit(1);
                }]);

            // Apply position filter
            if ($position !== 'all') {
                $query->where('position', $position);
            }

            // Apply team filter
            if ($team !== 'all') {
                $query->whereHas('teams', function ($teamQuery) use ($team) {
                    $teamQuery->where('name', $team);
                });
            }

            // Apply price filter
            if ($maxPrice) {
                $maxPriceInCents = $maxPrice * 1000000; // Convert to cents
                $query->whereHas('marketValues', function ($marketQuery) use ($maxPriceInCents) {
                    $marketQuery->where('market_value', '<=', $maxPriceInCents);
                });
            }

            // Apply sorting (only name sorting can be done at DB level)
            if ($sort === 'name_asc') {
                $query->orderBy('name', 'asc');
            } elseif ($sort === 'name_desc') {
                $query->orderBy('name', 'desc');
            }

            // Use Laravel's built-in pagination
            $players = $query->paginate(14);

            // Transform the paginated data using the PlayerDataService to include team_data
            $players->getCollection()->transform(function ($player) use ($currentGameweek) {
                return $this->playerDataService->transformPlayerData($player, $currentGameweek);
            });

            // For price sorting, we need to handle it after pagination due to relationship complexity
            if (in_array($sort, ['price_asc', 'price_desc'])) {
                $collection = $players->getCollection();
                if ($sort === 'price_asc') {
                    $collection = $collection->sortBy('market_value');
                } else {
                    $collection = $collection->sortByDesc('market_value');
                }
                $players->setCollection($collection->values());
            }

            // Get all teams from current season for filter dropdown with logos
            $allTeams = \App\Models\Team::whereHas('seasons', function ($query) use ($currentSeason) {
                $query->where('seasons.id', $currentSeason->id);
            })
                ->get(['name', 'logo'])
                ->sortBy('name')
                ->values()
                ->map(function ($team) {
                    return [
                        'name' => $team->name,
                        'logo' => $team->logo,
                    ];
                });

            return response()->json([
                'data' => $players->items(),
                'current_page' => $players->currentPage(),
                'last_page' => $players->lastPage(),
                'per_page' => $players->perPage(),
                'total' => $players->total(),
                'from' => $players->firstItem(),
                'to' => $players->lastItem(),
                'path' => $players->path(),
                'prev_page_url' => $players->previousPageUrl(),
                'next_page_url' => $players->nextPageUrl(),
                'links' => $players->linkCollection(),
                'teams' => $allTeams, // All teams for filter dropdown
            ]);
        } catch (\Exception $e) {

            return response()->json([
                'error' => 'Internal server error',
                'message' => $e->getMessage(),
                'debug' => config('app.debug') ? $e->getTraceAsString() : null,
            ], 500);
        }
    }

    /**
     * Process player transfers
     */
    // here we validate the transfer count
    public function processTransfers(Request $request)
    {
        // In transfer mode, we only validate squad changes, not captain/vice-captain
        $validated = $request->validate([
            'selected_players' => 'required|array|size:15',
            'selected_players.*' => 'required|exists:players,id',
            'lineup_players' => 'required|array|size:11',
            'lineup_players.*' => 'required|exists:players,id',
            'transfers_made' => 'required|integer|min:0',
        ]);

        // Get fantasy context using service
        $contextResult = $this->contextService->getFantasyContextWithErrors($request);
        if (! $contextResult['success']) {
            return response()->json(['error' => $contextResult['error']], 400);
        }
        $context = $contextResult['data'];
        $currentGameweek = $context['currentGameweek'];

        // Get user's fantasy team
        $fantasyTeam = $this->contextService->getUserFantasyTeam($request);

        if (! $fantasyTeam) {
            return response()->json(['error' => 'Fantasy team not found'], 404);
        }

        // Get current squad from the service to leverage caching
        $currentSquad = $this->playerDataService->getSquadPlayers($fantasyTeam->id, $currentGameweek);

        $currentSquadPlayerIds = $currentSquad->pluck('player_id')->toArray();
        $newSquadPlayerIds = $validated['selected_players'];

        // Calculate players added and removed
        $playersAdded = array_values(array_diff($newSquadPlayerIds, $currentSquadPlayerIds));
        $playersRemoved = array_values(array_diff($currentSquadPlayerIds, $newSquadPlayerIds));

        // Check if the number of transfers matches
        if (count($playersAdded) !== count($playersRemoved)) {
            return response()->json(['error' => 'Transfer count mismatch'], 400);
        }

        // Validate transfers count
        $freeTransfers = $currentGameweek->free_transfers ?? 1;
        $actualTransfersCount = count($playersAdded);

        // The transfers_made from frontend should match the actual number of transfers
        if ($validated['transfers_made'] !== $actualTransfersCount) {
            return response()->json(['error' => 'Transfer count mismatch between frontend and backend'], 400);
        }

        // Get existing captain and vice-captain from the squad to preserve them
        $captain = $currentSquad->firstWhere('is_captain', true);
        $viceCaptain = $currentSquad->firstWhere('is_vice_captain', true);

        $currentCaptainId = $captain ? $captain->player_id : null;
        $currentViceCaptainId = $viceCaptain ? $viceCaptain->player_id : null;

        // Add preserved captain/vice-captain to validated data
        $validated['captain_id'] = $currentCaptainId;
        $validated['vice_captain_id'] = $currentViceCaptainId;

        // Calculate squad value and transfer cost
        $transferPoints = max(0, count($playersAdded) - $freeTransfers) * 4; // 4 points per transfer above free limit

        // Validate team budget
        $newPlayers = Player::whereIn('id', $newSquadPlayerIds)
            ->with(['marketValues' => function ($query) use ($currentGameweek) {
                $query->where('gameweek_id', $currentGameweek->id);
            }])
            ->get();

        $newSquadValue = $newPlayers->sum(function ($player) use ($currentGameweek) {
            return $player->getMarketValueForGameweek($currentGameweek->id) ?? 0;
        });

        if ($newSquadValue > $fantasyTeam->budget) {
            return response()->json(['error' => 'Not enough budget for these transfers'], 400);
        }

        try {
            // Use transfer service to handle comprehensive transfer processing
            // This includes squad updates, transfer recording, and penalty tracking
            $this->transferService->processTransfers(
                $fantasyTeam,
                $currentGameweek,
                $validated,
                $newSquadPlayerIds,
                $playersAdded,
                $playersRemoved,
                $newSquadValue
            );

            return response()->json([
                'message' => 'Transfers processed successfully',
                'success' => true,
                'transfers_made' => count($playersAdded),
                'points_deducted' => max(0, count($playersAdded) - ($currentGameweek->free_transfers ?? 1)) * 4,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to process transfers: '.$e->getMessage(),
                'success' => false,
            ], 500);
        }
    }
}

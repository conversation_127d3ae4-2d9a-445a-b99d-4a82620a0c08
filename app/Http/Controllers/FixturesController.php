<?php

namespace App\Http\Controllers;

use App\Services\FantasyContextService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class FixturesController extends Controller
{
    protected $fantasyContextService;

    public function __construct(FantasyContextService $fantasyContextService)
    {
        $this->fantasyContextService = $fantasyContextService;
    }

    /**
     * Display the fixtures page with dynamic data
     */
    public function index(Request $request): Response
    {
        $contextResult = $this->fantasyContextService->getFantasyContextWithErrors($request);

        if (! $contextResult['success']) {
            return Inertia::render('Fixtures', [
                'error' => $contextResult['error'],
                'fixtures' => [],
                'gameweeks' => [],
                'currentGameweek' => null,
            ]);
        }

        $context = $contextResult['data'];
        $currentGameweek = $context['currentGameweek'];
        $currentSeasonPhase = $context['currentSeasonPhase'];

        // Get all gameweeks for the current season phase ordered by start_date
        $allGameweeks = $currentSeasonPhase->gameweeks()
            ->orderBy('start_date')
            ->get(['id', 'name', 'start_date', 'end_date', 'status']);

        // Get the smart gameweek pagination (previous + current + upcoming gameweeks)
        $paginatedGameweeks = $this->getSmartGameweekPagination($allGameweeks, $currentGameweek, 7);

        // Get fixtures for the paginated gameweeks
        $fixtures = [];
        foreach ($paginatedGameweeks as $gameweek) {
            $games = $gameweek->games()
                ->with([
                    'homeTeam:id,name,short_name,logo,shirt',
                    'awayTeam:id,name,short_name,logo,shirt',
                    'stadium:id,name',
                    'referees:id,name',
                ])
                ->orderBy('game_date')
                ->get([
                    'id',
                    'gameweek_id',
                    'home_team_id',
                    'away_team_id',
                    'stadium_id',
                    'game_date',
                    'home_score',
                    'away_score',
                    'status',
                ]);
            $fixtures[$gameweek->id] = $games->map(function ($game) {
                // Get main referee
                $mainReferee = $game->referees->where('pivot.role', 'main_referee')->first();

                return [
                    'id' => $game->id,
                    'gameweek_id' => $game->gameweek_id,
                    'homeTeam' => [
                        'id' => $game->homeTeam->id,
                        'name' => $game->homeTeam->name,
                        'short_name' => $game->homeTeam->short_name,
                        'logo' => $this->getTeamLogoUrl($game->homeTeam->logo),
                        'shirt' => $game->homeTeam->shirt,
                    ],
                    'awayTeam' => [
                        'id' => $game->awayTeam->id,
                        'name' => $game->awayTeam->name,
                        'short_name' => $game->awayTeam->short_name,
                        'logo' => $this->getTeamLogoUrl($game->awayTeam->logo),
                        'shirt' => $game->awayTeam->shirt,
                    ],
                    'game_date' => $game->game_date,
                    'home_score' => $game->home_score,
                    'away_score' => $game->away_score,
                    'status' => $game->status->value,
                    'stadium' => $game->stadium ? [
                        'id' => $game->stadium->id,
                        'name' => $game->stadium->name,
                    ] : null,
                    'referee' => $mainReferee ? [
                        'id' => $mainReferee->id,
                        'name' => $mainReferee->name,
                    ] : null,
                ];
            })->groupBy(function ($game) {
                return (new \DateTime($game['game_date']))->format('Y-m-d');
            })->toArray();
        }

        return Inertia::render('Fixtures', [
            'fixtures' => $fixtures,
            'gameweeks' => $paginatedGameweeks->map(function ($gameweek) {
                return [
                    'id' => $gameweek->id,
                    'name' => $gameweek->name,
                    'startDate' => $gameweek->start_date,
                    'endDate' => $gameweek->end_date,
                    'status' => $gameweek->status,
                ];
            }),
            'allGameweeks' => $allGameweeks->map(function ($gameweek) {
                return [
                    'id' => $gameweek->id,
                    'name' => $gameweek->name,
                    'startDate' => $gameweek->start_date,
                    'endDate' => $gameweek->end_date,
                    'status' => $gameweek->status,
                ];
            }),
            'pagination' => [
                'currentIndex' => $allGameweeks->search(function ($gw) use ($currentGameweek) {
                    return $gw->id === $currentGameweek->id;
                }),
                'totalGameweeks' => $allGameweeks->count(),
                'hasNext' => $this->hasNextGameweeks($allGameweeks, $currentGameweek),
                'hasPrevious' => $this->hasPreviousGameweeks($allGameweeks, $currentGameweek),
            ],
            'currentGameweek' => [
                'id' => $currentGameweek->id,
                'name' => $currentGameweek->name,
                'startDate' => $currentGameweek->start_date,
                'endDate' => $currentGameweek->end_date,
                'status' => $currentGameweek->status,
            ],
            'competition' => [
                'id' => $context['competition']->id,
                'name' => $context['competition']->name,
            ],
            'season' => [
                'id' => $context['currentSeason']->id,
                'name' => $context['currentSeason']->name,
            ],
        ]);
    }

    /**
     * Get smart gameweek pagination ensuring previous, current, and upcoming gameweeks are shown
     */
    private function getSmartGameweekPagination($allGameweeks, $currentGameweek, $maxVisible = 7)
    {
        $currentIndex = $allGameweeks->search(function ($gameweek) use ($currentGameweek) {
            return $gameweek->id === $currentGameweek->id;
        });

        if ($currentIndex === false) {
            // If current gameweek not found, return first few gameweeks
            return $allGameweeks->take($maxVisible);
        }

        $totalGameweeks = $allGameweeks->count();

        // Ensure we show at least 1 previous, current, and 1 upcoming when possible
        $minPrevious = 1;
        $minUpcoming = 1;

        // Calculate how many additional gameweeks we can show
        $remainingSlots = $maxVisible - 1 - $minPrevious - $minUpcoming; // -1 for current
        $extraPrevious = floor($remainingSlots / 2);
        $extraUpcoming = $remainingSlots - $extraPrevious;

        // Calculate desired start and end indices
        $desiredPrevious = $minPrevious + $extraPrevious;
        $desiredUpcoming = $minUpcoming + $extraUpcoming;

        $startIndex = max(0, $currentIndex - $desiredPrevious);
        $endIndex = min($totalGameweeks - 1, $currentIndex + $desiredUpcoming);

        // Adjust if we can't get enough gameweeks on one side
        $actualPrevious = $currentIndex - $startIndex;
        $actualUpcoming = $endIndex - $currentIndex;

        // If we have fewer previous gameweeks than desired, add more upcoming
        if ($actualPrevious < $desiredPrevious) {
            $extraSlots = $desiredPrevious - $actualPrevious;
            $endIndex = min($totalGameweeks - 1, $endIndex + $extraSlots);
        }

        // If we have fewer upcoming gameweeks than desired, add more previous
        if ($actualUpcoming < $desiredUpcoming) {
            $extraSlots = $desiredUpcoming - $actualUpcoming;
            $startIndex = max(0, $startIndex - $extraSlots);
        }

        return $allGameweeks->slice($startIndex, $endIndex - $startIndex + 1)->values();
    }

    /**
     * Check if there are next gameweeks available
     */
    private function hasNextGameweeks($allGameweeks, $currentGameweek)
    {
        $currentIndex = $allGameweeks->search(function ($gameweek) use ($currentGameweek) {
            return $gameweek->id === $currentGameweek->id;
        });

        return $currentIndex !== false && $currentIndex < $allGameweeks->count() - 1;
    }

    /**
     * Check if there are previous gameweeks available
     */
    private function hasPreviousGameweeks($allGameweeks, $currentGameweek)
    {
        $currentIndex = $allGameweeks->search(function ($gameweek) use ($currentGameweek) {
            return $gameweek->id === $currentGameweek->id;
        });

        return $currentIndex !== false && $currentIndex > 0;
    }

    /**
     * Get the proper URL for a team logo
     */
    private function getTeamLogoUrl(?string $logoPath): ?string
    {
        if (! $logoPath) {
            return null;
        }

        // If the logo path starts with '/', it's an old format (public path)
        if (str_starts_with($logoPath, '/')) {
            return asset($logoPath);
        }

        // Otherwise, it's a storage path
        return asset('storage/'.$logoPath);
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EnsurePhoneVerified
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Unauthenticated.'], 401);
            }
            return redirect()->route('auth.onboarding');
        }

        $user = Auth::user();

        // Check if phone is verified and profile is completed
        if (!$user->phone_verified_at || !$user->profile_completed) {
            Auth::logout();
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Phone verification required.'], 401);
            }
            return redirect()->route('auth.onboarding');
        }

        return $next($request);
    }
}

<?php

namespace App\Http\Middleware;

use App\Models\Tenant;
use App\Services\FantasyContextService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Middleware;
use Tighten\Ziggy\Ziggy;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    protected FantasyContextService $fantasyContextService;

    public function __construct(FantasyContextService $fantasyContextService)
    {
        $this->fantasyContextService = $fantasyContextService;
    }

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        $competitions = $this->fantasyContextService->getTenantCompetitions() ?? [];
        $currentCompetitionId = $request->session()->get('current_competition_id');
        $currentCompetition = $competitions->firstWhere('id', $currentCompetitionId);

        return [
            ...parent::share($request),
            'app' => [
                'url' => config('app.url'),
                'locale' => fn () => app()->getLocale(),
                'available_languages' => fn () => Tenant::current()?->available_languages ?? [],
                'translations' => function () {
                    $locale = app()->getLocale();
                    $path = resource_path("lang/{$locale}.json");

                    if (! file_exists($path)) {
                        return [];
                    }

                    return json_decode(file_get_contents($path), true);
                },
            ],
            'auth' => [
                'user' => fn () => Auth::user() ? [
                    'id' => Auth::user()->id,
                    'name' => Auth::user()->name,
                    'first_name' => Auth::user()->first_name,
                    'last_name' => Auth::user()->last_name,
                    'email' => Auth::user()->email,
                    'avatar' => Auth::user()->avatar,
                    'wallet_balance' => Auth::user()->balance,
                ] : null,
            ],
            'tenant' => function () {
                $tenant = Tenant::current();

                return $tenant ? [
                    'id' => $tenant->id,
                    'name' => $tenant->name,
                    'domain' => $tenant->domain,
                    'database' => $tenant->database,
                    'logo' => $tenant->logo,
                    'favicon' => $tenant->favicon,
                    'background_image' => $tenant->background_image,
                    'primary_color' => $tenant->primary_color,
                    'secondary_color' => $tenant->secondary_color,
                    'created_at' => $tenant->created_at,
                    'updated_at' => $tenant->updated_at,
                ] : null;
            },
            'competitions' => $competitions,
            'currentCompetition' => $currentCompetition,
            'flash' => [
                'message' => fn () => $request->session()->get('message'),
                'error' => fn () => $request->session()->get('error'),
                'success' => fn () => $request->session()->get('success'),
            ],
            'ziggy' => fn () => [
                ...(new Ziggy)->toArray(),
                'location' => $request->url(),
            ],
        ];
    }
}

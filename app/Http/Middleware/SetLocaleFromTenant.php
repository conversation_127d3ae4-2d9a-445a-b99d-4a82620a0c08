<?php

namespace App\Http\Middleware;

use App\Models\Tenant;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetLocaleFromTenant
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $tenant = Tenant::current();
        if (! $tenant || ! is_array($tenant->available_languages) || count($tenant->available_languages) === 0) {
            return $next($request);
        }

        $availableLanguages = $tenant->available_languages;
        $sessionLocale = session('locale');

        if ($sessionLocale && in_array($sessionLocale, $availableLanguages)) {
            app()->setLocale($sessionLocale);
        } else {
            $defaultLocale = $availableLanguages[0];
            app()->setLocale($defaultLocale);
            session(['locale' => $defaultLocale]);
        }

        return $next($request);
    }
}

<?php

namespace App\Http\Middleware;

use App\Services\FantasyContextService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureCompetitionExists
{
    public function __construct(protected FantasyContextService $fantasyContextService)
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $competitions = $this->fantasyContextService->getTenantCompetitions();

        // If no competitions are found for the tenant, abort.
        if ($competitions === null || $competitions->isEmpty()) {
            abort(403, 'No competition set for this tenant. Please contact your administrator.');
        }

        // Check if current competition is set in session
        $currentCompetitionId = $request->session()->get('current_competition_id');

        // If no current competition is set, or the set competition doesn't exist for this tenant
        if (! $currentCompetitionId || ! $competitions->contains('id', $currentCompetitionId)) {
            // Set the first competition as current
            $firstCompetition = $competitions->first();
            $request->session()->put('current_competition_id', $firstCompetition->id);
        }

        return $next($request);
    }
}

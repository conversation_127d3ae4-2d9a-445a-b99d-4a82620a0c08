<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum CompetitionStatus: string implements HasLabel, HasColor, HasIcon, HasDescription
{
    case UPCOMING = 'upcoming';
    case ONGOING = 'ongoing';
    case COMPLETED = 'completed';
    case ARCHIVED = 'archived';

    /**
     * Get display label for the enum value (Filament interface)
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::UPCOMING => 'Upcoming',
            self::ONGOING => 'Ongoing',
            self::COMPLETED => 'Completed',
            self::ARCHIVED => 'Archived',
        };
    }

    /**
     * Get color for Filament UI display
     */
    public function getColor(): string|array|null
    {
        return match ($this) {
            self::UPCOMING => 'info',
            self::ONGOING => 'success',
            self::COMPLETED => 'warning',
            self::ARCHIVED => 'gray',
        };
    }

    /**
     * Get icon for Filament UI display
     */
    public function getIcon(): ?string
    {
        return match ($this) {
            self::UPCOMING => 'heroicon-m-clock',
            self::ONGOING => 'heroicon-m-play',
            self::COMPLETED => 'heroicon-m-check-circle',
            self::ARCHIVED => 'heroicon-m-archive-box',
        };
    }

    /**
     * Get description for the status (Filament interface)
     */
    public function getDescription(): ?string
    {
        return match ($this) {
            self::UPCOMING => 'Competition is scheduled to start in the future.',
            self::ONGOING => 'Competition is currently active with ongoing matches.',
            self::COMPLETED => 'Competition has finished but is still accessible.',
            self::ARCHIVED => 'Competition is archived and no longer active.',
        };
    }

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Check if the status allows modifications
     */
    public function allowsModifications(): bool
    {
        return match ($this) {
            self::UPCOMING, self::ONGOING => true,
            self::COMPLETED, self::ARCHIVED => false,
        };
    }

    /**
     * Check if the status allows new registrations
     */
    public function allowsRegistrations(): bool
    {
        return match ($this) {
            self::UPCOMING => true,
            self::ONGOING, self::COMPLETED, self::ARCHIVED => false,
        };
    }
}

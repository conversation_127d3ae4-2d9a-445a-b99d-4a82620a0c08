<?php

namespace App\Enums;

use Filament\Support\Contracts\HasIcon;

enum FantasyIcon: string implements HasIcon
{
    case Stadium = 'gmdi-stadium-o';
    case Player = 'gmdi-emoji-people-o';
    case Kickoff = 'gmdi-sports-o';
    case Ball = 'gmdi-sports-soccer-o';
    case Score = 'gmdi-sports-soccer-o';
    case Team = 'gmdi-groups-o';
    case Calendar = 'gmdi-calendar-month-o';

    public function getIcon(): ?string
    {
        return $this->value;
    }
}

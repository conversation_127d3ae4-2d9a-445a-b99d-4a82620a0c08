<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum RefereeRole: string implements HasColor, HasDescription, HasIcon, HasLabel
{
    case MAIN_REFEREE = 'main_referee';
    case ASSISTANT_REFEREE_1 = 'assistant_referee_1';
    case ASSISTANT_REFEREE_2 = 'assistant_referee_2';

    /**
     * Get display label for the enum value (Filament interface)
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::MAIN_REFEREE => 'Main Referee',
            self::ASSISTANT_REFEREE_1 => 'Assistant Referee 1',
            self::ASSISTANT_REFEREE_2 => 'Assistant Referee 2',
        };
    }

    /**
     * Get color for Filament UI display
     */
    public function getColor(): string|array|null
    {
        return match ($this) {
            self::MAIN_REFEREE => 'danger',
            self::ASSISTANT_REFEREE_1 => 'warning',
            self::ASSISTANT_REFEREE_2 => 'warning',
        };
    }

    /**
     * Get icon for Filament UI display
     */
    public function getIcon(): ?string
    {
        return match ($this) {
            self::MAIN_REFEREE => 'heroicon-m-user-circle',
            self::ASSISTANT_REFEREE_1 => 'heroicon-m-flag',
            self::ASSISTANT_REFEREE_2 => 'heroicon-m-flag',
        };
    }

    /**
     * Get description for the role (Filament interface)
     */
    public function getDescription(): ?string
    {
        return match ($this) {
            self::MAIN_REFEREE => 'The main referee who controls the match and makes final decisions.',
            self::ASSISTANT_REFEREE_1 => 'Assistant referee who helps with offside calls and other decisions.',
            self::ASSISTANT_REFEREE_2 => 'Assistant referee who helps with offside calls and other decisions.',
        };
    }

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Check if this is a main referee role
     */
    public function isMainReferee(): bool
    {
        return $this === self::MAIN_REFEREE;
    }

    /**
     * Check if this is an assistant referee role
     */
    public function isAssistantReferee(): bool
    {
        return in_array($this, [self::ASSISTANT_REFEREE_1, self::ASSISTANT_REFEREE_2]);
    }

    /**
     * Get the required roles for a complete referee team
     */
    public static function getRequiredRoles(): array
    {
        return [
            self::MAIN_REFEREE,
            self::ASSISTANT_REFEREE_1,
            self::ASSISTANT_REFEREE_2,
        ];
    }
}

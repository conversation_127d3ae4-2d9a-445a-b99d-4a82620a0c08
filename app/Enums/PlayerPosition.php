<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum PlayerPosition: string implements HasColor, HasDescription, HasIcon, HasLabel
{
    case GOALKEEPER = 'GK';
    case DEFENDER = 'DEF';
    case MIDFIELDER = 'MID';
    case FORWARD = 'FWD';

    /**
     * Get display label for the enum value (Filament interface)
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::GOALKEEPER => 'Goalkeeper',
            self::DEFENDER => 'Defender',
            self::MIDFIELDER => 'Midfielder',
            self::FORWARD => 'Forward',
        };
    }

    /**
     * Get color for Filament UI display
     */
    public function getColor(): string|array|null
    {
        return match ($this) {
            self::GOALKEEPER => 'warning',
            self::DEFENDER => 'info',
            self::MIDFIELDER => 'success',
            self::FORWARD => 'danger',
        };
    }

    /**
     * Get icon for Filament UI display
     */
    public function getIcon(): ?string
    {
        return match ($this) {
            self::GOALKEEPER => 'heroicon-m-shield-check',
            self::DEFENDER => 'heroicon-m-shield-exclamation',
            self::MIDFIELDER => 'heroicon-m-arrow-path',
            self::FORWARD => 'heroicon-m-bolt',
        };
    }

    /**
     * Get description for the position (Filament interface)
     */
    public function getDescription(): ?string
    {
        return match ($this) {
            self::GOALKEEPER => 'Protects the goal and prevents the opposing team from scoring.',
            self::DEFENDER => 'Defends against opposing attackers and helps build play from the back.',
            self::MIDFIELDER => 'Controls the game flow, distributes passes, and links defense with attack.',
            self::FORWARD => 'Primary goal scorer and creates attacking opportunities.',
        };
    }

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get required count for this position in a valid squad
     */
    public function requiredCount(): int
    {
        return match ($this) {
            self::GOALKEEPER => 2,
            self::DEFENDER => 5,
            self::MIDFIELDER => 5,
            self::FORWARD => 3,
        };
    }

    /**
     * Get all position requirements as an array
     */
    public static function requirements(): array
    {
        return [
            self::GOALKEEPER->value => self::GOALKEEPER->requiredCount(),
            self::DEFENDER->value => self::DEFENDER->requiredCount(),
            self::MIDFIELDER->value => self::MIDFIELDER->requiredCount(),
            self::FORWARD->value => self::FORWARD->requiredCount(),
        ];
    }
}

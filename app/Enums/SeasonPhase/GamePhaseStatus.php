<?php

namespace App\Enums\SeasonPhase;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum GamePhaseStatus: string implements HasColor, HasDescription, HasIcon, HasLabel
{
    case UPCOMING = 'upcoming';
    case ONGOING = 'ongoing';
    case COMPLETED = 'completed';

    /**
     * Get display label for the enum value (Filament interface)
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::UPCOMING => 'Upcoming',
            self::ONGOING => 'Ongoing',
            self::COMPLETED => 'Completed',
        };
    }

    /**
     * Get color for Filament UI display
     */
    public function getColor(): string|array|null
    {
        return match ($this) {
            self::UPCOMING => 'gray',
            self::ONGOING => 'warning',
            self::COMPLETED => 'success',
        };
    }

    /**
     * Get icon for Filament UI display
     */
    public function getIcon(): ?string
    {
        return match ($this) {
            self::UPCOMING => 'heroicon-m-clock',
            self::ONGOING => 'heroicon-m-play',
            self::COMPLETED => 'heroicon-m-check-badge',
        };
    }

    /**
     * Get description for the game phase status (Filament interface)
     */
    public function getDescription(): ?string
    {
        return match ($this) {
            self::UPCOMING => 'The game has not started yet.',
            self::ONGOING => 'The game is currently in progress.',
            self::COMPLETED => 'The game has finished.',
        };
    }

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all cases with their labels for form options
     */
    public static function options(): array
    {
        return collect(self::cases())->map(fn ($case) => [
            'value' => $case->value,
            'label' => __($case->getLabel()),
        ])->toArray();
    }
}

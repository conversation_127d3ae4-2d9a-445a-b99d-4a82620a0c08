<?php

namespace App\Enums\SeasonPhase;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum SeasonPhaseFormat: string implements HasColor, HasDescription, HasIcon, HasLabel
{
    case LEAGUE = 'league';
    case CUP = 'cup';

    /**
     * Get display label for the enum value (Filament interface)
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::LEAGUE => 'League',
            self::CUP => 'Cup',
        };
    }

    /**
     * Get color for Filament UI display
     */
    public function getColor(): string|array|null
    {
        return match ($this) {
            self::LEAGUE => 'info',
            self::CUP => 'danger',
        };
    }

    /**
     * Get icon for Filament UI display
     */
    public function getIcon(): ?string
    {
        return match ($this) {
            self::LEAGUE => 'heroicon-m-list-bullet',
            self::CUP => 'heroicon-m-trophy',
        };
    }

    /**
     * Get description for the season phase format (Filament interface)
     */
    public function getDescription(): ?string
    {
        return match ($this) {
            self::LEAGUE => 'Round-robin format where each team plays others.',
            self::CUP => 'Knockout tournament format with elimination rounds.',
        };
    }

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all cases with their labels for form options
     */
    public static function options(): array
    {
        return collect(self::cases())->map(fn ($case) => [
            'value' => $case->value,
            'label' => __($case->getLabel()),
        ])->toArray();
    }
}

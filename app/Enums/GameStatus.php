<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum GameStatus: string implements HasColor, HasDescription, HasIcon, HasLabel
{
    case SCHEDULED = 'scheduled';
    case IN_PROGRESS = 'in_progress';
    case FINISHED = 'finished';
    case POSTPONED = 'postponed';
    case CANCELED = 'canceled';

    /**
     * Get display label for the enum value (Filament interface)
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::SCHEDULED => 'Scheduled',
            self::IN_PROGRESS => 'In Progress',
            self::FINISHED => 'Finished',
            self::POSTPONED => 'Postponed',
            self::CANCELED => 'Canceled',
        };
    }

    /**
     * Get color for Filament UI display
     */
    public function getColor(): string|array|null
    {
        return match ($this) {
            self::SCHEDULED => 'info',
            self::IN_PROGRESS => 'warning',
            self::FINISHED => 'success',
            self::POSTPONED => 'gray',
            self::CANCELED => 'danger',
        };
    }

    /**
     * Get icon for Filament UI display
     */
    public function getIcon(): ?string
    {
        return match ($this) {
            self::SCHEDULED => 'heroicon-m-calendar',
            self::IN_PROGRESS => 'heroicon-m-play',
            self::FINISHED => 'heroicon-m-check-circle',
            self::POSTPONED => 'heroicon-m-pause',
            self::CANCELED => 'heroicon-m-x-circle',
        };
    }

    /**
     * Get description for the status (Filament interface)
     */
    public function getDescription(): ?string
    {
        return match ($this) {
            self::SCHEDULED => 'The game is scheduled and waiting to start.',
            self::IN_PROGRESS => 'The game is currently being played.',
            self::FINISHED => 'The game has been completed with final scores.',
            self::POSTPONED => 'The game has been postponed to a later date.',
            self::CANCELED => 'The game has been canceled and will not be played.',
        };
    }

    /**
     * Check if game is currently active
     */
    public function isActive(): bool
    {
        return $this === self::IN_PROGRESS;
    }

    /**
     * Check if game is completed
     */
    public function isCompleted(): bool
    {
        return $this === self::FINISHED;
    }

    /**
     * Check if game is scheduled
     */
    public function isScheduled(): bool
    {
        return $this === self::SCHEDULED;
    }

    /**
     * Check if game is canceled or postponed
     */
    public function isCanceledOrPostponed(): bool
    {
        return in_array($this, [self::CANCELED, self::POSTPONED]);
    }

    /**
     * Check if scores can be entered for this game status
     */
    public function allowsScoreEntry(): bool
    {
        return in_array($this, [self::IN_PROGRESS, self::FINISHED]);
    }

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}

<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum GameweekStatus: string implements HasColor, HasDescription, HasIcon, HasLabel
{
    case UPCOMING = 'upcoming';
    case ONGOING = 'ongoing';
    case COMPLETED = 'completed';

    /**
     * Get display label for the enum value (Filament interface)
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::UPCOMING => 'Upcoming',
            self::ONGOING => 'Ongoing',
            self::COMPLETED => 'Completed',
        };
    }

    /**
     * Get color for Filament UI display
     */
    public function getColor(): string|array|null
    {
        return match ($this) {
            self::UPCOMING => 'info',
            self::ONGOING => 'success',
            self::COMPLETED => 'gray',
        };
    }

    /**
     * Get icon for Filament UI display
     */
    public function getIcon(): ?string
    {
        return match ($this) {
            self::UPCOMING => 'heroicon-m-clock',
            self::ONGOING => 'heroicon-m-play',
            self::COMPLETED => 'heroicon-m-check-circle',
        };
    }

    /**
     * Get description for the status (Filament interface)
     */
    public function getDescription(): ?string
    {
        return match ($this) {
            self::UPCOMING => 'This gameweek has not started yet. Transfers are still allowed.',
            self::ONGOING => 'This gameweek is currently active. No transfers allowed.',
            self::COMPLETED => 'This gameweek has finished. Points have been calculated.',
        };
    }

    /**
     * Check if gameweek is active (ongoing)
     */
    public function isActive(): bool
    {
        return $this === self::ONGOING;
    }

    /**
     * Check if gameweek is finished
     */
    public function isFinished(): bool
    {
        return $this === self::COMPLETED;
    }

    /**
     * Check if gameweek is upcoming
     */
    public function isUpcoming(): bool
    {
        return $this === self::UPCOMING;
    }

    /**
     * Check if transfers are allowed for this gameweek status
     */
    public function allowsTransfers(): bool
    {
        return $this === self::UPCOMING;
    }

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}

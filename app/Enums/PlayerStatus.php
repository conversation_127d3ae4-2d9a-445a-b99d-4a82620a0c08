<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum PlayerStatus: string implements HasColor, HasDescription, HasIcon, HasLabel
{
    case STARTING = 'starting';
    case SUBSTITUTE = 'substitute';
    case SUBSTITUTED_IN = 'substituted_in';
    case SUBSTITUTED_OUT = 'substituted_out';
    case YELLOW_CARD = 'yellow_card';
    case RED_CARD = 'red_card';
    case INJURED = 'injured';
    case NOT_SELECTED = 'not_selected';

    /**
     * Get display label for the enum value (Filament interface)
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::STARTING => 'Starting XI',
            self::SUBSTITUTE => 'Substitute',
            self::SUBSTITUTED_IN => 'Substituted In',
            self::SUBSTITUTED_OUT => 'Substituted Out',
            self::YELLOW_CARD => 'Yellow Card',
            self::RED_CARD => 'Red Card',
            self::INJURED => 'Injured',
            self::NOT_SELECTED => 'Not Selected',
        };
    }

    /**
     * Get color for Filament UI display
     */
    public function getColor(): string|array|null
    {
        return match ($this) {
            self::STARTING => 'success',
            self::SUBSTITUTE => 'info',
            self::SUBSTITUTED_IN => 'success',
            self::SUBSTITUTED_OUT => 'gray',
            self::YELLOW_CARD => 'warning',
            self::RED_CARD => 'danger',
            self::INJURED => 'danger',
            self::NOT_SELECTED => 'gray',
        };
    }

    /**
     * Get icon for Filament UI display
     */
    public function getIcon(): ?string
    {
        return match ($this) {
            self::STARTING => 'heroicon-m-play',
            self::SUBSTITUTE => 'heroicon-m-user',
            self::SUBSTITUTED_IN => 'heroicon-m-arrow-right-on-rectangle',
            self::SUBSTITUTED_OUT => 'heroicon-m-arrow-left-on-rectangle',
            self::YELLOW_CARD => 'heroicon-m-rectangle-stack',
            self::RED_CARD => 'heroicon-m-rectangle-stack',
            self::INJURED => 'heroicon-m-heart',
            self::NOT_SELECTED => 'heroicon-m-x-mark',
        };
    }

    /**
     * Get description for the status (Filament interface)
     */
    public function getDescription(): ?string
    {
        return match ($this) {
            self::STARTING => 'Player is in the starting eleven.',
            self::SUBSTITUTE => 'Player is on the bench as a substitute.',
            self::SUBSTITUTED_IN => 'Player was brought on as a substitute.',
            self::SUBSTITUTED_OUT => 'Player was substituted off the field.',
            self::YELLOW_CARD => 'Player received a yellow card.',
            self::RED_CARD => 'Player received a red card and was sent off.',
            self::INJURED => 'Player is injured and cannot play.',
            self::NOT_SELECTED => 'Player was not selected for the match.',
        };
    }

    /**
     * Check if player is currently on the field
     */
    public function isOnField(): bool
    {
        return in_array($this, [self::STARTING, self::SUBSTITUTED_IN]);
    }

    /**
     * Check if player is available to play
     */
    public function isAvailable(): bool
    {
        return ! in_array($this, [self::RED_CARD, self::INJURED, self::NOT_SELECTED]);
    }

    /**
     * Check if player has received a card
     */
    public function hasCard(): bool
    {
        return in_array($this, [self::YELLOW_CARD, self::RED_CARD]);
    }

    /**
     * Check if player can be substituted
     */
    public function canBeSubstituted(): bool
    {
        return in_array($this, [self::STARTING, self::SUBSTITUTED_IN]) && $this !== self::RED_CARD;
    }

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get statuses for players who can be on the field
     */
    public static function fieldStatuses(): array
    {
        return [self::STARTING, self::SUBSTITUTED_IN];
    }

    /**
     * Get statuses for players who are not playing
     */
    public static function benchStatuses(): array
    {
        return [self::SUBSTITUTE, self::SUBSTITUTED_OUT, self::NOT_SELECTED];
    }
}

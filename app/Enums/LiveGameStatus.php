<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum LiveGameStatus: string implements HasColor, HasDescription, HasIcon, HasLabel
{
    case PRE_MATCH = 'pre_match';
    case LIVE = 'live';
    case HALF_TIME = 'half_time';
    case SECOND_HALF = 'second_half';
    case FULL_TIME = 'full_time';
    case EXTRA_TIME = 'extra_time';
    case PENALTY_SHOOTOUT = 'penalty_shootout';
    case FINISHED = 'finished';
    case SUSPENDED = 'suspended';
    case ABANDONED = 'abandoned';

    /**
     * Get display label for the enum value (Filament interface)
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::PRE_MATCH => 'Pre-Match',
            self::LIVE => 'Live',
            self::HALF_TIME => 'Half Time',
            self::SECOND_HALF => 'Second Half',
            self::FULL_TIME => 'Full Time',
            self::EXTRA_TIME => 'Extra Time',
            self::PENALTY_SHOOTOUT => 'Penalty Shootout',
            self::FINISHED => 'Finished',
            self::SUSPENDED => 'Suspended',
            self::ABANDONED => 'Abandoned',
        };
    }

    /**
     * Get color for Filament UI display
     */
    public function getColor(): string|array|null
    {
        return match ($this) {
            self::PRE_MATCH => 'info',
            self::LIVE => 'success',
            self::HALF_TIME => 'warning',
            self::SECOND_HALF => 'success',
            self::FULL_TIME => 'gray',
            self::EXTRA_TIME => 'warning',
            self::PENALTY_SHOOTOUT => 'danger',
            self::FINISHED => 'gray',
            self::SUSPENDED => 'warning',
            self::ABANDONED => 'danger',
        };
    }

    /**
     * Get icon for Filament UI display
     */
    public function getIcon(): ?string
    {
        return match ($this) {
            self::PRE_MATCH => 'heroicon-m-clock',
            self::LIVE => 'heroicon-m-play',
            self::HALF_TIME => 'heroicon-m-pause',
            self::SECOND_HALF => 'heroicon-m-play',
            self::FULL_TIME => 'heroicon-m-stop',
            self::EXTRA_TIME => 'heroicon-m-forward',
            self::PENALTY_SHOOTOUT => 'heroicon-m-target',
            self::FINISHED => 'heroicon-m-check-circle',
            self::SUSPENDED => 'heroicon-m-exclamation-triangle',
            self::ABANDONED => 'heroicon-m-x-circle',
        };
    }

    /**
     * Get description for the status (Filament interface)
     */
    public function getDescription(): ?string
    {
        return match ($this) {
            self::PRE_MATCH => 'Match has not started yet. Lineups can be set.',
            self::LIVE => 'Match is currently being played.',
            self::HALF_TIME => 'First half has ended. Players are on break.',
            self::SECOND_HALF => 'Second half is being played.',
            self::FULL_TIME => 'Regular time has ended.',
            self::EXTRA_TIME => 'Extra time is being played.',
            self::PENALTY_SHOOTOUT => 'Penalty shootout is in progress.',
            self::FINISHED => 'Match has been completed.',
            self::SUSPENDED => 'Match has been temporarily suspended.',
            self::ABANDONED => 'Match has been abandoned and will not continue.',
        };
    }

    /**
     * Check if match is currently active
     */
    public function isActive(): bool
    {
        return in_array($this, [self::LIVE, self::SECOND_HALF, self::EXTRA_TIME, self::PENALTY_SHOOTOUT]);
    }

    /**
     * Check if match is completed
     */
    public function isCompleted(): bool
    {
        return in_array($this, [self::FINISHED, self::ABANDONED]);
    }

    /**
     * Check if lineups can be modified
     */
    public function allowsLineupChanges(): bool
    {
        return $this === self::PRE_MATCH;
    }

    /**
     * Check if live events can be recorded
     */
    public function allowsLiveEvents(): bool
    {
        return $this->isActive() || $this === self::HALF_TIME;
    }

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}

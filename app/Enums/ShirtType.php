<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum ShirtType: string implements HasColor, HasDescription, HasIcon, HasLabel
{
    case STRIPED = 'striped';
    case FILLED = 'filled';
    case HALVED = 'halved';
    case SASH = 'sash';
    case HOOPS = 'hoops';
    case CHECKERED = 'checkered';

    /**
     * Get display label for the enum value (Filament interface)
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::STRIPED => 'fantasyTeam.shirtTypes.striped',
            self::FILLED => 'fantasyTeam.shirtTypes.filled',
            self::HALVED => 'fantasyTeam.shirtTypes.halved',
            self::SASH => 'fantasyTeam.shirtTypes.sash',
            self::HOOPS => 'fantasyTeam.shirtTypes.hoops',
            self::CHECKERED => 'fantasyTeam.shirtTypes.checkered',
        };
    }

    /**
     * Get color for Filament UI display
     */
    public function getColor(): string|array|null
    {
        return match ($this) {
            self::STRIPED => 'info',
            self::FILLED => 'success',
            self::HALVED => 'warning',
            self::SASH => 'danger',
            self::HOOPS => 'primary',
            self::CHECKERED => 'gray',
        };
    }

    /**
     * Get icon for Filament UI display
     */
    public function getIcon(): ?string
    {
        return match ($this) {
            self::STRIPED => 'heroicon-m-bars-3',
            self::FILLED => 'heroicon-m-square-3-stack-3d',
            self::HALVED => 'heroicon-m-view-columns',
            self::SASH => 'heroicon-m-arrow-trending-up',
            self::HOOPS => 'heroicon-m-bars-4',
            self::CHECKERED => 'heroicon-m-squares-2x2',
        };
    }

    /**
     * Get description for the shirt type (Filament interface)
     */
    public function getDescription(): ?string
    {
        return match ($this) {
            self::STRIPED => 'Kit with stripes or patterns.',
            self::FILLED => 'Solid color kit without patterns.',
            self::HALVED => 'Kit split into two colors vertically.',
            self::SASH => 'Kit with a diagonal stripe.',
            self::HOOPS => 'Kit with horizontal stripes.',
            self::CHECKERED => 'Kit with a checkerboard pattern.',
        };
    }

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all cases with their labels for form options (backward compatibility)
     */
    public static function options(): array
    {
        return collect(self::cases())->map(fn ($case) => [
            'value' => $case->value,
            'label' => __($case->getLabel()),
        ])->toArray();
    }
}

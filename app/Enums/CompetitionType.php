<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum CompetitionType: string implements HasLabel, HasColor, HasIcon, HasDescription
{
    case LEAGUE = 'league';
    case CUP = 'cup';
    case MIXED = 'mixed';

    /**
     * Get display label for the enum value (Filament interface)
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::LEAGUE => 'League',
            self::CUP => 'Cup',
            self::MIXED => 'Mixed',
        };
    }

    /**
     * Get color for Filament UI display
     */
    public function getColor(): string|array|null
    {
        return match ($this) {
            self::LEAGUE => 'success',
            self::CUP => 'warning',
            self::MIXED => 'info',
        };
    }

    /**
     * Get icon for Filament UI display
     */
    public function getIcon(): ?string
    {
        return match ($this) {
            self::LEAGUE => 'heroicon-m-trophy',
            self::CUP => 'heroicon-m-gift',
            self::MIXED => 'heroicon-m-squares-plus',
        };
    }

    /**
     * Get description for the type (Filament interface)
     */
    public function getDescription(): ?string
    {
        return match ($this) {
            self::LEAGUE => 'Round-robin format where teams play each other home and away.',
            self::CUP => 'Knockout tournament format with elimination rounds.',
            self::MIXED => 'Combination of league and cup formats (e.g., group stage + knockout).',
        };
    }

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get typical number of teams for this competition type
     */
    public function getTypicalTeamCount(): array
    {
        return match ($this) {
            self::LEAGUE => [16, 18, 20, 22],
            self::CUP => [32, 64, 128],
            self::MIXED => [16, 24, 32],
        };
    }

    /**
     * Get typical season duration in months
     */
    public function getTypicalDuration(): int
    {
        return match ($this) {
            self::LEAGUE => 9, // 9 months
            self::CUP => 6,    // 6 months
            self::MIXED => 10, // 10 months
        };
    }
}

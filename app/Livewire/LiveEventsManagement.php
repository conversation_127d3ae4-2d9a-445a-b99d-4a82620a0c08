<?php

namespace App\Livewire;

use App\Models\LiveGame;
use App\Models\LineupPlayer;
use App\Enums\PlayerPosition;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Section;
use Livewire\Component;

class LiveEventsManagement extends Component implements HasActions, HasForms
{
    use InteractsWithActions;
    use InteractsWithForms;

    public ?int $selectedLiveGameId = null;
    public ?LiveGame $selectedLiveGame = null;
    public array $events = [];

    public function mount(?int $liveGameId = null): void
    {
        if ($liveGameId) {
            $this->selectedLiveGameId = $liveGameId;
            $this->selectedLiveGame = LiveGame::with([
                'game.homeTeam', 
                'game.awayTeam',
                'lineups.players.player'
            ])->find($liveGameId);
            $this->events = $this->selectedLiveGame->events ?? [];
        }
    }

    /**
     * Record a goal event
     */
    public function recordGoal(array $data): void
    {
        if (!$this->selectedLiveGame) return;

        $lineupPlayer = LineupPlayer::find($data['lineup_player_id']);
        
        // Record goal in lineup player stats
        $lineupPlayer->recordGoal($data['minute'], $data['goal_type']);
        
        // Add event to live game
        $this->selectedLiveGame->addEvent([
            'type' => 'goal',
            'minute' => $data['minute'],
            'description' => $this->getGoalDescription($data),
            'team_id' => $lineupPlayer->lineup->team_id,
            'player_id' => $lineupPlayer->player_id,
            'lineup_player_id' => $lineupPlayer->id,
            'goal_type' => $data['goal_type'],
            'assist_player_id' => $data['assist_player_id'] ?? null,
        ]);

        // Update game score
        $this->updateGameScore($lineupPlayer->lineup->team_id, $data['goal_type']);

        $this->refreshEvents();
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Goal recorded successfully!'
        ]);
    }

    /**
     * Record a card event
     */
    public function recordCard(array $data): void
    {
        if (!$this->selectedLiveGame) return;

        $lineupPlayer = LineupPlayer::find($data['lineup_player_id']);
        
        // Give card to player
        $lineupPlayer->giveCard($data['card_type'], $data['minute']);
        
        // Add event to live game
        $this->selectedLiveGame->addEvent([
            'type' => 'card',
            'minute' => $data['minute'],
            'description' => $this->getCardDescription($data),
            'team_id' => $lineupPlayer->lineup->team_id,
            'player_id' => $lineupPlayer->player_id,
            'lineup_player_id' => $lineupPlayer->id,
            'card_type' => $data['card_type'],
            'reason' => $data['reason'] ?? null,
        ]);

        $this->refreshEvents();
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => ucfirst($data['card_type']) . ' card recorded successfully!'
        ]);
    }

    /**
     * Record a substitution event
     */
    public function recordSubstitution(array $data): void
    {
        if (!$this->selectedLiveGame) return;

        $playerOut = LineupPlayer::find($data['player_out_id']);
        $playerIn = LineupPlayer::find($data['player_in_id']);
        
        // Perform substitution
        $playerIn->substitute($playerOut, $data['minute']);
        
        // Add event to live game
        $this->selectedLiveGame->addEvent([
            'type' => 'substitution',
            'minute' => $data['minute'],
            'description' => $this->getSubstitutionDescription($playerIn, $playerOut),
            'team_id' => $playerOut->lineup->team_id,
            'player_out_id' => $playerOut->player_id,
            'player_in_id' => $playerIn->player_id,
            'lineup_player_out_id' => $playerOut->id,
            'lineup_player_in_id' => $playerIn->id,
        ]);

        $this->refreshEvents();
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Substitution recorded successfully!'
        ]);
    }

    /**
     * Update current minute
     */
    public function updateMinute(int $minute): void
    {
        if (!$this->selectedLiveGame) return;

        $this->selectedLiveGame->update(['current_minute' => $minute]);
        
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Match minute updated to ' . $minute . "'"
        ]);
    }

    /**
     * Start half time
     */
    public function startHalfTime(): void
    {
        if (!$this->selectedLiveGame) return;

        $this->selectedLiveGame->update([
            'status' => \App\Enums\LiveGameStatus::HALF_TIME,
            'half_time_start' => now(),
        ]);

        $this->selectedLiveGame->addEvent([
            'type' => 'half_time',
            'minute' => 45,
            'description' => 'Half time started',
            'team_id' => null,
            'player_id' => null,
        ]);

        $this->refreshEvents();
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Half time started!'
        ]);
    }

    /**
     * Start second half
     */
    public function startSecondHalf(): void
    {
        if (!$this->selectedLiveGame) return;

        $this->selectedLiveGame->update([
            'status' => \App\Enums\LiveGameStatus::SECOND_HALF,
            'half_time_end' => now(),
        ]);

        $this->selectedLiveGame->addEvent([
            'type' => 'second_half',
            'minute' => 46,
            'description' => 'Second half started',
            'team_id' => null,
            'player_id' => null,
        ]);

        $this->refreshEvents();
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Second half started!'
        ]);
    }

    /**
     * Get available players for a team
     */
    public function getTeamPlayers(int $teamId): array
    {
        if (!$this->selectedLiveGame) return [];

        return $this->selectedLiveGame->lineups()
            ->where('team_id', $teamId)
            ->first()
            ?->players()
            ->with('player')
            ->get()
            ->mapWithKeys(fn($lineupPlayer) => [
                $lineupPlayer->id => $lineupPlayer->player->name . ' (#' . $lineupPlayer->jersey_number . ')'
            ])
            ->toArray() ?? [];
    }

    /**
     * Get substitute players for a team
     */
    public function getSubstitutePlayers(int $teamId): array
    {
        if (!$this->selectedLiveGame) return [];

        return $this->selectedLiveGame->lineups()
            ->where('team_id', $teamId)
            ->first()
            ?->players()
            ->where('status', \App\Enums\PlayerStatus::SUBSTITUTE)
            ->with('player')
            ->get()
            ->mapWithKeys(fn($lineupPlayer) => [
                $lineupPlayer->id => $lineupPlayer->player->name . ' (#' . $lineupPlayer->jersey_number . ')'
            ])
            ->toArray() ?? [];
    }

    /**
     * Get playing players for a team (can be substituted)
     */
    public function getPlayingPlayers(int $teamId): array
    {
        if (!$this->selectedLiveGame) return [];

        return $this->selectedLiveGame->lineups()
            ->where('team_id', $teamId)
            ->first()
            ?->players()
            ->whereIn('status', [\App\Enums\PlayerStatus::STARTING, \App\Enums\PlayerStatus::SUBSTITUTED_IN])
            ->with('player')
            ->get()
            ->mapWithKeys(fn($lineupPlayer) => [
                $lineupPlayer->id => $lineupPlayer->player->name . ' (#' . $lineupPlayer->jersey_number . ')'
            ])
            ->toArray() ?? [];
    }

    private function getGoalDescription(array $data): string
    {
        $lineupPlayer = LineupPlayer::find($data['lineup_player_id']);
        $goalType = match($data['goal_type']) {
            'penalty' => ' (Penalty)',
            'own_goal' => ' (Own Goal)',
            'free_kick' => ' (Free Kick)',
            default => ''
        };
        
        return $lineupPlayer->player->name . ' scored' . $goalType;
    }

    private function getCardDescription(array $data): string
    {
        $lineupPlayer = LineupPlayer::find($data['lineup_player_id']);
        $cardType = ucfirst($data['card_type']);
        $reason = $data['reason'] ? ' - ' . $data['reason'] : '';
        
        return $lineupPlayer->player->name . ' received ' . $cardType . ' card' . $reason;
    }

    private function getSubstitutionDescription(LineupPlayer $playerIn, LineupPlayer $playerOut): string
    {
        return $playerIn->player->name . ' substituted in for ' . $playerOut->player->name;
    }

    private function updateGameScore(int $teamId, string $goalType): void
    {
        if ($goalType === 'own_goal') {
            // Own goal counts for the opposing team
            $teamId = $teamId === $this->selectedLiveGame->game->home_team_id 
                ? $this->selectedLiveGame->game->away_team_id 
                : $this->selectedLiveGame->game->home_team_id;
        }

        $isHomeTeam = $teamId === $this->selectedLiveGame->game->home_team_id;
        
        if ($isHomeTeam) {
            $this->selectedLiveGame->game->increment('home_score');
        } else {
            $this->selectedLiveGame->game->increment('away_score');
        }
    }

    private function refreshEvents(): void
    {
        $this->selectedLiveGame->refresh();
        $this->events = $this->selectedLiveGame->events ?? [];
    }

    public function render()
    {
        return view('livewire.live-events-management');
    }
}

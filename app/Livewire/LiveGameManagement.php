<?php

namespace App\Livewire;

use App\Models\LiveGame;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Livewire\Component;

class LiveGameManagement extends Component implements HasActions, HasForms, HasTable
{
    use InteractsWithActions;
    use InteractsWithForms;
    use InteractsWithTable;

    public function table(Table $table): Table
    {
        return $table
            ->query(
                \App\Models\Game::query()
                    ->with([
                        'homeTeam',
                        'awayTeam',
                        'gameweek',
                        'liveGame.lineups.players',
                    ])
                    ->orderBy('game_date', 'desc')
            )
            ->columns([
                TextColumn::make('game_display_name')
                    ->label('Match')
                    ->searchable(['homeTeam.name', 'awayTeam.name'])
                    ->weight('bold')
                    ->description(fn ($record) => $record->game_date?->format('M j, Y H:i')),
                TextColumn::make('gameweek.name')
                    ->label('Gameweek')
                    ->badge()
                    ->color('primary'),
                TextColumn::make('status')
                    ->label('Game Status')
                    ->badge()
                    ->color(fn ($state): string => match ($state?->value ?? $state) {
                        'scheduled' => 'gray',
                        'in_progress' => 'warning',
                        'finished' => 'success',
                        'postponed' => 'danger',
                        'canceled' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('liveGame.status')
                    ->label('Live Status')
                    ->badge()
                    ->color(fn ($state): string => match ($state?->value ?? $state) {
                        'pre_match' => 'gray',
                        'live' => 'success',
                        'half_time' => 'warning',
                        'second_half' => 'primary',
                        'full_time' => 'info',
                        'finished' => 'secondary',
                        default => 'gray',
                    })
                    ->placeholder('Not Live'),
                TextColumn::make('score')
                    ->label('Score')
                    ->formatStateUsing(fn ($record) => ($record->home_score ?? 0).' - '.($record->away_score ?? 0)
                    )
                    ->alignCenter()
                    ->weight('bold'),
                TextColumn::make('lineups_status')
                    ->label('Lineups')
                    ->formatStateUsing(function ($record) {
                        if (! $record->liveGame) {
                            return 'Not Set';
                        }
                        $lineupCount = $record->liveGame->lineups()->count();
                        $confirmedCount = $record->liveGame->lineups()->where('is_confirmed', true)->count();

                        return "{$confirmedCount}/{$lineupCount} Ready";
                    })
                    ->badge()
                    ->color(function ($record) {
                        if (! $record->liveGame) {
                            return 'gray';
                        }
                        $lineupCount = $record->liveGame->lineups()->count();
                        $confirmedCount = $record->liveGame->lineups()->where('is_confirmed', true)->count();
                        if ($lineupCount >= 2 && $confirmedCount >= 2) {
                            return 'success';
                        }
                        if ($lineupCount >= 2) {
                            return 'warning';
                        }

                        return 'danger';
                    })
                    ->url(fn ($record) => '/admin/game-detail?game='.$record->id),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(\App\Enums\GameStatus::class)
                    ->multiple(),
                SelectFilter::make('gameweek_id')
                    ->label('Gameweek')
                    ->relationship('gameweek', 'name')
                    ->searchable()
                    ->preload(),
            ])

            ->defaultSort('game_date', 'desc');
    }

    /**
     * Start a live match
     */
    public function startMatch(LiveGame $liveGame): void
    {
        try {
            // Validate that lineups are set for both teams
            $homeLineup = $liveGame->homeLineup();
            $awayLineup = $liveGame->awayLineup();

            if (! $homeLineup || ! $awayLineup) {
                $this->dispatch('notify', [
                    'type' => 'error',
                    'message' => 'Both team lineups must be set before starting the match.',
                ]);

                return;
            }

            // Validate that both lineups have starting XI
            if ($homeLineup->startingXI()->count() < 11 || $awayLineup->startingXI()->count() < 11) {
                $this->dispatch('notify', [
                    'type' => 'error',
                    'message' => 'Both teams must have 11 starting players.',
                ]);

                return;
            }

            // Start the match
            $liveGame->update([
                'status' => \App\Enums\LiveGameStatus::LIVE,
                'kick_off_time' => now(),
                'current_minute' => 1,
            ]);

            // Add kick-off event
            $liveGame->addEvent([
                'type' => 'kick_off',
                'minute' => 0,
                'description' => 'Match started',
                'team_id' => null,
                'player_id' => null,
            ]);

            // Update game status
            $liveGame->game->update(['status' => \App\Enums\GameStatus::IN_PROGRESS]);

            $this->dispatch('notify', [
                'type' => 'success',
                'message' => 'Match started successfully!',
            ]);

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Failed to start match: '.$e->getMessage(),
            ]);
        }
    }

    /**
     * End a live match
     */
    public function endMatch(LiveGame $liveGame): void
    {
        try {
            // End the match
            $liveGame->update([
                'status' => \App\Enums\LiveGameStatus::FINISHED,
                'full_time' => now(),
            ]);

            // Add full-time event
            $liveGame->addEvent([
                'type' => 'full_time',
                'minute' => $liveGame->current_minute,
                'description' => 'Match ended',
                'team_id' => null,
                'player_id' => null,
            ]);

            // Update game status
            $liveGame->game->update(['status' => \App\Enums\GameStatus::FINISHED]);

            // Sync player performances from live stats
            $this->syncPlayerPerformances($liveGame);

            $this->dispatch('notify', [
                'type' => 'success',
                'message' => 'Match ended successfully!',
            ]);

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Failed to end match: '.$e->getMessage(),
            ]);
        }
    }

    /**
     * Sync player performances from live stats to PlayerPerformance model
     */
    private function syncPlayerPerformances(LiveGame $liveGame): void
    {
        foreach ($liveGame->lineups as $lineup) {
            foreach ($lineup->players as $lineupPlayer) {
                $liveStats = $lineupPlayer->live_stats ?? [];

                // Create or update player performance
                \App\Models\PlayerPerformance::updateOrCreate(
                    [
                        'game_id' => $liveGame->game_id,
                        'player_id' => $lineupPlayer->player_id,
                        'team_id' => $lineup->team_id,
                    ],
                    [
                        'minutes_played' => $this->calculateMinutesPlayed($lineupPlayer, $liveGame),
                        'goals_scored' => count($liveStats['goals'] ?? []),
                        'assists' => count($liveStats['assists'] ?? []),
                        'yellow_cards' => count(array_filter($liveStats['cards'] ?? [], fn ($card) => $card['type'] === 'yellow')),
                        'red_cards' => count(array_filter($liveStats['cards'] ?? [], fn ($card) => $card['type'] === 'red')),
                        'clean_sheet' => $this->calculateCleanSheet($lineupPlayer, $liveGame),
                        'goals_conceded' => $liveStats['goals_conceded'] ?? 0,
                        'own_goals' => count(array_filter($liveStats['goals'] ?? [], fn ($goal) => $goal['type'] === 'own_goal')),
                        'penalities_saved' => $liveStats['penalties_saved'] ?? 0,
                        'penalities_missed' => count(array_filter($liveStats['goals'] ?? [], fn ($goal) => $goal['type'] === 'penalty_missed')),
                        'penalties_caused' => $liveStats['penalties_caused'] ?? 0,
                        'penalties_committed' => $liveStats['penalties_committed'] ?? 0,
                        'saves' => $liveStats['saves'] ?? 0,
                    ]
                );
            }
        }
    }

    /**
     * Calculate minutes played for a player
     */
    private function calculateMinutesPlayed($lineupPlayer, $liveGame): int
    {
        if ($lineupPlayer->status === \App\Enums\PlayerStatus::STARTING) {
            return $lineupPlayer->substitution_minute ?? $liveGame->current_minute;
        } elseif ($lineupPlayer->status === \App\Enums\PlayerStatus::SUBSTITUTED_IN) {
            return $liveGame->current_minute - ($lineupPlayer->substitution_minute ?? 0);
        }

        return 0;
    }

    /**
     * Calculate clean sheet for a player
     */
    private function calculateCleanSheet($lineupPlayer, $liveGame): bool
    {
        // Only goalkeepers and defenders get clean sheets
        if (! in_array($lineupPlayer->position, [\App\Enums\PlayerPosition::GOALKEEPER, \App\Enums\PlayerPosition::DEFENDER])) {
            return false;
        }

        // Check if team conceded any goals
        $teamId = $lineupPlayer->lineup->team_id;
        $isHomeTeam = $liveGame->game->home_team_id === $teamId;
        $goalsConceded = $isHomeTeam ? $liveGame->game->away_score : $liveGame->game->home_score;

        return $goalsConceded === 0;
    }

    public function render()
    {
        return view('livewire.live-game-management');
    }
}

<?php

namespace App\Livewire;

use App\Enums\PlayerPosition;
use App\Enums\PlayerStatus;
use App\Models\Lineup;
use App\Models\LineupPlayer;
use App\Models\LiveGame;
use App\Models\Player;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Schemas\Components\Section as ComponentsSection;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Livewire\Component;

class LineupManagement extends Component implements HasActions, HasForms, HasTable
{
    use InteractsWithActions;
    use InteractsWithForms;
    use InteractsWithTable;

    public ?int $selectedLiveGameId = null;

    public ?LiveGame $selectedLiveGame = null;

    public function mount(?int $liveGameId = null): void
    {
        if ($liveGameId) {
            $this->selectedLiveGameId = $liveGameId;
            $this->selectedLiveGame = LiveGame::with(['game.homeTeam', 'game.awayTeam'])->find($liveGameId);
        }
    }

    public function render()
    {
        if (! $this->selectedLiveGame) {
            return view('livewire.lineup-management', [
                'homeLineup' => null,
                'awayLineup' => null,
                'homeTeam' => null,
                'awayTeam' => null,
            ]);
        }

        $homeTeam = $this->selectedLiveGame->game->homeTeam;
        $awayTeam = $this->selectedLiveGame->game->awayTeam;

        $homeLineup = $this->selectedLiveGame->lineups()
            ->where('team_id', $homeTeam->id)
            ->with(['players.player'])
            ->first();

        $awayLineup = $this->selectedLiveGame->lineups()
            ->where('team_id', $awayTeam->id)
            ->with(['players.player'])
            ->first();

        return view('livewire.lineup-management', [
            'homeLineup' => $homeLineup,
            'awayLineup' => $awayLineup,
            'homeTeam' => $homeTeam,
            'awayTeam' => $awayTeam,
        ]);
    }

    public function table(Table $table): Table
    {
        // This method is kept for compatibility but not used in the new design
        return $table->query(LineupPlayer::query())
            ->columns([
                TextColumn::make('lineup.team.name')
                    ->label('Team')
                    ->badge()
                    ->color('primary')
                    ->sortable(),
                TextColumn::make('player.name')
                    ->label('Player')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                TextColumn::make('position')
                    ->label('Position')
                    ->badge()
                    ->color(fn ($state): string => match ($state) {
                        PlayerPosition::GOALKEEPER => 'warning',
                        PlayerPosition::DEFENDER => 'info',
                        PlayerPosition::MIDFIELDER => 'success',
                        PlayerPosition::FORWARD => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn ($state): string => match ($state) {
                        PlayerStatus::STARTING => 'success',
                        PlayerStatus::SUBSTITUTE => 'warning',
                        PlayerStatus::SUBSTITUTED_IN => 'info',
                        PlayerStatus::SUBSTITUTED_OUT => 'gray',
                        PlayerStatus::RED_CARD => 'danger',
                        PlayerStatus::YELLOW_CARD => 'warning',
                        default => 'gray',
                    })
                    ->sortable(),
                TextColumn::make('jersey_number')
                    ->label('Jersey #')
                    ->alignCenter()
                    ->sortable(),
                IconColumn::make('is_captain')
                    ->label('Captain')
                    ->boolean()
                    ->trueIcon('heroicon-o-star')
                    ->falseIcon('heroicon-o-minus'),
                IconColumn::make('is_vice_captain')
                    ->label('Vice Captain')
                    ->boolean()
                    ->trueIcon('heroicon-o-star')
                    ->falseIcon('heroicon-o-minus'),
                TextColumn::make('substitution_minute')
                    ->label('Sub Minute')
                    ->suffix("'")
                    ->placeholder('N/A')
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('lineup.team_id')
                    ->label('Team')
                    ->relationship('lineup.team', 'name')
                    ->searchable()
                    ->preload(),
                SelectFilter::make('position')
                    ->options(PlayerPosition::class)
                    ->multiple(),
                SelectFilter::make('status')
                    ->options(PlayerStatus::class)
                    ->multiple(),
            ])
            ->actions([
                EditAction::make()
                    ->label('Edit')
                    ->icon('heroicon-o-pencil')
                    ->form([
                        ComponentsSection::make('Player Details')
                            ->schema([
                                Select::make('position')
                                    ->label('Position')
                                    ->options(PlayerPosition::class)
                                    ->required(),
                                Select::make('status')
                                    ->label('Status')
                                    ->options(PlayerStatus::class)
                                    ->required(),
                                TextInput::make('jersey_number')
                                    ->label('Jersey Number')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(99),
                                Toggle::make('is_captain')
                                    ->label('Captain'),
                                Toggle::make('is_vice_captain')
                                    ->label('Vice Captain'),
                                TextInput::make('substitution_minute')
                                    ->label('Substitution Minute')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(120),
                            ])
                            ->columns(2),
                    ])
                    ->fillForm(fn (LineupPlayer $record): array => [
                        'position' => $record->position,
                        'status' => $record->status,
                        'jersey_number' => $record->jersey_number,
                        'is_captain' => $record->is_captain,
                        'is_vice_captain' => $record->is_vice_captain,
                        'substitution_minute' => $record->substitution_minute,
                    ])
                    ->action(function (array $data, LineupPlayer $record): void {
                        $record->update($data);
                        $this->dispatch('notify', [
                            'type' => 'success',
                            'message' => 'Player updated successfully!',
                        ]);
                    }),
                Action::make('substitute')
                    ->label('Substitute')
                    ->icon('heroicon-o-arrow-path')
                    ->color('warning')
                    ->visible(fn (LineupPlayer $record): bool => $record->status->canBeSubstituted()
                    )
                    ->form([
                        Select::make('substitute_player_id')
                            ->label('Substitute Player')
                            ->options(function (LineupPlayer $record) {
                                return $record->lineup->players()
                                    ->where('status', PlayerStatus::SUBSTITUTE)
                                    ->with('player')
                                    ->get()
                                    ->pluck('player.name', 'id');
                            })
                            ->required(),
                        TextInput::make('minute')
                            ->label('Substitution Minute')
                            ->numeric()
                            ->required()
                            ->minValue(1)
                            ->maxValue(120),
                    ])
                    ->action(function (array $data, LineupPlayer $record): void {
                        $substitutePlayer = LineupPlayer::find($data['substitute_player_id']);
                        $substitutePlayer->substitute($record, $data['minute']);

                        $this->dispatch('notify', [
                            'type' => 'success',
                            'message' => 'Substitution completed successfully!',
                        ]);
                    }),
            ])
            ->headerActions([
                Action::make('add_player')
                    ->label('Add Player to Lineup')
                    ->icon('heroicon-o-plus')
                    ->color('primary')
                    ->visible(fn (): bool => $this->selectedLiveGame !== null)
                    ->form([
                        Select::make('team_id')
                            ->label('Team')
                            ->options(function () {
                                if (! $this->selectedLiveGame) {
                                    return [];
                                }

                                return [
                                    $this->selectedLiveGame->game->home_team_id => $this->selectedLiveGame->game->homeTeam->name,
                                    $this->selectedLiveGame->game->away_team_id => $this->selectedLiveGame->game->awayTeam->name,
                                ];
                            })
                            ->required()
                            ->reactive(),
                        Select::make('player_id')
                            ->label('Player')
                            ->options(function (callable $get) {
                                $teamId = $get('team_id');
                                if (! $teamId) {
                                    return [];
                                }

                                return Player::whereHas('teams', function ($q) use ($teamId) {
                                    $q->where('teams.id', $teamId);
                                })->pluck('name', 'id');
                            })
                            ->searchable()
                            ->required(),
                        Select::make('position')
                            ->label('Position')
                            ->options(PlayerPosition::class)
                            ->required(),
                        Select::make('status')
                            ->label('Status')
                            ->options([
                                PlayerStatus::STARTING->value => 'Starting XI',
                                PlayerStatus::SUBSTITUTE->value => 'Substitute',
                            ])
                            ->default(PlayerStatus::STARTING)
                            ->required(),
                        TextInput::make('jersey_number')
                            ->label('Jersey Number')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(99),
                        Toggle::make('is_captain')
                            ->label('Captain'),
                        Toggle::make('is_vice_captain')
                            ->label('Vice Captain'),
                    ])
                    ->action(function (array $data): void {
                        // Get or create lineup for the team
                        $lineup = Lineup::firstOrCreate([
                            'live_game_id' => $this->selectedLiveGameId,
                            'team_id' => $data['team_id'],
                        ]);

                        // Add player to lineup
                        LineupPlayer::create([
                            'lineup_id' => $lineup->id,
                            'player_id' => $data['player_id'],
                            'position' => $data['position'],
                            'status' => $data['status'],
                            'jersey_number' => $data['jersey_number'],
                            'is_captain' => $data['is_captain'] ?? false,
                            'is_vice_captain' => $data['is_vice_captain'] ?? false,
                        ]);

                        $this->dispatch('notify', [
                            'type' => 'success',
                            'message' => 'Player added to lineup successfully!',
                        ]);
                    }),
            ])
            ->defaultSort('teams.name', 'asc')
            ->groups([
                'lineup.team.name',
                'status',
            ]);
    }

    public function render()
    {
        return view('livewire.lineup-management');
    }
}

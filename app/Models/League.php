<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class League extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'name',
        'season_id',
        'owner_id',
        'type',
        'invite_code',
        'logo',
    ];

    protected $appends = ['logo_url'];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($league) {
            if (empty($league->tenant_id) && Tenant::current()) {
                $league->tenant_id = Tenant::current()->id;
            }
        });
    }

    /**
     * Get the URL to the league's logo.
     *
     * @return string|null
     */
    public function getLogoUrlAttribute()
    {
        return $this->logo ? Storage::url($this->logo) : null;
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function season()
    {
        return $this->belongsTo(Season::class);
    }
    

    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function fantasyTeams()
    {
        return $this->belongsToMany(FantasyTeam::class);
    }

    public function rankingLeagues()
    {
        return $this->hasMany(RankingLeague::class);
    }
}

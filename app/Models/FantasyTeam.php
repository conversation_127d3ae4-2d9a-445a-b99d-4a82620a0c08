<?php

namespace App\Models;

use App\Enums\ShirtType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FantasyTeam extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'user_id',
        'season_id',
        'name',
        'avatar',
        'shirt_type',
        'shirt_color',
        'strip_color',
        'budget',
        'total_points',
    ];

    protected $casts = [
        'shirt_type' => ShirtType::class,
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($fantasyTeam) {
            if (empty($fantasyTeam->tenant_id) && Tenant::current()) {
                $fantasyTeam->tenant_id = Tenant::current()->id;
            }
        });
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function season()
    {
        return $this->belongsTo(Season::class);
    }

    public function fantasyPlayers()
    {
        return $this->hasMany(FantasyPlayer::class);
    }

    public function fantasyTransfers()
    {
        return $this->hasMany(FantasyTransfer::class);
    }

    public function leagues()
    {
        return $this->belongsToMany(League::class);
    }

    public function rankingGlobals()
    {
        return $this->hasMany(RankingGlobal::class);
    }

    public function rankingFavoriteTeams()
    {
        return $this->hasMany(RankingFavoriteTeam::class);
    }

    public function rankingJoinedGameweeks()
    {
        return $this->hasMany(RankingJoinedGameweek::class);
    }

    public function rankingLeagues()
    {
        return $this->hasMany(RankingLeague::class);
    }

    public function transfers()
    {
        return $this->hasMany(FantasyTransfer::class);
    }

    public function fantasyTeamGameweeks()
    {
        return $this->hasMany(FantasyTeamGameweek::class);
    }

    /**
     * Update budget after squad creation or transfers
     */
    public function updateBudget(float $newBudget): void
    {
        $this->budget = $newBudget;
        $this->save();
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FantasyPlayer extends Model
{
    use HasFactory;

    protected $fillable = [
        'fantasy_team_id',
        'gameweek_id',
        'player_id',
        'is_captain',
        'is_vice_captain',
        'purchase_price',
    ];

    protected $casts = [
        'is_captain' => 'boolean',
        'is_vice_captain' => 'boolean',
    ];

    public function fantasyTeam()
    {
        return $this->belongsTo(FantasyTeam::class);
    }

    public function gameweek()
    {
        return $this->belongsTo(Gameweek::class);
    }

    public function fantasyTeamLineups()
    {
        return $this->hasMany(FantasyTeamLineup::class);
    }

    public function player()
    {
        return $this->belongsTo(Player::class);
    }
}

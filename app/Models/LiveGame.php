<?php

namespace App\Models;

use App\Enums\LiveGameStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LiveGame extends Model
{
    use HasFactory;

    protected $fillable = [
        'game_id',
        'status',
        'current_minute',
        'added_time',
        'kick_off_time',
        'half_time_start',
        'half_time_end',
        'full_time',
        'events',
        'statistics',
        'notes',
    ];

    protected $casts = [
        'status' => LiveGameStatus::class,
        'kick_off_time' => 'datetime',
        'half_time_start' => 'datetime',
        'half_time_end' => 'datetime',
        'full_time' => 'datetime',
        'events' => 'array',
        'statistics' => 'array',
    ];

    /**
     * Get the game that this live game belongs to
     */
    public function game(): BelongsTo
    {
        return $this->belongsTo(Game::class);
    }

    /**
     * Get the lineups for this live game
     */
    public function lineups(): HasMany
    {
        return $this->hasMany(Lineup::class);
    }

    /**
     * Get the home team lineup
     */
    public function homeLineup()
    {
        return $this->lineups()->whereHas('team', function ($query) {
            $query->where('id', $this->game->home_team_id);
        })->first();
    }

    /**
     * Get the away team lineup
     */
    public function awayLineup()
    {
        return $this->lineups()->whereHas('team', function ($query) {
            $query->where('id', $this->game->away_team_id);
        })->first();
    }

    /**
     * Add an event to the live game
     */
    public function addEvent(array $event): void
    {
        $events = $this->events ?? [];
        $event['timestamp'] = now();
        $events[] = $event;
        $this->update(['events' => $events]);
    }

    /**
     * Update live statistics
     */
    public function updateStatistics(array $stats): void
    {
        $currentStats = $this->statistics ?? [];
        $this->update(['statistics' => array_merge($currentStats, $stats)]);
    }

    /**
     * Check if the game is currently live
     */
    public function isLive(): bool
    {
        return $this->status->isActive();
    }

    /**
     * Check if lineups can be modified
     */
    public function canModifyLineups(): bool
    {
        return $this->status->allowsLineupChanges();
    }

    /**
     * Get the current match time display
     */
    public function getCurrentTimeDisplay(): string
    {
        if ($this->status === LiveGameStatus::PRE_MATCH) {
            return 'Pre-Match';
        }

        if ($this->status === LiveGameStatus::HALF_TIME) {
            return 'HT';
        }

        if ($this->status === LiveGameStatus::FULL_TIME) {
            return 'FT';
        }

        if ($this->status === LiveGameStatus::FINISHED) {
            return 'Finished';
        }

        $time = $this->current_minute;
        if ($this->added_time > 0) {
            $time .= '+'.$this->added_time;
        }

        return $time."'";
    }
}

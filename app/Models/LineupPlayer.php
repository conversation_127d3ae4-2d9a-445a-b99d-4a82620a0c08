<?php

namespace App\Models;

use App\Enums\PlayerPosition;
use App\Enums\PlayerStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LineupPlayer extends Model
{
    use HasFactory;

    protected $fillable = [
        'lineup_id',
        'player_id',
        'position',
        'status',
        'jersey_number',
        'is_captain',
        'is_vice_captain',
        'substitution_minute',
        'substituted_for',
        'live_stats',
        'notes',
    ];

    protected $casts = [
        'position' => PlayerPosition::class,
        'status' => PlayerStatus::class,
        'is_captain' => 'boolean',
        'is_vice_captain' => 'boolean',
        'live_stats' => 'array',
    ];

    /**
     * Get the lineup that this player belongs to
     */
    public function lineup(): BelongsTo
    {
        return $this->belongsTo(Lineup::class);
    }

    /**
     * Get the player
     */
    public function player(): BelongsTo
    {
        return $this->belongsTo(Player::class);
    }

    /**
     * Get the player this one was substituted for
     */
    public function substitutedFor(): BelongsTo
    {
        return $this->belongsTo(LineupPlayer::class, 'substituted_for');
    }

    /**
     * Update live statistics for this player
     */
    public function updateLiveStats(array $stats): void
    {
        $currentStats = $this->live_stats ?? [];
        $this->update(['live_stats' => array_merge($currentStats, $stats)]);
    }

    /**
     * Record a substitution
     */
    public function substitute(LineupPlayer $playerOut, int $minute): void
    {
        $this->update([
            'status' => PlayerStatus::SUBSTITUTED_IN,
            'substitution_minute' => $minute,
            'substituted_for' => $playerOut->id,
        ]);

        $playerOut->update([
            'status' => PlayerStatus::SUBSTITUTED_OUT,
            'substitution_minute' => $minute,
        ]);
    }

    /**
     * Give player a card
     */
    public function giveCard(string $cardType, int $minute): void
    {
        $status = $cardType === 'yellow' ? PlayerStatus::YELLOW_CARD : PlayerStatus::RED_CARD;

        $this->update(['status' => $status]);

        // Add to live stats
        $this->updateLiveStats([
            'cards' => array_merge($this->live_stats['cards'] ?? [], [
                [
                    'type' => $cardType,
                    'minute' => $minute,
                    'timestamp' => now(),
                ],
            ]),
        ]);
    }

    /**
     * Record a goal
     */
    public function recordGoal(int $minute, string $type = 'goal'): void
    {
        $this->updateLiveStats([
            'goals' => array_merge($this->live_stats['goals'] ?? [], [
                [
                    'minute' => $minute,
                    'type' => $type, // goal, penalty, own_goal
                    'timestamp' => now(),
                ],
            ]),
        ]);
    }

    /**
     * Record an assist
     */
    public function recordAssist(int $minute): void
    {
        $this->updateLiveStats([
            'assists' => array_merge($this->live_stats['assists'] ?? [], [
                [
                    'minute' => $minute,
                    'timestamp' => now(),
                ],
            ]),
        ]);
    }

    /**
     * Check if player is currently on the field
     */
    public function isOnField(): bool
    {
        return $this->status->isOnField();
    }

    /**
     * Check if player can be substituted
     */
    public function canBeSubstituted(): bool
    {
        return $this->status->canBeSubstituted();
    }

    /**
     * Get total goals scored in this match
     */
    public function getGoalsCount(): int
    {
        return count($this->live_stats['goals'] ?? []);
    }

    /**
     * Get total assists in this match
     */
    public function getAssistsCount(): int
    {
        return count($this->live_stats['assists'] ?? []);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PlayerPerformance extends Model
{
    use HasFactory;

    protected $fillable = [
        'game_id',
        'player_id',
        'minutes_played',
        'goals_scored',
        'assists',
        'clean_sheet',
        'goals_conceded',
        'own_goals',
        'penalities_saved',
        'penalities_missed',
        'penalties_caused',
        'penalties_committed',
        'saves',
        'yellow_cards',
        'red_cards',
        'team_id',
    ];

    protected $casts = [
        'clean_sheet' => 'boolean',
    ];

    public function game()
    {
        return $this->belongsTo(Game::class);
    }

    public function player()
    {
        return $this->belongsTo(Player::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }
}

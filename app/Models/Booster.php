<?php

namespace App\Models;

use Bavix\Wallet\Interfaces\Customer;
use Bavix\Wallet\Interfaces\ProductInterface;
use Bavix\Wallet\Traits\HasWallet;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Booster extends Model implements ProductInterface
{
    use HasFactory;
    use HasWallet;

    protected $fillable = [
        'title',
        'description',
        'effect',
        'price',
        'image',
    ];

    public function getAmountProduct(Customer $customer): int|string
    {
        return $this->price;
    }

    public function getMetaProduct(): ?array
    {
        return [
            'title' => $this->title,
            'description' => $this->description,
        ];
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }
}

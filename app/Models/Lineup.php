<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Lineup extends Model
{
    use HasFactory;

    protected $fillable = [
        'live_game_id',
        'team_id',
        'formation',
        'is_confirmed',
        'confirmed_at',
        'confirmed_by',
        'notes',
    ];

    protected $casts = [
        'is_confirmed' => 'boolean',
        'confirmed_at' => 'datetime',
    ];

    /**
     * Get the live game that this lineup belongs to
     */
    public function liveGame(): BelongsTo
    {
        return $this->belongsTo(LiveGame::class);
    }

    /**
     * Get the team that this lineup belongs to
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the user who confirmed this lineup
     */
    public function confirmedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'confirmed_by');
    }

    /**
     * Get the players in this lineup
     */
    public function players(): <PERSON><PERSON>any
    {
        return $this->hasMany(LineupPlayer::class);
    }

    /**
     * Get the starting XI players
     */
    public function startingXI()
    {
        return $this->players()->where('status', 'starting')->orderBy('position');
    }

    /**
     * Get the substitute players
     */
    public function substitutes()
    {
        return $this->players()->where('status', 'substitute')->orderBy('jersey_number');
    }

    /**
     * Get the captain
     */
    public function captain()
    {
        return $this->players()->where('is_captain', true)->first();
    }

    /**
     * Get the vice captain
     */
    public function viceCaptain()
    {
        return $this->players()->where('is_vice_captain', true)->first();
    }

    /**
     * Confirm the lineup
     */
    public function confirm(User $user): void
    {
        $this->update([
            'is_confirmed' => true,
            'confirmed_at' => now(),
            'confirmed_by' => $user->id,
        ]);
    }

    /**
     * Check if lineup is complete (has 11 starting players)
     */
    public function isComplete(): bool
    {
        return $this->startingXI()->count() === 11;
    }

    /**
     * Check if lineup can be modified
     */
    public function canBeModified(): bool
    {
        return ! $this->is_confirmed && $this->liveGame->canModifyLineups();
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RankingJoinedGameweek extends Model
{
    use HasFactory;

    protected $table = 'ranking_joined_gameweek';

    protected $fillable = [
        'tenant_id',
        'join_gameweek_id',
        'gameweek_id',
        'fantasy_team_id',
        'rank_gameweek',
        'rank',
        'points',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (Tenant::current()) {
                $model->tenant_id = Tenant::current()->id;
            }
        });
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function joinGameweek()
    {
        return $this->belongsTo(Gameweek::class, 'join_gameweek_id');
    }

    public function gameweek()
    {
        return $this->belongsTo(Gameweek::class);
    }

    public function fantasyTeam()
    {
        return $this->belongsTo(FantasyTeam::class);
    }
}

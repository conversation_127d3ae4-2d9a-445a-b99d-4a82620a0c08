<?php

namespace App\Models;

use App\Enums\CompetitionStatus;
use App\Enums\CompetitionType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Competition extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'logo',
        'slug',
        'type',
        'status',
        'current_season_id',
    ];

    protected $casts = [
        'type' => CompetitionType::class,
        'status' => CompetitionStatus::class,
    ];

    /**
     * Mutator to handle empty string for current_season_id
     */
    public function setCurrentSeasonIdAttribute($value)
    {
        $this->attributes['current_season_id'] = empty($value) ? null : $value;
    }

    public function tenants()
    {
        return $this->belongsToMany(Tenant::class);
    }

    public function seasons()
    {
        return $this->hasMany(Season::class);
    }

    public function currentSeason()
    {
        return $this->belongsTo(Season::class, 'current_season_id');
    }

    /**
     * Get the current season with its current phase and gameweek
     */
    public function getCurrentSeasonWithPhaseAndGameweek()
    {
        return $this->currentSeason()
            ->with([
                'currentSeasonPhase.currentGameweek',
            ])
            ->first();
    }
}

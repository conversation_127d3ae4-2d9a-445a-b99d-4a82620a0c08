<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class FantasyTransfer extends Model
{
    use HasFactory, UsesTenantConnection;

    protected $table = 'fantasy_transfers';

    protected $fillable = [
        'tenant_id',
        'fantasy_team_id',
        'gameweek_id',
        'player_in_id',
        'player_out_id',
        'transfer_cost',
        'is_free_transfer',
        'transfer_type', // 'in', 'out', 'swap'
    ];

    protected $casts = [
        'is_free_transfer' => 'boolean',
        'transfer_cost' => 'decimal:2',
    ];

    public function fantasyTeam()
    {
        return $this->belongsTo(FantasyTeam::class);
    }

    public function gameweek()
    {
        return $this->belongsTo(Gameweek::class);
    }

    public function playerIn()
    {
        return $this->belongsTo(Player::class, 'player_in_id');
    }

    public function playerOut()
    {
        return $this->belongsTo(Player::class, 'player_out_id');
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transfer) {
            if (empty($transfer->tenant_id) && Tenant::current()) {
                $transfer->tenant_id = Tenant::current()->id;
            }
        });
    }

    /**
     * Get recent transfers for the current user in the current context
     *
     * @param  int  $limit  Number of transfers to return
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getRecentTransfers($limit = 3)
    {
        // Get current user
        $user = Auth::user();
        if (! $user) {
            return collect();
        }

        // Get current competition from session
        $competitionId = session('current_competition_id');
        if (! $competitionId) {
            return collect();
        }

        // Get competition with current season
        $competition = Competition::with('currentSeason')->find($competitionId);
        if (! $competition || ! $competition->currentSeason) {
            return collect();
        }

        $seasonId = $competition->currentSeason->id;

        // Get user's fantasy team for current context
        $fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $seasonId)
            ->first();

        if (! $fantasyTeam) {
            return collect();
        }

        // Get recent transfers with player info
        return self::where('fantasy_team_id', $fantasyTeam->id)
            ->with(['playerIn', 'playerOut', 'gameweek'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($transfer) {
                return [
                    'id' => $transfer->id,
                    'type' => $transfer->transfer_type,
                    'date' => $transfer->created_at,
                    'gameweek' => $transfer->gameweek ? $transfer->gameweek->name : null,
                    'transfer_cost' => $transfer->transfer_cost,
                    'is_free_transfer' => $transfer->is_free_transfer,
                    'inPlayer' => $transfer->playerIn ? [
                        'id' => $transfer->playerIn->id,
                        'name' => $transfer->playerIn->name,
                        'price' => $transfer->playerIn->price ?? 0,
                    ] : null,
                    'outPlayer' => $transfer->playerOut ? [
                        'id' => $transfer->playerOut->id,
                        'name' => $transfer->playerOut->name,
                        'price' => $transfer->playerOut->price ?? 0,
                    ] : null,
                ];
            });
    }
}

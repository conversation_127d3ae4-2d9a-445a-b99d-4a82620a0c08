<?php

namespace App\Models;

use App\Enums\RefereeRole;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Referee extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'name_ar',
        'birthday',
        'country',
        'image',
        'experience_years',
    ];

    protected $casts = [

        'birthday' => 'date',
        'experience_years' => 'integer',
    ];

    /**
     * Get the games where this referee officiated
     */
    public function games()
    {
        return $this->belongsToMany(Game::class, 'game_referee')
            ->withPivot('role')
            ->withTimestamps();
    }

    /**
     * Get games where this referee was the main referee
     */
    public function mainRefereeGames()
    {
        return $this->games()->wherePivot('role', RefereeRole::MAIN_REFEREE->value);
    }

    /**
     * Get games where this referee was an assistant referee
     */
    public function assistantRefereeGames()
    {
        return $this->games()->wherePivot('role', '!=', RefereeRole::MAIN_REFEREE->value);
    }

    /**
     * Get the total number of games officiated
     */
    public function getTotalGamesAttribute(): int
    {
        return $this->games()->count();
    }

    /**
     * Get the number of games as main referee
     */
    public function getMainRefereeGamesCountAttribute(): int
    {
        return $this->mainRefereeGames()->count();
    }

    /**
     * Get the number of games as assistant referee
     */
    public function getAssistantRefereeGamesCountAttribute(): int
    {
        return $this->assistantRefereeGames()->count();
    }

    /**
     * Get the referee's full name with fallback
     */
    public function getFullNameAttribute(): string
    {
        return $this->name_ar ?? $this->name;
    }

    /**
     * Scope to filter referees by country
     */
    public function scopeFromCountry($query, string $country)
    {
        return $query->where('country', $country);
    }

    /**
     * Scope to filter referees by minimum experience
     */
    public function scopeWithMinimumExperience($query, int $years)
    {
        return $query->where('experience_years', '>=', $years);
    }
}

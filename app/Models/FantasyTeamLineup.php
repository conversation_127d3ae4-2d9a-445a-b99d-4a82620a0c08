<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FantasyTeamLineup extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'position',
        'sub_priority',
        'fantasy_player_id',
        'gameweek_id',
    ];

    public function fantasyPlayer()
    {
        return $this->belongsTo(FantasyPlayer::class);
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($lineup) {
            if (empty($lineup->tenant_id) && Tenant::current()) {
                $lineup->tenant_id = Tenant::current()->id;
            }
        });
    }
}

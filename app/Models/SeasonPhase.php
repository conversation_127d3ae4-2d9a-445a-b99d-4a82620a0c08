<?php

namespace App\Models;

use App\Enums\SeasonPhase\GamePhaseStatus;
use App\Enums\SeasonPhase\SeasonPhaseFormat;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SeasonPhase extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'format',
        'config',
        'teams_count',
        'status',
        'season_id',
    ];

    protected $casts = [
        'config' => 'array',
        'status' => GamePhaseStatus::class,
        'format' => SeasonPhaseFormat::class,
    ];

    public function season()
    {
        return $this->belongsTo(Season::class);
    }

    public function gameweeks()
    {
        return $this->hasMany(Gameweek::class);
    }

    /**
     * Get the current active gameweek for this season phase
     */
    public function currentGameweek()
    {
        return $this->hasOne(Gameweek::class)
            ->where('status', 'ongoing')
            ->orderBy('start_date', 'desc');
    }
}

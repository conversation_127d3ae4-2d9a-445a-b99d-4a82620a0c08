<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RankingFavoriteTeam extends Model
{
    use HasFactory;

    protected $table = 'ranking_favorite_team';

    protected $fillable = [
        'tenant_id',
        'team_id',
        'gameweek_id',
        'fantasy_team_id',
        'rank',
        'points',
        'rank_gameweek',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (Tenant::current()) {
                $model->tenant_id = Tenant::current()->id;
            }
        });
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function gameweek()
    {
        return $this->belongsTo(Gameweek::class);
    }

    public function fantasyTeam()
    {
        return $this->belongsTo(FantasyTeam::class);
    }
}

<?php

namespace App\Models;

use App\Enums\PlayerPosition;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Player extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'name_ar',
        'birthday',
        'country',
        'position',
        'image',
    ];

    protected $casts = [
        'position' => PlayerPosition::class,
    ];

    public function teams()
    {
        return $this->belongsToMany(Team::class);
    }

    public function currentTeam()
    {
        return $this->teams()->whereHas('seasons', function ($query) {
            $query->where('seasons.competition_id', session('current_competition_id'));
        })->first();
    }

    public function fantasyPlayers()
    {
        return $this->hasMany(FantasyPlayer::class);
    }

    public function inFantasyTransfers()
    {
        return $this->hasMany(FantasyTransfer::class, 'player_in_id');
    }

    public function outFantasyTransfers()
    {
        return $this->hasMany(FantasyTransfer::class, 'player_out_id');
    }

    public function playerPerformances()
    {
        return $this->hasMany(PlayerPerformance::class);
    }

    public function fantasyPoints()
    {
        return $this->hasMany(FantasyPoint::class);
    }

    public function marketValues()
    {
        return $this->hasMany(PlayerMarketValue::class);
    }

    /**
     * Get the current market value for the player (latest gameweek)
     */
    public function getCurrentMarketValue(): ?int
    {
        // Get the most recent gameweek that has a market value for this player
        $latestMarketValue = $this->marketValues()
            ->with('gameweek')
            ->get()
            ->sortByDesc(function ($marketValue) {
                // Sort by gameweek deadline (or start_date if no deadline in rules)
                $gameweek = $marketValue->gameweek;

                return $gameweek->deadline ?? $gameweek->start_date;
            })
            ->first();

        return $latestMarketValue?->market_value;
    }

    /**
     * Get market value for a specific gameweek
     */
    public function getMarketValueForGameweek(int $gameweekId): ?int
    {
        return $this->marketValues()
            ->where('gameweek_id', $gameweekId)
            ->value('market_value');
    }

    /**
     * Accessor for market_value to maintain backward compatibility
     */
    public function getMarketValueAttribute(): ?int
    {
        return $this->getCurrentMarketValue();
    }
}

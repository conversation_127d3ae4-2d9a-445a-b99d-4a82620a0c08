<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PlayerMarketValue extends Model
{
    use HasFactory;

    protected $fillable = [
        'player_id',
        'gameweek_id',
        'market_value',
    ];

    protected $casts = [
        'market_value' => 'integer',
    ];

    public function player(): BelongsTo
    {
        return $this->belongsTo(Player::class);
    }

    public function gameweek(): BelongsTo
    {
        return $this->belongsTo(Gameweek::class);
    }
}

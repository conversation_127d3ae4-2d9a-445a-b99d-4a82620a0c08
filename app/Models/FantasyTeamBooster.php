<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FantasyTeamBooster extends Model
{
    protected $fillable = [
        'fantasy_team_id',
        'booster_id',
        'game_week_id',
    ];

    public function fantasyTeam()
    {
        return $this->belongsTo(FantasyTeam::class);
    }

    public function booster()
    {
        return $this->belongsTo(Booster::class);
    }

    public function gameWeek()
    {
        return $this->belongsTo(GameWeek::class);
    }
}

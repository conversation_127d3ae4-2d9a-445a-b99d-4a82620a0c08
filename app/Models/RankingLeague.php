<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RankingLeague extends Model
{
    use HasFactory;

    protected $table = 'ranking_league';

    protected $fillable = [
        'tenant_id',
        'league_id',
        'gameweek_id',
        'fantasy_team_id',
        'rank_gameweek',
        'rank',
        'points',
        'points_gameweek',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (Tenant::current()) {
                $model->tenant_id = Tenant::current()->id;
            }
        });
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function league()
    {
        return $this->belongsTo(League::class);
    }

    public function gameweek()
    {
        return $this->belongsTo(Gameweek::class);
    }

    public function fantasyTeam()
    {
        return $this->belongsTo(FantasyTeam::class);
    }
}

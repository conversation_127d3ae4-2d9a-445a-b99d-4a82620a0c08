<?php

namespace App\Models;

use App\Enums\GameStatus;
use App\Enums\RefereeRole;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Game extends Model
{
    use HasFactory;

    protected $fillable = [
        'gameweek_id',
        'home_team_id',
        'away_team_id',
        'stadium_id',
        'game_date',
        'home_score',
        'away_score',
        'status',
    ];

    protected $casts = [
        'status' => GameStatus::class,
        'game_date' => 'datetime',
    ];

    protected $appends = [
        'game_display_name',
    ];

    public function gameweek()
    {
        return $this->belongsTo(Gameweek::class);
    }

    public function homeTeam()
    {
        return $this->belongsTo(Team::class, 'home_team_id');
    }

    public function awayTeam()
    {
        return $this->belongsTo(Team::class, 'away_team_id');
    }

    public function playerPerformances()
    {
        return $this->hasMany(PlayerPerformance::class);
    }

    public function fantasyPoints()
    {
        return $this->hasMany(FantasyPoint::class);
    }

    public function liveGame()
    {
        return $this->hasOne(LiveGame::class);
    }

    public function stadium()
    {
        return $this->belongsTo(Stadium::class);
    }

    /**
     * Get all referees assigned to this game
     */
    public function referees()
    {
        return $this->belongsToMany(Referee::class, 'game_referee')
            ->withPivot('role')
            ->withTimestamps();
    }

    /**
     * Get the main referee for this game
     */
    public function mainReferee()
    {
        return $this->referees()->wherePivot('role', RefereeRole::MAIN_REFEREE->value)->first();
    }

    /**
     * Get the assistant referees for this game
     */
    public function assistantReferees()
    {
        return $this->referees()->wherePivot('role', '!=', RefereeRole::MAIN_REFEREE->value);
    }

    /**
     * Get assistant referee 1
     */
    public function assistantReferee1()
    {
        return $this->referees()->wherePivot('role', RefereeRole::ASSISTANT_REFEREE_1->value)->first();
    }

    /**
     * Get assistant referee 2
     */
    public function assistantReferee2()
    {
        return $this->referees()->wherePivot('role', RefereeRole::ASSISTANT_REFEREE_2->value)->first();
    }

    /**
     * Check if the game has a complete referee team
     */
    public function hasCompleteRefereeTeam(): bool
    {
        $assignedRoles = $this->referees()->pluck('game_referee.role')->toArray();
        $requiredRoles = array_map(fn ($role) => $role->value, RefereeRole::getRequiredRoles());

        return count(array_intersect($assignedRoles, $requiredRoles)) === count($requiredRoles);
    }

    /**
     * Assign a complete referee team to the game
     */
    public function assignRefereeTeam(Referee $mainReferee, Referee $assistant1, Referee $assistant2): void
    {
        // Clear existing referee assignments
        $this->referees()->detach();

        // Assign the new referee team
        $this->referees()->attach([
            $mainReferee->id => ['role' => RefereeRole::MAIN_REFEREE->value],
            $assistant1->id => ['role' => RefereeRole::ASSISTANT_REFEREE_1->value],
            $assistant2->id => ['role' => RefereeRole::ASSISTANT_REFEREE_2->value],
        ]);
    }

    /**
     * Get a display name for the game showing teams and date
     */
    public function getGameDisplayNameAttribute(): string
    {
        $homeTeam = $this->homeTeam?->name ?? 'TBD';
        $awayTeam = $this->awayTeam?->name ?? 'TBD';
        $date = $this->game_date ? $this->game_date->format('M j, H:i') : 'TBD';

        return "{$homeTeam} vs {$awayTeam} - {$date}";
    }
}

<?php

namespace App\Models;

use App\Enums\GameweekStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Gameweek extends Model
{
    use HasFactory;

    protected $fillable = [
        'season_phase_id',
        'name',
        'start_date',
        'end_date',
        'rules',
        'status',
    ];

    protected $casts = [
        'rules' => 'array',
        'status' => GameweekStatus::class,
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    public function seasonPhase()
    {
        return $this->belongsTo(SeasonPhase::class);
    }

    public function fantasyPlayers()
    {
        return $this->hasMany(FantasyPlayer::class);
    }

    public function games()
    {
        return $this->hasMany(Game::class);
    }

    public function rankingGlobals()
    {
        return $this->hasMany(RankingGlobal::class);
    }

    public function rankingFavoriteTeams()
    {
        return $this->hasMany(RankingFavoriteTeam::class);
    }

    public function rankingJoinedGameweeks()
    {
        return $this->hasMany(RankingJoinedGameweek::class, 'join_gameweek_id');
    }

    public function rankingGameweeks()
    {
        return $this->hasMany(RankingJoinedGameweek::class, 'gameweek_id');
    }

    public function rankingLeagues()
    {
        return $this->hasMany(RankingLeague::class);
    }

    /**
     * Get the transfer deadline for this gameweek
     * First checks the rules JSON, then falls back to start_date
     */
    public function getDeadlineAttribute(): ?string
    {
        // Check if deadline is set in rules
        if (isset($this->rules['transfer_deadline'])) {
            return $this->rules['transfer_deadline'];
        }

        // Fall back to start_date as deadline
        return $this->start_date?->format('Y-m-d H:i:s');
    }

    /**
     * Check if transfers are still allowed for this gameweek
     */
    public function allowsTransfers(): bool
    {
        $deadline = $this->deadline;
        if (! $deadline) {
            return false;
        }

        return now() < $deadline;
    }
}

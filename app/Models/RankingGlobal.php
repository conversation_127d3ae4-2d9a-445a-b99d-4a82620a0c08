<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class RankingGlobal extends Model
{
    use HasFactory;

    protected $table = 'ranking_global';

    protected $fillable = [
        'tenant_id',
        'fantasy_team_id',
        'gameweek_id',
        'rank',
        'point',
        'rank_gameweek',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (Tenant::current()) {
                $model->tenant_id = Tenant::current()->id;
            }
        });
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function fantasyTeam()
    {
        return $this->belongsTo(FantasyTeam::class);
    }

    public function gameweek()
    {
        return $this->belongsTo(Gameweek::class);
    }
    
    /**
     * Get total points for the current user's fantasy team in the current competition and tenant
     * 
     * @return int Total points or 0 if no data found
     */
    public static function getTotalPointsForCurrentContext()
    {
        // Get current user
        $user = Auth::user();
        if (!$user) {
            return 0;
        }
        
        // Get current competition from session
        $competitionId = session('current_competition_id');
        if (!$competitionId) {
            return 0;
        }
        
        // Get competition with current season
        $competition = Competition::with('currentSeason')->find($competitionId);
        if (!$competition || !$competition->currentSeason) {
            return 0;
        }
        
        $seasonId = $competition->currentSeason->id;
        
        // Get user's fantasy team for current context
        $fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $seasonId)
            ->first();
            
        if (!$fantasyTeam) {
            return 0;
        }
        
        // Sum points across all gameweeks for this fantasy team
        $totalPoints = self::where('fantasy_team_id', $fantasyTeam->id)
            ->sum('point');
            
        return $totalPoints ?: 0;
    }
    
    /**
     * Get current global rank for the user's fantasy team
     * 
     * @return string Current rank or 'N/A' if not ranked
     */
    public static function getCurrentGlobalRank()
    {
        // Get current user
        $user = Auth::user();
        if (!$user) {
            return 'N/A';
        }
        
        // Get current competition from session
        $competitionId = session('current_competition_id');
        if (!$competitionId) {
            return 'N/A';
        }
        
        // Get competition with current season
        $competition = Competition::with('currentSeason')->find($competitionId);
        if (!$competition || !$competition->currentSeason) {
            return 'N/A';
        }
        
        $seasonId = $competition->currentSeason->id;
        
        // Get user's fantasy team for current context
        $fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $seasonId)
            ->first();
            
        if (!$fantasyTeam) {
            return 'N/A';
        }
        
        // Get most recent ranking
        $latestRanking = self::where('fantasy_team_id', $fantasyTeam->id)
            ->orderBy('gameweek_id', 'desc')
            ->first();
            
        return $latestRanking ? $latestRanking->rank : 'N/A';
    }
}

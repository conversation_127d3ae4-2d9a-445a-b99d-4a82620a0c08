<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Stadium extends Model
{
    /** @use HasFactory<\Database\Factories\StadiumFactory> */
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'stadiums';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'location',
        'city',
        'country',
        'capacity',
        'year_opened',
        'covered',
        'image_url',
    ];

    /**
     * The teams that play at this stadium.
     */
    public function teams()
    {
        return $this->belongsToMany(Team::class, 'stadium_team')->withPivot('is_home');
    }

    public function homeTeams()
    {
        return $this->belongsToMany(Team::class, 'stadium_team')
            ->wherePivot('is_home', true);
    }

    /**
     * The games played at this stadium.
     */
    public function games()
    {
        return $this->hasMany(Game::class);
    }
}

<?php

namespace App\Providers;

use App\Services\FantasyContextService;
use App\Services\PlayerDataService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(FantasyContextService::class, function ($app) {
            return new FantasyContextService();
        });

        $this->app->singleton(PlayerDataService::class, function ($app) {
            return new PlayerDataService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}

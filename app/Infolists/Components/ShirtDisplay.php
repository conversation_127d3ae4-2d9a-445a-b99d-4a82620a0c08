<?php

namespace App\Infolists\Components;

use Filament\Infolists\Components\Entry;

class ShirtDisplay extends Entry
{
    protected string $view = 'infolists.components.shirt-display';

    protected string | \Closure | null $logoField = null;

    public function logoField(string | \Closure | null $field): static
    {
        $this->logoField = $field;

        return $this;
    }

    public function getLogoField(): ?string
    {
        return $this->evaluate($this->logoField);
    }

    public function getLogoUrl(): ?string
    {
        $logoField = $this->getLogoField();
        
        if (!$logoField) {
            return null;
        }

        $record = $this->getRecord();
        $logoPath = data_get($record, $logoField);

        return $logoPath ? "/storage/{$logoPath}" : null;
    }
}

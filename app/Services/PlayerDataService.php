<?php

namespace App\Services;

use App\Models\FantasyPlayer;
use App\Models\Player;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class PlayerDataService
{
    protected ?Collection $squadPlayers = null;

    protected bool $squadPlayersResolved = false;

    public function getSquadPlayers($fantasyTeamId, $gameweek): Collection
    {
        if ($this->squadPlayersResolved) {
            return $this->squadPlayers;
        }

        $this->squadPlayers = FantasyPlayer::with([
            'player.teams',
            'player.marketValues' => fn ($q) => $q->where('gameweek_id', $gameweek->id),
            'player.fantasyPoints' => fn ($q) => $q->whereHas('game', fn ($g) => $g->where('gameweek_id', $gameweek->id)),
            'fantasyTeamLineups' => fn ($q) => $q->where('gameweek_id', $gameweek->id),
        ])
            ->where('fantasy_team_id', $fantasyTeamId)
            ->where('gameweek_id', $gameweek->id)
            ->get();

        $this->squadPlayersResolved = true;

        return $this->squadPlayers;
    }

    /**
     * Transform fantasy player data for frontend consumption
     */
    public function transformFantasyPlayerData(FantasyPlayer $fantasyPlayer): array
    {
        $player = $fantasyPlayer->player;
        // Use correct column from player_market_values and current gameweek eager load
        $marketValue = $player->marketValues->first()?->market_value ?? 0;
        $currentTeam = $player->currentTeam();
        $points = $player->fantasyPoints->sum('points');

        return [
            'id' => $player->id,
            'name' => $player->name,
            // Optional extended fields for richer player info
            'name_ar' => $player->name_ar ?? null,
            'birthday' => isset($player->birthday) ? (string) $player->birthday : null,
            'country' => $player->country ?? null,
            'image' => $player->image ?? null,
            'position' => $player->position->value,
            // Normalize price to millions for frontend consistency and include raw market_value
            'price' => $marketValue > 0 ? ($marketValue / 1000000) : 0,
            'market_value' => $marketValue,
            'team' => $currentTeam ? $currentTeam->name : 'Unknown',
            'team_data' => $currentTeam ? $currentTeam->toArray() : null,
            'is_captain' => $fantasyPlayer->is_captain,
            'is_vice_captain' => $fantasyPlayer->is_vice_captain,
            'fantasy_player_id' => $fantasyPlayer->id,
            'points' => $points,
        ];
    }

    /**
     * Transform regular player data for frontend consumption
     */
    public function transformPlayerData(Player $player, $currentGameweek): array
    {
        // Use eager-loaded marketValues to avoid N+1 queries (similar to original TransferController logic)
        $marketValue = $player->marketValues->first()?->market_value ?? 5000000; // Default 5M if no market value

        // Get the first team (assuming current team is the first one)
        $currentTeam = $player->currentTeam();

        return [
            'id' => $player->id,
            'name' => $player->name,
            // Optional extended fields for richer player info
            'name_ar' => $player->name_ar ?? null,
            'birthday' => isset($player->birthday) ? (string) $player->birthday : null,
            'country' => $player->country ?? null,
            'image' => $player->image ?? null,
            'position' => $player->position->value,
            'price' => $marketValue / 1000000, // Convert to millions (match original format)
            'market_value' => $marketValue, // Include for sorting
            'team' => $currentTeam ? $currentTeam->name : 'Unknown',
            'team_data' => $currentTeam ? $currentTeam->toArray() : null,
        ];
    }

    /**
     * Get squad players data for a fantasy team and gameweek
     */
    public function getSquadPlayersData($fantasyTeamId, $currentGameweek): \Illuminate\Support\Collection
    {
        return $this->getSquadPlayers($fantasyTeamId, $currentGameweek)
            ->map(function ($fantasyPlayer) {
                return $this->transformFantasyPlayerData($fantasyPlayer);
            });
    }

    /**
     * Get available players data (excluding current squad players)
     */
    public function getAvailablePlayersData(Request $request, array $excludePlayerIds, $currentGameweek, $competition = null): LengthAwarePaginator
    {
        $query = Player::with(['teams', 'marketValues' => function ($query) use ($currentGameweek) {
            $query->where('gameweek_id', $currentGameweek->id);
        }])
            ->whereNotIn('id', $excludePlayerIds);

        // Filter by competition if provided
        if ($competition) {
            $query->whereHas('teams', function ($query) use ($competition) {
                // Teams are related to seasons, and seasons are related to competitions
                $query->whereHas('seasons', function ($q) use ($competition) {
                    $q->where('seasons.competition_id', $competition->id);
                });
            });
        }

        // Filter by Position
        if ($request->filled('position') && $request->input('position') !== 'all') {
            // This assumes your 'players' table has a 'position' column
            // that matches the values like 'FWD', 'MID', etc.
            // If not, you'll need to map them back.
            $query->where('position', $request->input('position'));
        }

        // Filter by Team
        if ($request->filled('team') && $request->input('team') !== 'all') {
            $teamName = $request->input('team');
            $query->whereHas('teams', function ($q) use ($teamName) {
                $q->where('name', $teamName);
            });
        }

        // Filter by Max Price
        if ($request->filled('maxPrice')) {
            $maxPrice = (float) $request->input('maxPrice');
            // UI sends maxPrice in millions; convert to raw before comparing against market_value
            $maxPriceRaw = (int) round($maxPrice * 1000000);
            // Ensure you only check the price for the current gameweek
            $query->whereHas('marketValues', function ($q) use ($maxPriceRaw, $currentGameweek) {
                $q->where('gameweek_id', $currentGameweek->id)->where('market_value', '<=', $maxPriceRaw);
            });
        }

        $paginatedPlayers = $query->paginate(14)->withQueryString();

        $paginatedPlayers->getCollection()->transform(function ($player) use ($currentGameweek) {
            return $this->transformPlayerData($player, $currentGameweek);
        });

        return $paginatedPlayers;
    }

    /**
     * Get substitute priority based on player position
     */
    public function getSubstitutePriority(string $position): int
    {
        return match ($position) {
            'GK' => 1,
            'DEF' => 2,
            'MID' => 3,
            'FWD' => 4,
            default => 1,
        };
    }
}

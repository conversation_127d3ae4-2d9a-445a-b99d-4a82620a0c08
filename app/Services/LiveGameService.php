<?php

namespace App\Services;

use App\Models\LiveGame;
use App\Models\Lineup;
use App\Models\LineupPlayer;
use App\Models\PlayerPerformance;
use App\Models\Game;
use App\Enums\LiveGameStatus;
use App\Enums\GameStatus;
use App\Enums\PlayerStatus;
use App\Enums\PlayerPosition;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LiveGameService
{
    /**
     * Create a new live game from an existing game
     */
    public function createLiveGame(Game $game): LiveGame
    {
        return DB::transaction(function () use ($game) {
            // Check if live game already exists
            if ($game->liveGame) {
                throw new \Exception('Live game already exists for this match.');
            }

            // Create live game
            $liveGame = LiveGame::create([
                'game_id' => $game->id,
                'status' => LiveGameStatus::PRE_MATCH,
                'current_minute' => 0,
                'added_time' => 0,
            ]);

            // Create empty lineups for both teams
            Lineup::create([
                'live_game_id' => $liveGame->id,
                'team_id' => $game->home_team_id,
                'formation' => '4-4-2',
            ]);

            Lineup::create([
                'live_game_id' => $liveGame->id,
                'team_id' => $game->away_team_id,
                'formation' => '4-4-2',
            ]);

            Log::info('Live game created', ['live_game_id' => $liveGame->id, 'game_id' => $game->id]);

            return $liveGame;
        });
    }

    /**
     * Start a live game with validation
     */
    public function startLiveGame(LiveGame $liveGame): void
    {
        DB::transaction(function () use ($liveGame) {
            // Validate pre-conditions
            $this->validateGameCanStart($liveGame);

            // Start the game
            $liveGame->update([
                'status' => LiveGameStatus::LIVE,
                'kick_off_time' => now(),
                'current_minute' => 1,
            ]);

            // Update main game status
            $liveGame->game->update(['status' => GameStatus::IN_PROGRESS]);

            // Add kick-off event
            $liveGame->addEvent([
                'type' => 'kick_off',
                'minute' => 0,
                'description' => 'Match started',
                'team_id' => null,
                'player_id' => null,
            ]);

            Log::info('Live game started', ['live_game_id' => $liveGame->id]);
        });
    }

    /**
     * End a live game and sync all data
     */
    public function endLiveGame(LiveGame $liveGame): void
    {
        DB::transaction(function () use ($liveGame) {
            // End the game
            $liveGame->update([
                'status' => LiveGameStatus::FINISHED,
                'full_time' => now(),
            ]);

            // Update main game status
            $liveGame->game->update(['status' => GameStatus::FINISHED]);

            // Add full-time event
            $liveGame->addEvent([
                'type' => 'full_time',
                'minute' => $liveGame->current_minute,
                'description' => 'Match ended',
                'team_id' => null,
                'player_id' => null,
            ]);

            // Sync all player performances
            $this->syncAllPlayerPerformances($liveGame);

            Log::info('Live game ended', ['live_game_id' => $liveGame->id]);
        });
    }

    /**
     * Record a goal with all related updates
     */
    public function recordGoal(LiveGame $liveGame, array $data): void
    {
        DB::transaction(function () use ($liveGame, $data) {
            $lineupPlayer = LineupPlayer::find($data['lineup_player_id']);
            
            // Record goal in lineup player stats
            $lineupPlayer->recordGoal($data['minute'], $data['goal_type']);
            
            // Add event to live game
            $liveGame->addEvent([
                'type' => 'goal',
                'minute' => $data['minute'],
                'description' => $this->getGoalDescription($lineupPlayer, $data),
                'team_id' => $lineupPlayer->lineup->team_id,
                'player_id' => $lineupPlayer->player_id,
                'lineup_player_id' => $lineupPlayer->id,
                'goal_type' => $data['goal_type'],
                'assist_player_id' => $data['assist_player_id'] ?? null,
            ]);

            // Update game score
            $this->updateGameScore($liveGame, $lineupPlayer->lineup->team_id, $data['goal_type']);

            Log::info('Goal recorded', [
                'live_game_id' => $liveGame->id,
                'player_id' => $lineupPlayer->player_id,
                'minute' => $data['minute'],
                'type' => $data['goal_type']
            ]);
        });
    }

    /**
     * Record a card with validation
     */
    public function recordCard(LiveGame $liveGame, array $data): void
    {
        DB::transaction(function () use ($liveGame, $data) {
            $lineupPlayer = LineupPlayer::find($data['lineup_player_id']);
            
            // Give card to player
            $lineupPlayer->giveCard($data['card_type'], $data['minute']);
            
            // Add event to live game
            $liveGame->addEvent([
                'type' => 'card',
                'minute' => $data['minute'],
                'description' => $this->getCardDescription($lineupPlayer, $data),
                'team_id' => $lineupPlayer->lineup->team_id,
                'player_id' => $lineupPlayer->player_id,
                'lineup_player_id' => $lineupPlayer->id,
                'card_type' => $data['card_type'],
                'reason' => $data['reason'] ?? null,
            ]);

            Log::info('Card recorded', [
                'live_game_id' => $liveGame->id,
                'player_id' => $lineupPlayer->player_id,
                'minute' => $data['minute'],
                'type' => $data['card_type']
            ]);
        });
    }

    /**
     * Record a substitution with validation
     */
    public function recordSubstitution(LiveGame $liveGame, array $data): void
    {
        DB::transaction(function () use ($liveGame, $data) {
            $playerOut = LineupPlayer::find($data['player_out_id']);
            $playerIn = LineupPlayer::find($data['player_in_id']);
            
            // Validate substitution
            $this->validateSubstitution($playerOut, $playerIn);
            
            // Perform substitution
            $playerIn->substitute($playerOut, $data['minute']);
            
            // Add event to live game
            $liveGame->addEvent([
                'type' => 'substitution',
                'minute' => $data['minute'],
                'description' => $this->getSubstitutionDescription($playerIn, $playerOut),
                'team_id' => $playerOut->lineup->team_id,
                'player_out_id' => $playerOut->player_id,
                'player_in_id' => $playerIn->player_id,
                'lineup_player_out_id' => $playerOut->id,
                'lineup_player_in_id' => $playerIn->id,
            ]);

            Log::info('Substitution recorded', [
                'live_game_id' => $liveGame->id,
                'player_out_id' => $playerOut->player_id,
                'player_in_id' => $playerIn->player_id,
                'minute' => $data['minute']
            ]);
        });
    }

    /**
     * Validate that a game can be started
     */
    private function validateGameCanStart(LiveGame $liveGame): void
    {
        if ($liveGame->status !== LiveGameStatus::PRE_MATCH) {
            throw new \Exception('Game is not in pre-match status.');
        }

        $homeLineup = $liveGame->homeLineup();
        $awayLineup = $liveGame->awayLineup();

        if (!$homeLineup || !$awayLineup) {
            throw new \Exception('Both team lineups must be set before starting the match.');
        }

        if ($homeLineup->startingXI()->count() < 11 || $awayLineup->startingXI()->count() < 11) {
            throw new \Exception('Both teams must have 11 starting players.');
        }

        // Validate formation requirements
        $this->validateLineupFormation($homeLineup);
        $this->validateLineupFormation($awayLineup);
    }

    /**
     * Validate lineup formation (must have GK, and proper position distribution)
     */
    private function validateLineupFormation(Lineup $lineup): void
    {
        $startingPlayers = $lineup->startingXI()->get();
        
        $positionCounts = $startingPlayers->groupBy('position')->map->count();
        
        // Must have exactly 1 goalkeeper
        if (($positionCounts[PlayerPosition::GOALKEEPER->value] ?? 0) !== 1) {
            throw new \Exception('Team must have exactly 1 goalkeeper in starting XI.');
        }

        // Must have at least 3 defenders
        if (($positionCounts[PlayerPosition::DEFENDER->value] ?? 0) < 3) {
            throw new \Exception('Team must have at least 3 defenders in starting XI.');
        }

        // Must have at least 2 midfielders
        if (($positionCounts[PlayerPosition::MIDFIELDER->value] ?? 0) < 2) {
            throw new \Exception('Team must have at least 2 midfielders in starting XI.');
        }

        // Must have at least 1 forward
        if (($positionCounts[PlayerPosition::FORWARD->value] ?? 0) < 1) {
            throw new \Exception('Team must have at least 1 forward in starting XI.');
        }
    }

    /**
     * Validate substitution rules
     */
    private function validateSubstitution(LineupPlayer $playerOut, LineupPlayer $playerIn): void
    {
        if ($playerOut->lineup_id !== $playerIn->lineup_id) {
            throw new \Exception('Players must be from the same team.');
        }

        if (!$playerOut->status->canBeSubstituted()) {
            throw new \Exception('Player cannot be substituted.');
        }

        if ($playerIn->status !== PlayerStatus::SUBSTITUTE) {
            throw new \Exception('Incoming player must be a substitute.');
        }
    }

    /**
     * Sync all player performances from live stats
     */
    private function syncAllPlayerPerformances(LiveGame $liveGame): void
    {
        foreach ($liveGame->lineups as $lineup) {
            foreach ($lineup->players as $lineupPlayer) {
                $this->syncPlayerPerformance($liveGame, $lineupPlayer);
            }
        }
    }

    /**
     * Sync individual player performance
     */
    private function syncPlayerPerformance(LiveGame $liveGame, LineupPlayer $lineupPlayer): void
    {
        $liveStats = $lineupPlayer->live_stats ?? [];
        
        PlayerPerformance::updateOrCreate(
            [
                'game_id' => $liveGame->game_id,
                'player_id' => $lineupPlayer->player_id,
                'team_id' => $lineupPlayer->lineup->team_id,
            ],
            [
                'minutes_played' => $this->calculateMinutesPlayed($lineupPlayer, $liveGame),
                'goals_scored' => count($liveStats['goals'] ?? []),
                'assists' => count($liveStats['assists'] ?? []),
                'yellow_cards' => count(array_filter($liveStats['cards'] ?? [], fn($card) => $card['type'] === 'yellow')),
                'red_cards' => count(array_filter($liveStats['cards'] ?? [], fn($card) => $card['type'] === 'red')),
                'clean_sheet' => $this->calculateCleanSheet($lineupPlayer, $liveGame),
                'goals_conceded' => $liveStats['goals_conceded'] ?? 0,
                'own_goals' => count(array_filter($liveStats['goals'] ?? [], fn($goal) => $goal['type'] === 'own_goal')),
                'penalities_saved' => $liveStats['penalties_saved'] ?? 0,
                'penalities_missed' => count(array_filter($liveStats['goals'] ?? [], fn($goal) => $goal['type'] === 'penalty_missed')),
                'penalties_caused' => $liveStats['penalties_caused'] ?? 0,
                'penalties_committed' => $liveStats['penalties_committed'] ?? 0,
                'saves' => $liveStats['saves'] ?? 0,
            ]
        );
    }

    private function calculateMinutesPlayed(LineupPlayer $lineupPlayer, LiveGame $liveGame): int
    {
        if ($lineupPlayer->status === PlayerStatus::STARTING) {
            return $lineupPlayer->substitution_minute ?? $liveGame->current_minute;
        } elseif ($lineupPlayer->status === PlayerStatus::SUBSTITUTED_IN) {
            return $liveGame->current_minute - ($lineupPlayer->substitution_minute ?? 0);
        }
        
        return 0;
    }

    private function calculateCleanSheet(LineupPlayer $lineupPlayer, LiveGame $liveGame): bool
    {
        if (!in_array($lineupPlayer->position, [PlayerPosition::GOALKEEPER, PlayerPosition::DEFENDER])) {
            return false;
        }
        
        $teamId = $lineupPlayer->lineup->team_id;
        $isHomeTeam = $liveGame->game->home_team_id === $teamId;
        $goalsConceded = $isHomeTeam ? $liveGame->game->away_score : $liveGame->game->home_score;
        
        return $goalsConceded === 0;
    }

    private function updateGameScore(LiveGame $liveGame, int $teamId, string $goalType): void
    {
        if ($goalType === 'own_goal') {
            $teamId = $teamId === $liveGame->game->home_team_id 
                ? $liveGame->game->away_team_id 
                : $liveGame->game->home_team_id;
        }

        $isHomeTeam = $teamId === $liveGame->game->home_team_id;
        
        if ($isHomeTeam) {
            $liveGame->game->increment('home_score');
        } else {
            $liveGame->game->increment('away_score');
        }
    }

    private function getGoalDescription(LineupPlayer $lineupPlayer, array $data): string
    {
        $goalType = match($data['goal_type']) {
            'penalty' => ' (Penalty)',
            'own_goal' => ' (Own Goal)',
            'free_kick' => ' (Free Kick)',
            default => ''
        };
        
        return $lineupPlayer->player->name . ' scored' . $goalType;
    }

    private function getCardDescription(LineupPlayer $lineupPlayer, array $data): string
    {
        $cardType = ucfirst($data['card_type']);
        $reason = $data['reason'] ? ' - ' . $data['reason'] : '';
        
        return $lineupPlayer->player->name . ' received ' . $cardType . ' card' . $reason;
    }

    private function getSubstitutionDescription(LineupPlayer $playerIn, LineupPlayer $playerOut): string
    {
        return $playerIn->player->name . ' substituted in for ' . $playerOut->player->name;
    }
}

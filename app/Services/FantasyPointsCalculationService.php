<?php

namespace App\Services;

use App\Models\PlayerPerformance;
use App\Models\Player;
use App\Models\Game;

class FantasyPointsCalculationService
{
    /**
     * Calculate fantasy points for a player performance based on the official rules
     */
    public function calculatePoints(PlayerPerformance $performance, Player $player): array
    {
        $breakdown = [];
        $totalPoints = 0;

        // Get player position
        $position = $player->position->value;

        // Minutes played points
        $minutesPoints = $this->getMinutesPoints($performance->minutes_played);
        if ($minutesPoints > 0) {
            $breakdown[] = [
                'action' => $this->getMinutesDescription($performance->minutes_played),
                'points' => $minutesPoints,
                'count' => 1
            ];
            $totalPoints += $minutesPoints;
        }

        // Goals scored
        if ($performance->goals_scored > 0) {
            $goalPoints = $this->getGoalPoints($position) * $performance->goals_scored;
            $breakdown[] = [
                'action' => 'Goal scored',
                'points' => $this->getGoalPoints($position),
                'count' => $performance->goals_scored
            ];
            $totalPoints += $goalPoints;
        }

        // Assists
        if ($performance->assists > 0) {
            $assistPoints = $this->getAssistPoints($position) * $performance->assists;
            $breakdown[] = [
                'action' => 'Assist',
                'points' => $this->getAssistPoints($position),
                'count' => $performance->assists
            ];
            $totalPoints += $assistPoints;
        }

        // Clean sheet (only if played 60+ minutes)
        if ($performance->clean_sheet && $performance->minutes_played >= 60) {
            $cleanSheetPoints = $this->getCleanSheetPoints($position);
            if ($cleanSheetPoints > 0) {
                $breakdown[] = [
                    'action' => 'Clean sheet',
                    'points' => $cleanSheetPoints,
                    'count' => 1
                ];
                $totalPoints += $cleanSheetPoints;
            }
        }

        // Yellow cards
        if ($performance->yellow_cards > 0) {
            $yellowCardPoints = -1 * $performance->yellow_cards;
            $breakdown[] = [
                'action' => 'Yellow card',
                'points' => -1,
                'count' => $performance->yellow_cards
            ];
            $totalPoints += $yellowCardPoints;
        }

        // Red cards
        if ($performance->red_cards > 0) {
            $redCardPoints = -5 * $performance->red_cards; // Assuming direct red card
            $breakdown[] = [
                'action' => 'Red card (direct)',
                'points' => -5,
                'count' => $performance->red_cards
            ];
            $totalPoints += $redCardPoints;
        }

        // Penalties saved (goalkeepers only)
        if ($performance->penalities_saved > 0 && $position === 'GK') {
            $penaltySavedPoints = 5 * $performance->penalities_saved;
            $breakdown[] = [
                'action' => 'Penalty saved',
                'points' => 5,
                'count' => $performance->penalities_saved
            ];
            $totalPoints += $penaltySavedPoints;
        }

        // Penalties caused (earned)
        if ($performance->penalties_caused > 0) {
            $penaltyCausedPoints = 2 * $performance->penalties_caused;
            $breakdown[] = [
                'action' => 'Penalty earned',
                'points' => 2,
                'count' => $performance->penalties_caused
            ];
            $totalPoints += $penaltyCausedPoints;
        }

        // Penalties missed
        if ($performance->penalities_missed > 0) {
            $penaltyMissedPoints = -3 * $performance->penalities_missed;
            $breakdown[] = [
                'action' => 'Penalty missed',
                'points' => -3,
                'count' => $performance->penalities_missed
            ];
            $totalPoints += $penaltyMissedPoints;
        }

        // Penalties committed
        if ($performance->penalties_committed > 0) {
            $penaltyCommittedPoints = -1 * $performance->penalties_committed;
            $breakdown[] = [
                'action' => 'Penalty committed',
                'points' => -1,
                'count' => $performance->penalties_committed
            ];
            $totalPoints += $penaltyCommittedPoints;
        }

        // Own goals
        if ($performance->own_goals > 0) {
            $ownGoalPoints = -2 * $performance->own_goals;
            $breakdown[] = [
                'action' => 'Own goal',
                'points' => -2,
                'count' => $performance->own_goals
            ];
            $totalPoints += $ownGoalPoints;
        }

        // Goals conceded (for GK and DEF, starting from 2nd goal)
        if ($performance->goals_conceded > 1 && in_array($position, ['GK', 'DEF'])) {
            $goalsConcededPenalty = -1 * ($performance->goals_conceded - 1);
            $breakdown[] = [
                'action' => 'Goals conceded (from 2nd goal)',
                'points' => -1,
                'count' => $performance->goals_conceded - 1
            ];
            $totalPoints += $goalsConcededPenalty;
        }

        // Hat-trick bonus (3+ goals)
        if ($performance->goals_scored >= 3) {
            $breakdown[] = [
                'action' => 'Hat-trick bonus',
                'points' => 5,
                'count' => 1
            ];
            $totalPoints += 5;
        }

        // Game result bonus (win/draw) - this would need game result data
        $gameResultPoints = $this->getGameResultPoints($performance);
        if ($gameResultPoints > 0) {
            $breakdown[] = [
                'action' => $gameResultPoints === 3 ? 'Victory' : 'Draw',
                'points' => $gameResultPoints,
                'count' => 1
            ];
            $totalPoints += $gameResultPoints;
        }

        return [
            'total_points' => $totalPoints,
            'breakdown' => $breakdown
        ];
    }

    /**
     * Get points for minutes played
     */
    private function getMinutesPoints(int $minutes): int
    {
        if ($minutes >= 61) return 3;
        if ($minutes >= 31) return 2;
        if ($minutes >= 1) return 1;
        return 0;
    }

    /**
     * Get description for minutes played
     */
    private function getMinutesDescription(int $minutes): string
    {
        if ($minutes >= 61) return "Played 61+ minutes";
        if ($minutes >= 31) return "Played 31-60 minutes";
        if ($minutes >= 1) return "Played 1-30 minutes";
        return "Did not play";
    }

    /**
     * Get points for goals based on position
     */
    private function getGoalPoints(string $position): int
    {
        return match($position) {
            'GK' => 10,
            'DEF' => 7,
            'MID' => 6,
            'FWD' => 5,
            default => 5
        };
    }

    /**
     * Get points for assists based on position
     */
    private function getAssistPoints(string $position): int
    {
        return match($position) {
            'GK' => 7,
            'DEF' => 4,
            'MID' => 3,
            'FWD' => 2,
            default => 3
        };
    }

    /**
     * Get points for clean sheet based on position
     */
    private function getCleanSheetPoints(string $position): int
    {
        return match($position) {
            'GK' => 5,
            'DEF' => 3,
            'MID' => 1,
            'FWD' => 0,
            default => 0
        };
    }

    /**
     * Get game result points (simplified - would need actual game result)
     */
    private function getGameResultPoints(PlayerPerformance $performance): int
    {
        // This is a simplified version - in reality, we  need to check the actual game result
        // For now, we'll return 0 as we don't have the game result logic implemented
        return 0;
    }
}

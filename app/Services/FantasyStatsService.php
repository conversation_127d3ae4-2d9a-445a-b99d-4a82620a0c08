<?php

namespace App\Services;

use App\Models\FantasyTeam;
use App\Models\FantasyTeamGameweek;
use App\Models\Gameweek;
use App\Models\RankingGlobal;
use App\Models\Tenant;

class FantasyStatsService
{
    /**
     * Get comprehensive statistics for a fantasy team in a specific gameweek
     */
    public function getFantasyTeamStats(FantasyTeam $fantasyTeam, Gameweek $gameweek): array
    {
        $currentTenant = Tenant::current();

        return [
            'gameweek_points' => $this->getGameweekPoints($fantasyTeam, $gameweek),
            'transfers_used' => $this->getTransfersUsed($fantasyTeam, $gameweek),
            'gameweek_rank' => $this->getGameweekRank($fantasyTeam, $gameweek, $currentTenant),
            'highest_points' => $this->getHighestGameweekPoints($fantasyTeam),
            'average_points' => $this->getAverageGameweekPoints($fantasyTeam),
            'total_points' => $this->getTotalPoints($fantasyTeam),
        ];
    }

    /**
     * Get points scored in a specific gameweek
     */
    public function getGameweekPoints(FantasyTeam $fantasyTeam, Gameweek $gameweek): int
    {
        $gameweekStats = FantasyTeamGameweek::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $gameweek->id)
            ->first();

        return $gameweekStats ? $gameweekStats->total_points : 0;
    }

    /**
     * Get number of transfers used in a specific gameweek
     */
    public function getTransfersUsed(FantasyTeam $fantasyTeam, Gameweek $gameweek): int
    {
        $gameweekStats = FantasyTeamGameweek::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $gameweek->id)
            ->first();

        return $gameweekStats ? $gameweekStats->transfers_made : 0;
    }

    /**
     * Get gameweek rank for the fantasy team
     */
    public function getGameweekRank(FantasyTeam $fantasyTeam, Gameweek $gameweek, ?Tenant $tenant = null): ?int
    {
        $query = RankingGlobal::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $gameweek->id);

        if ($tenant) {
            $query->where('tenant_id', $tenant->id);
        }

        $ranking = $query->first();

        return $ranking?->rank_gameweek;
    }

    /**
     * Get the highest points scored in any gameweek by this fantasy team
     */
    public function getHighestGameweekPoints(FantasyTeam $fantasyTeam): int
    {
        $highestPoints = FantasyTeamGameweek::where('fantasy_team_id', $fantasyTeam->id)
            ->max('total_points');

        return $highestPoints ?: 0;
    }

    /**
     * Get the average points per gameweek for this fantasy team
     */
    public function getAverageGameweekPoints(FantasyTeam $fantasyTeam): float
    {
        $averagePoints = FantasyTeamGameweek::where('fantasy_team_id', $fantasyTeam->id)
            ->where('total_points', '>', 0) // Only count gameweeks where points were scored
            ->avg('total_points');

        return $averagePoints ? round($averagePoints, 1) : 0.0;
    }

    /**
     * Get total points across all gameweeks for this fantasy team
     */
    public function getTotalPoints(FantasyTeam $fantasyTeam): int
    {
        $totalPoints = FantasyTeamGameweek::where('fantasy_team_id', $fantasyTeam->id)
            ->sum('total_points');

        return $totalPoints ?: 0;
    }

    /**
     * Get overall rank for the fantasy team (latest available)
     */
    public function getOverallRank(FantasyTeam $fantasyTeam, ?Tenant $tenant = null): ?int
    {
        $query = RankingGlobal::where('fantasy_team_id', $fantasyTeam->id);

        if ($tenant) {
            $query->where('tenant_id', $tenant->id);
        }

        $ranking = $query->orderBy('gameweek_id', 'desc')->first();

        return $ranking?->rank;
    }

    /**
     * Get statistics for multiple gameweeks (useful for charts/trends)
     */
    public function getGameweekTrends(FantasyTeam $fantasyTeam, array $gameweekIds): array
    {
        $gameweekStats = FantasyTeamGameweek::where('fantasy_team_id', $fantasyTeam->id)
            ->whereIn('gameweek_id', $gameweekIds)
            ->with('gameweek')
            ->orderBy('gameweek_id')
            ->get();

        return $gameweekStats->map(fn ($stats) => [
            'gameweek_id' => $stats->gameweek_id,
            'gameweek_name' => $stats->gameweek->name,
            'points' => $stats->total_points,
            'transfers' => $stats->transfers_made,
            'transfer_cost' => $stats->transfer_cost_penalty,
        ])->toArray();
    }

    /**
     * Get league-specific statistics if the team is in leagues
     */
    public function getLeagueStats(FantasyTeam $fantasyTeam, Gameweek $gameweek): array
    {
        // Get all leagues this fantasy team is part of
        $leagues = $fantasyTeam->leagues;
        $leagueStats = [];

        foreach ($leagues as $league) {
            $leagueRanking = \App\Models\RankingLeague::where('fantasy_team_id', $fantasyTeam->id)
                ->where('league_id', $league->id)
                ->where('gameweek_id', $gameweek->id)
                ->first();

            if ($leagueRanking) {
                $leagueStats[] = [
                    'league_id' => $league->id,
                    'league_name' => $league->name,
                    'rank' => $leagueRanking->rank,
                    'gameweek_rank' => $leagueRanking->rank_gameweek,
                    'points' => $leagueRanking->points,
                    'gameweek_points' => $leagueRanking->points_gameweek,
                ];
            }
        }

        return $leagueStats;
    }
}

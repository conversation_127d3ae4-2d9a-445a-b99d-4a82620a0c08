<?php

namespace App\Services;

use App\Models\FantasyPoint;
use App\Models\Game;
use App\Models\Gameweek;
use App\Models\Player;
use App\Models\PlayerPerformance;

class PlayerPerformanceService
{
    protected FantasyPointsCalculationService $pointsCalculationService;

    public function __construct(FantasyPointsCalculationService $pointsCalculationService)
    {
        $this->pointsCalculationService = $pointsCalculationService;
    }

    /**
     * Get player performance data for a specific gameweek
     */
    public function getPlayerPerformanceForGameweek(Player $player, Gameweek $gameweek): ?array
    {
        // Find the game this player played in during the gameweek
        $performance = PlayerPerformance::where('player_id', $player->id)
            ->whereHas('game', function ($query) use ($gameweek) {
                $query->where('gameweek_id', $gameweek->id);
            })
            ->with(['game.homeTeam', 'game.awayTeam', 'team'])
            ->first();

        if (! $performance) {
            return null;
        }

        $game = $performance->game;

        // Get fantasy points for this player in this game
        $fantasyPoint = FantasyPoint::where('player_id', $player->id)
            ->where('game_id', $game->id)
            ->first();

        // Calculate points breakdown using the calculation service
        $pointsData = $this->pointsCalculationService->calculatePoints($performance, $player);

        return [
            'player' => [
                'id' => $player->id,
                'name' => $player->name,
                'position' => $player->position->value,
            ],
            'game' => [
                'id' => $game->id,
                'home_team' => [
                    'id' => $game->homeTeam->id,
                    'name' => $game->homeTeam->name,
                    'short_name' => $game->homeTeam->short_name,
                    'logo' => $this->getTeamLogoUrl($game->homeTeam->logo),
                ],
                'away_team' => [
                    'id' => $game->awayTeam->id,
                    'name' => $game->awayTeam->name,
                    'short_name' => $game->awayTeam->short_name,
                    'logo' => $this->getTeamLogoUrl($game->awayTeam->logo),
                ],
                'home_score' => $game->home_score,
                'away_score' => $game->away_score,
                'status' => $game->status->value,
                'game_date' => $game->game_date?->format('Y-m-d H:i'),
            ],
            'performance' => [
                'minutes_played' => $performance->minutes_played,
                'goals_scored' => $performance->goals_scored,
                'assists' => $performance->assists,
                'clean_sheet' => $performance->clean_sheet,
                'goals_conceded' => $performance->goals_conceded,
                'own_goals' => $performance->own_goals,
                'penalties_saved' => $performance->penalities_saved,
                'penalties_missed' => $performance->penalities_missed,
                'penalties_caused' => $performance->penalties_caused,
                'penalties_committed' => $performance->penalties_committed,
                'saves' => $performance->saves,
                'yellow_cards' => $performance->yellow_cards,
                'red_cards' => $performance->red_cards,
            ],
            'team_played_for' => [
                'id' => $performance->team->id,
                'name' => $performance->team->name,
                'short_name' => $performance->team->short_name,
                'logo' => $this->getTeamLogoUrl($performance->team->logo),
            ],
            'fantasy_points' => [
                'total' => $fantasyPoint?->points ?? $pointsData['total_points'],
                'breakdown' => $pointsData['breakdown'],
                'calculated_total' => $pointsData['total_points'],
            ],
        ];
    }

    /**
     * Get multiple players' performance data for a gameweek
     */
    public function getMultiplePlayersPerformanceForGameweek(array $playerIds, Gameweek $gameweek): array
    {
        $results = [];

        foreach ($playerIds as $playerId) {
            $player = Player::find($playerId);
            if ($player) {
                $performance = $this->getPlayerPerformanceForGameweek($player, $gameweek);
                if ($performance) {
                    $results[$playerId] = $performance;
                }
            }
        }

        return $results;
    }

    /**
     * Check if player played in the gameweek
     */
    public function didPlayerPlayInGameweek(Player $player, Gameweek $gameweek): bool
    {
        return PlayerPerformance::where('player_id', $player->id)
            ->whereHas('game', function ($query) use ($gameweek) {
                $query->where('gameweek_id', $gameweek->id);
            })
            ->where('minutes_played', '>', 0)
            ->exists();
    }

    /**
     * Get player's team for a specific game (handles team changes)
     */
    public function getPlayerTeamForGame(Player $player, Game $game): ?array
    {
        $performance = PlayerPerformance::where('player_id', $player->id)
            ->where('game_id', $game->id)
            ->with('team')
            ->first();

        if (! $performance || ! $performance->team) {
            return null;
        }

        return [
            'id' => $performance->team->id,
            'name' => $performance->team->name,
            'short_name' => $performance->team->short_name,
            'logo' => $this->getTeamLogoUrl($performance->team->logo),
        ];
    }

    /**
     * Get the proper URL for a team logo
     */
    private function getTeamLogoUrl(?string $logoPath): ?string
    {
        if (! $logoPath) {
            return null;
        }

        // If the logo path starts with '/', it's an old format (public path)
        if (str_starts_with($logoPath, '/')) {
            return asset($logoPath);
        }

        // Otherwise, it's a storage path
        return asset('storage/'.$logoPath);
    }
}

<?php

namespace App\Services;

use App\Models\Competition;
use App\Models\FantasyTeam;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class FantasyContextService
{
    protected ?Competition $competition = null;

    protected bool $competitionResolved = false;

    protected ?FantasyTeam $fantasyTeam = null;

    protected bool $fantasyTeamResolved = false;

    protected ?Collection $tenantCompetitions = null;

    protected bool $tenantCompetitionsResolved = false;

    public function getTenantCompetitions(): ?Collection
    {
        if ($this->tenantCompetitionsResolved) {
            return $this->tenantCompetitions;
        }

        $tenant = Tenant::current();
        if (! $tenant) {
            $this->tenantCompetitionsResolved = true;

            return null;
        }

        $this->tenantCompetitions = $tenant->competitions()->select('id', 'name', 'slug')->get();
        $this->tenantCompetitionsResolved = true;

        return $this->tenantCompetitions;
    }

    public function getCurrentCompetition(Request $request): ?Competition
    {
        if ($this->competitionResolved) {
            return $this->competition;
        }

        $tenant = Tenant::current();
        if (! $tenant) {
            $this->competitionResolved = true;

            return null;
        }

        $currentCompetitionId = $request->session()->get('current_competition_id');
        if (! $currentCompetitionId) {
            $this->competitionResolved = true;

            return null;
        }

        $this->competition = Competition::with(['currentSeason.currentSeasonPhase.currentGameweek'])
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->find($currentCompetitionId);

        $this->competitionResolved = true;

        return $this->competition;
    }

    public function getUserFantasyTeam(Request $request): ?FantasyTeam
    {
        if ($this->fantasyTeamResolved) {
            return $this->fantasyTeam;
        }

        $user = Auth::user();
        if (! $user) {
            $this->fantasyTeamResolved = true;

            return null;
        }

        $competition = $this->getCurrentCompetition($request);
        if (! $competition || ! $competition->currentSeason) {
            $this->fantasyTeamResolved = true;

            return null;
        }
        $this->fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $competition->currentSeason->id)
            ->first();

        $this->fantasyTeamResolved = true;

        return $this->fantasyTeam;
    }

    /**
     * Get the complete fantasy context (competition, season, phase, gameweek)
     *
     * @return array|null Returns array with context data or null if invalid
     */
    public function getFantasyContext(Request $request): ?array
    {
        $competition = $this->getCurrentCompetition($request);

        if (
            ! $competition ||
            ! $competition->currentSeason ||
            ! $competition->currentSeason->currentSeasonPhase ||
            ! $competition->currentSeason->currentSeasonPhase->currentGameweek
        ) {
            return null;
        }

        return [
            'tenant' => Tenant::current(),
            'competition' => $competition,
            'currentSeason' => $competition->currentSeason,
            'currentSeasonPhase' => $competition->currentSeason->currentSeasonPhase,
            'currentGameweek' => $competition->currentSeason->currentSeasonPhase->currentGameweek,
        ];
    }

    /**
     * Get fantasy context with error messages for redirects
     *
     * @return array Returns ['success' => bool, 'data' => array|null, 'error' => string|null]
     */
    public function getFantasyContextWithErrors(Request $request): array
    {
        $tenant = Tenant::current();
        if (! $tenant) {
            return [
                'success' => false,
                'data' => null,
                'error' => 'No tenant context available.',
            ];
        }

        // Get current competition from session
        $currentCompetitionId = $request->session()->get('current_competition_id');
        if (! $currentCompetitionId) {
            return [
                'success' => false,
                'data' => null,
                'error' => 'No competition selected.',
            ];
        }

        $competition = $this->getCurrentCompetition($request);

        if (! $competition) {
            return [
                'success' => false,
                'data' => null,
                'error' => 'No active competition found.',
            ];
        }

        $currentSeason = $competition->currentSeason;
        if (! $currentSeason) {
            return [
                'success' => false,
                'data' => null,
                'error' => 'No active season found.',
            ];
        }

        $currentSeasonPhase = $currentSeason->currentSeasonPhase;
        if (! $currentSeasonPhase) {
            return [
                'success' => false,
                'data' => null,
                'error' => 'No active season phase found.',
            ];
        }

        $currentGameweek = $currentSeasonPhase->currentGameweek;
        if (! $currentGameweek) {
            return [
                'success' => false,
                'data' => null,
                'error' => 'No active gameweek found.',
            ];
        }

        return [
            'success' => true,
            'data' => [
                'tenant' => $tenant,
                'competition' => $competition,
                'currentSeason' => $currentSeason,
                'currentSeasonPhase' => $currentSeasonPhase,
                'currentGameweek' => $currentGameweek,
            ],
            'error' => null,
        ];
    }
}

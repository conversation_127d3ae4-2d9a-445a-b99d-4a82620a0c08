<?php

namespace App\Services;

use App\Models\FantasyPlayer;
use App\Models\FantasyTeam;

class FantasyValidationService
{
    protected PlayerDataService $playerDataService;

    public function __construct(PlayerDataService $playerDataService)
    {
        $this->playerDataService = $playerDataService;
    }

    /**
     * Check if the fantasy team has a valid squad for the given gameweek
     */
    public function hasValidSquad(FantasyTeam $fantasyTeam, $gameweek): bool
    {
        // Get all fantasy players for this team and gameweek
        $fantasyPlayers = FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $gameweek->id)
            ->with(['player', 'fantasyTeamLineups'])
            ->get();

        // Must have exactly 15 players
        if ($fantasyPlayers->count() !== 15) {
            return false;
        }

        // Count starting and bench players
        $startingCount = 0;
        $benchCount = 0;
        $unassignedCount = 0;

        foreach ($fantasyPlayers as $fantasyPlayer) {
            $lineupEntries = $fantasyPlayer->fantasyTeamLineups;

            if ($lineupEntries->isEmpty()) {
                $unassignedCount++;
            } else {
                $lineupEntry = $lineupEntries->first();
                if ($lineupEntry && $lineupEntry->position === 'starting') {
                    $startingCount++;
                } elseif ($lineupEntry && $lineupEntry->position === 'bench') {
                    $benchCount++;
                } else {
                    $unassignedCount++;
                }
            }
        }

        // Must have exactly 11 starting, 4 bench, 0 unassigned
        return $startingCount === 11 && $benchCount === 4 && $unassignedCount === 0;
    }

    /**
     * Validate squad composition by position
     */
    public function validateSquadComposition(array $playerIds): array
    {
        $players = \App\Models\Player::whereIn('id', $playerIds)->get();
        
        $positionCounts = [
            'GK' => 0,
            'DEF' => 0,
            'MID' => 0,
            'FWD' => 0,
        ];

        foreach ($players as $player) {
            $normalizedPosition = $this->playerDataService->normalizePosition($player->position->value);
            $positionCounts[$normalizedPosition]++;
        }

        $errors = [];

        // Check position requirements (2 GK, 5 DEF, 5 MID, 3 FWD)
        if ($positionCounts['GK'] !== 2) {
            $errors[] = "Must have exactly 2 goalkeepers (currently have {$positionCounts['GK']})";
        }
        if ($positionCounts['DEF'] !== 5) {
            $errors[] = "Must have exactly 5 defenders (currently have {$positionCounts['DEF']})";
        }
        if ($positionCounts['MID'] !== 5) {
            $errors[] = "Must have exactly 5 midfielders (currently have {$positionCounts['MID']})";
        }
        if ($positionCounts['FWD'] !== 3) {
            $errors[] = "Must have exactly 3 forwards (currently have {$positionCounts['FWD']})";
        }

        // Check total squad size
        if (count($playerIds) !== 15) {
            $errors[] = "Squad must have exactly 15 players (currently have " . count($playerIds) . ")";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'positionCounts' => $positionCounts,
        ];
    }

    /**
     * Validate lineup composition (starting XI)
     */
    public function validateLineupComposition(array $startingPlayerIds): array
    {
        if (count($startingPlayerIds) !== 11) {
            return [
                'valid' => false,
                'errors' => ['Starting lineup must have exactly 11 players'],
            ];
        }

        $players = \App\Models\Player::whereIn('id', $startingPlayerIds)->get();
        
        $positionCounts = [
            'GK' => 0,
            'DEF' => 0,
            'MID' => 0,
            'FWD' => 0,
        ];

        foreach ($players as $player) {
            $normalizedPosition = $this->playerDataService->normalizePosition($player->position->value);
            $positionCounts[$normalizedPosition]++;
        }

        $errors = [];

        // Check minimum requirements for starting XI
        if ($positionCounts['GK'] !== 1) {
            $errors[] = "Starting lineup must have exactly 1 goalkeeper";
        }
        if ($positionCounts['DEF'] < 3) {
            $errors[] = "Starting lineup must have at least 3 defenders";
        }
        if ($positionCounts['FWD'] < 1) {
            $errors[] = "Starting lineup must have at least 1 forward";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'positionCounts' => $positionCounts,
        ];
    }

    /**
     * Validate captain and vice-captain selections
     */
    public function validateCaptainSelection(array $startingPlayerIds, ?int $captainId, ?int $viceCaptainId): array
    {
        $errors = [];

        if ($captainId && !in_array($captainId, $startingPlayerIds)) {
            $errors[] = 'Captain must be in starting lineup';
        }

        if ($viceCaptainId && !in_array($viceCaptainId, $startingPlayerIds)) {
            $errors[] = 'Vice captain must be in starting lineup';
        }

        if ($captainId && $viceCaptainId && $captainId === $viceCaptainId) {
            $errors[] = 'Captain and vice captain must be different players';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Validate budget constraints
     */
    public function validateBudget(array $playerIds, array $playerMarketValues, float $availableBudget): array
    {
        $totalCost = 0;
        foreach ($playerIds as $playerId) {
            $totalCost += $playerMarketValues[$playerId] ?? 0;
        }

        return [
            'valid' => $totalCost <= $availableBudget,
            'totalCost' => $totalCost,
            'availableBudget' => $availableBudget,
            'exceeds' => $totalCost > $availableBudget,
            'difference' => $totalCost - $availableBudget,
        ];
    }
}

<?php

namespace App\Services;

use App\Models\FantasyTransfer;
use App\Models\Gameweek;
use App\Models\League;
use Illuminate\Pagination\LengthAwarePaginator;
class LeagueNewsService
{

    /**
     * Get current gameweek for the league
     */
    private function getCurrentGameweek(League $league): ?Gameweek
    {
        return $league->season
            ->currentSeasonPhase
            ?->gameweeks()
            ->where('status', 'ongoing')
            ->orderBy('start_date', 'desc')
            ->first();
    }

     /**
     * Get paginated transfers for all gameweeks before the current gameweek
     */
    public function getPaginatedTransfers(League $league, ?int $gameweekId = null, int $perPage = 20, int $page = 1)
    {
        $currentGameweek = $this->getCurrentGameweek($league);
        if (! $currentGameweek) {
            return new LengthAwarePaginator(
                collect(),
                0,
                $perPage,
                $page,
                ['path' => request()->url(), 'query' => request()->query()]
            );
        }
        $fantasyTeamIds = $league->fantasyTeams()->pluck('fantasy_teams.id');
        // Get all gameweeks before the current gameweek
        $gameweekIds = Gameweek::where('season_phase_id', $currentGameweek->season_phase_id)
            ->where('id', '<', $currentGameweek->id)
            ->pluck('id');
            
        return FantasyTransfer::whereIn('fantasy_team_id', $fantasyTeamIds)
            ->whereIn('gameweek_id', $gameweekIds)
            ->where('tenant_id', $league->tenant_id)
            ->with(['playerIn', 'playerOut', 'fantasyTeam.user'])
            ->latest()
            ->paginate($perPage, ['*'], 'page', $page);
    }

}

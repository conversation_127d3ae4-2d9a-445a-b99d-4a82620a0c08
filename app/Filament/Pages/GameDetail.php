<?php

namespace App\Filament\Pages;

use App\Enums\LiveGameStatus;
use App\Models\Game;
use App\Models\Lineup;
use App\Models\LiveGame;
use App\Services\LiveGameService;
use BackedEnum;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;

class GameDetail extends Page
{
    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-play';

    protected static bool $shouldRegisterNavigation = false;

    public Game $game;

    public ?LiveGame $liveGame = null;

    public string $currentTab = 'overview';

    public function mount(): void
    {
        // Get game ID from request
        $gameId = request()->get('game');

        if (! $gameId) {
            abort(404, 'Game ID is required.');
        }

        $this->game = Game::with([
            'homeTeam.players',
            'awayTeam.players',
            'gameweek',
            'liveGame.lineups.players.player',
        ])->find($gameId);

        if (! $this->game) {
            abort(404, 'Game not found.');
        }

        // Validate that required relationships exist
        if (! $this->game->homeTeam || ! $this->game->awayTeam) {
            abort(404, 'Game teams not found. Please ensure both home and away teams are assigned.');
        }

        $this->liveGame = $this->game->liveGame;
    }

    public function getTitle(): string
    {
        return "Manage: {$this->game->game_display_name}";
    }

    public function getView(): string
    {
        return 'filament.pages.game-detail';
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back to Games')
                ->icon('heroicon-o-arrow-left')
                ->url('/admin/game-management')
                ->color('gray'),

            Action::make('setup_live_game')
                ->label('Setup Live Game')
                ->icon('heroicon-o-plus')
                ->color('primary')
                ->visible(fn () => ! $this->liveGame)
                ->action('setupLiveGame'),

            Action::make('go_live')
                ->label('🔴 GO LIVE')
                ->icon('heroicon-o-play')
                ->color('success')
                ->size('lg')
                ->visible(fn () => $this->canGoLive())
                ->requiresConfirmation()
                ->modalHeading('Start Live Match')
                ->modalDescription('Are you sure you want to start this match? This will begin live tracking.')
                ->action('goLive'),

            Action::make('end_match')
                ->label('End Match')
                ->icon('heroicon-o-stop')
                ->color('danger')
                ->visible(fn () => $this->liveGame?->status?->isActive())
                ->requiresConfirmation()
                ->action('endMatch'),
        ];
    }

    public function setupLiveGame(): void
    {
        try {
            DB::transaction(function () {
                // Create live game
                $this->liveGame = LiveGame::create([
                    'game_id' => $this->game->id,
                    'status' => LiveGameStatus::PRE_MATCH,
                    'current_minute' => 0,
                ]);

                // Create empty lineups for both teams
                Lineup::create([
                    'live_game_id' => $this->liveGame->id,
                    'team_id' => $this->game->home_team_id,
                    'formation' => '4-4-2',
                    'is_confirmed' => false,
                ]);

                Lineup::create([
                    'live_game_id' => $this->liveGame->id,
                    'team_id' => $this->game->away_team_id,
                    'formation' => '4-4-2',
                    'is_confirmed' => false,
                ]);

                $this->game->refresh();
                $this->liveGame = $this->game->liveGame;
            });

            Notification::make()
                ->title('Live Game Setup Complete')
                ->body('You can now set up team lineups.')
                ->success()
                ->send();

            $this->currentTab = 'lineups';

        } catch (\Exception $e) {
            Notification::make()
                ->title('Setup Failed')
                ->body('Failed to setup live game: '.$e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function goLive(): void
    {
        try {
            $liveGameService = app(LiveGameService::class);
            $liveGameService->startLiveGame($this->liveGame);

            $this->game->refresh();
            $this->liveGame = $this->game->liveGame;

            Notification::make()
                ->title('🔴 MATCH IS NOW LIVE!')
                ->body('Live tracking has started. You can now record events.')
                ->success()
                ->send();

            $this->currentTab = 'live';

        } catch (\Exception $e) {
            Notification::make()
                ->title('Failed to Start Match')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function endMatch(): void
    {
        try {
            $liveGameService = app(LiveGameService::class);
            $liveGameService->endLiveGame($this->liveGame);

            $this->game->refresh();
            $this->liveGame = $this->game->liveGame;

            Notification::make()
                ->title('Match Ended')
                ->body('Match has been completed and performance data synced.')
                ->success()
                ->send();

            $this->currentTab = 'summary';

        } catch (\Exception $e) {
            Notification::make()
                ->title('Failed to End Match')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function setTab(string $tab): void
    {
        $this->currentTab = $tab;
    }

    private function canGoLive(): bool
    {
        if (! $this->liveGame || $this->liveGame->status !== LiveGameStatus::PRE_MATCH) {
            return false;
        }

        // Check if both lineups are confirmed
        $confirmedLineups = $this->liveGame->lineups()
            ->where('is_confirmed', true)
            ->count();

        return $confirmedLineups >= 2;
    }

    public function getGameStatusBadge(): array
    {
        $status = $this->game->status;
        $color = match ($status?->value ?? $status) {
            'scheduled' => 'gray',
            'in_progress' => 'warning',
            'finished' => 'success',
            'postponed' => 'danger',
            'canceled' => 'danger',
            default => 'gray',
        };

        return ['status' => $status, 'color' => $color];
    }

    public function getLiveStatusBadge(): array
    {
        if (! $this->liveGame) {
            return ['status' => 'Not Live', 'color' => 'gray'];
        }

        $status = $this->liveGame->status;
        $color = match ($status?->value ?? $status) {
            'pre_match' => 'gray',
            'live' => 'success',
            'half_time' => 'warning',
            'second_half' => 'primary',
            'full_time' => 'info',
            'finished' => 'secondary',
            default => 'gray',
        };

        return ['status' => $status, 'color' => $color];
    }

    public function getLineupsStatus(): array
    {
        if (! $this->liveGame) {
            return ['text' => 'Not Set', 'color' => 'gray'];
        }

        $lineupCount = $this->liveGame->lineups()->count();
        $confirmedCount = $this->liveGame->lineups()->where('is_confirmed', true)->count();

        $text = "{$confirmedCount}/{$lineupCount} Ready";
        $color = 'gray';

        if ($lineupCount >= 2 && $confirmedCount >= 2) {
            $color = 'success';
        } elseif ($lineupCount >= 2) {
            $color = 'warning';
        } else {
            $color = 'danger';
        }

        return ['text' => $text, 'color' => $color];
    }

    public function getAvailableTabs(): array
    {
        $tabs = [
            'overview' => [
                'label' => 'Overview',
                'icon' => 'heroicon-o-information-circle',
                'available' => true,
            ],
        ];

        if ($this->liveGame) {
            $tabs['lineups'] = [
                'label' => 'Lineups',
                'icon' => 'heroicon-o-users',
                'available' => true,
            ];

            if ($this->liveGame->status?->isActive()) {
                $tabs['live'] = [
                    'label' => 'Live Control',
                    'icon' => 'heroicon-o-play',
                    'available' => true,
                ];
            }

            if ($this->liveGame->status?->isCompleted()) {
                $tabs['summary'] = [
                    'label' => 'Match Summary',
                    'icon' => 'heroicon-o-chart-bar',
                    'available' => true,
                ];
            }
        }

        return $tabs;
    }
}

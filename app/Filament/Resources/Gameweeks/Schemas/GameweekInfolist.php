<?php

namespace App\Filament\Resources\Gameweeks\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class GameweekInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('seasonPhase.name')
                    ->label('Season Phase')
                    ->badge()
                    ->color('primary'),
                TextEntry::make('name')
                    ->label('Gameweek')
                    ->weight('bold'),
                TextEntry::make('start_date')
                    ->label('Start Date')
                    ->dateTime(),
                TextEntry::make('end_date')
                    ->label('End Date')
                    ->dateTime(),
                TextEntry::make('status')
                    ->badge()
                    ->color(fn ($state) => match ($state?->value ?? $state) {
                        'upcoming' => 'gray',
                        'active' => 'success',
                        'finished' => 'warning',
                        default => 'gray',
                    }),
                TextEntry::make('rules')
                    ->label('Rules')
                    ->state(function ($record) {
                        if ($record->rules) {
                            $rules = is_string($record->rules) ? json_decode($record->rules, true) : $record->rules;

                            return 'Transfers: '.($rules['transfers_allowed'] ?? 'N/A').
                                   ', Captain: '.($rules['captain_multiplier'] ?? 'N/A').'x';
                        }

                        return 'No rules set';
                    }),
                TextEntry::make('created_at')
                    ->label('Created')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->label('Updated')
                    ->dateTime(),
            ]);
    }
}

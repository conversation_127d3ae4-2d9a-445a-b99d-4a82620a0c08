<?php

namespace App\Filament\Resources\Gameweeks\Schemas;

use App\Enums\GameweekStatus;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class GameweekForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Basic Information')
                    ->description('Configure the gameweek details and association')
                    ->columns()
                    ->schema([
                        Select::make('season_phase_id')
                            ->label('Season Phase')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->relationship(
                                'seasonPhase',
                                'name',
                                modifyQueryUsing: fn ($query) => $query->with('season.competition')
                            )
                            ->getOptionLabelFromRecordUsing(
                                fn ($record) => "{$record->season?->competition?->name} - {$record->season?->name} - {$record->name}"
                            )
                            ->helperText('Select the phase this gameweek belongs to')
                            ->columnSpanFull(),

                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., Gameweek 1')
                            ->columnSpanFull(),
                    ]),

                Section::make('Schedule')
                    ->description('Set the gameweek start and end dates')
                    ->columns(2)
                    ->schema([
                        DateTimePicker::make('start_date')
                            ->required()
                            ->native(false)
                            ->label('Start Date & Time')
                            ->columnSpan(1),
                        DateTimePicker::make('end_date')
                            ->required()
                            ->native(false)
                            ->label('End Date & Time')
                            ->after('start_date')
                            ->columnSpan(1),
                    ]),

                Section::make('Configuration')
                    ->description('Define gameweek rules and status')
                    ->columns(1)
                    ->schema([
                        Hidden::make('rules')
                            ->label('Rules (JSON)')
                            ->required()
                            ->default('{"transfers_allowed": 2, "captain_multiplier": 2, "vice_captain_multiplier": 1.5, "bench_boost": false, "triple_captain": false, "wildcard": false}')
                            ->helperText('Enter gameweek rules in JSON format. This field is required.')
                            ->rules(['required', 'json'])
                            ->validationMessages([
                                'json' => 'The rules field must be valid JSON.',
                            ])
                            ->columnSpan('full'),
                        Select::make('status')
                            ->required()
                            ->options(GameweekStatus::class)
                            ->default(GameweekStatus::UPCOMING)
                            ->columnSpan('full'),
                    ]),
            ]);
    }
}

<?php

namespace App\Filament\Resources\Gameweeks;

use App\Enums\FantasyIcon;
use App\Filament\Resources\Gameweeks\Pages\CreateGameweek;
use App\Filament\Resources\Gameweeks\Pages\EditGameweek;
use App\Filament\Resources\Gameweeks\Pages\ListGameweeks;
use App\Filament\Resources\Gameweeks\Pages\ViewGameweek;
use App\Filament\Resources\Gameweeks\RelationManagers\GamesRelationManager;
use App\Filament\Resources\Gameweeks\Schemas\GameweekForm;
use App\Filament\Resources\Gameweeks\Schemas\GameweekInfolist;
use App\Filament\Resources\Gameweeks\Tables\GameweeksTable;
use App\Models\Gameweek;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Table;
use UnitEnum;

class GameweekResource extends Resource
{
    protected static ?string $model = Gameweek::class;

    protected static string|BackedEnum|null $navigationIcon = FantasyIcon::Kickoff;

    protected static ?int $navigationSort = 2;

    protected static string|UnitEnum|null $navigationGroup = 'Competitions management';

    public static function form(Schema $schema): Schema
    {
        return GameweekForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return GameweekInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return GameweeksTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            GamesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListGameweeks::route('/'),
            'create' => CreateGameweek::route('/create'),
            'view' => ViewGameweek::route('/{record}'),
            'edit' => EditGameweek::route('/{record}/edit'),
        ];
    }
}

<?php

namespace App\Filament\Resources\Players\Tables;

use App\Data\Countries;
use App\Enums\PlayerPosition;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class PlayersTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('image')
                    ->label('Photo')
                    ->disk('public')
                    ->circular(),
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                TextColumn::make('teams.name')
                    ->label('Team')
                    ->badge()
                    ->searchable()
                    ->sortable(),

                ImageColumn::make('teams.logo')
                    ->label('Team Avatar')
                    ->circular()
                    ->width(50)
                    ->disk('public'),

                TextColumn::make('position')
                    ->badge()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('country')
                    ->searchable()
                    ->sortable()
                    ->icon('heroicon-m-flag'),
                TextColumn::make('birthday')
                    ->label('Age')
                    ->date()
                    ->sortable()
                    ->formatStateUsing(function ($state) {
                        if (! $state) {
                            return '-';
                        }

                        try {
                            $birthday = \Carbon\Carbon::parse($state);

                            // Check if birthday is in the future
                            if ($birthday->isFuture()) {
                                return 'Invalid date (future)';
                            }

                            // Calculate age correctly (birthday to now) - get whole years only
                            $age = (int) $birthday->diffInYears(now());

                            // Ensure age is reasonable (between 16 and 50)
                            if ($age < 16 || $age > 50) {
                                return "Invalid age ({$age})";
                            }

                            return "{$age} years";
                        } catch (\Exception) {
                            return 'Invalid date format';
                        }
                    }),
                TextColumn::make('market_value')
                    ->label('Market Value')
                    ->money('EUR', divideBy: 100)
                    ->placeholder('Not set')
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('position')
                    ->options(PlayerPosition::class),
                SelectFilter::make('country')
                    ->options(Countries::all())
                    ->searchable(),
                SelectFilter::make('teams')
                    ->relationship('teams', 'name')
                    ->preload()
                    ->searchable(),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}

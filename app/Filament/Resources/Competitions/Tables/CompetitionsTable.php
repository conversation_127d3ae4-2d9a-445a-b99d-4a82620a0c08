<?php

namespace App\Filament\Resources\Competitions\Tables;

use App\Enums\CompetitionStatus;
use App\Enums\CompetitionType;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class CompetitionsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('logo')
                    ->label('Logo')
                    ->disk('public')
                    ->circular()
                    ->width(40),
                TextColumn::make('name')
                    ->label('Competition Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('currentSeason.name')
                    ->label('Current Season')
                    ->searchable()
                    ->placeholder('No active season'),
                // TextColumn::make('seasons_count')
                //     ->label('Seasons')
                //     ->counts('seasons')
                //     ->badge()
                //     ->color('primary'),
                // TextColumn::make('tenants_count')
                //     ->label('Tenants')
                //     ->counts('tenants')
                //     ->badge()
                //     ->color('info'),
                TextColumn::make('slug')
                    ->label('Slug')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->options(CompetitionType::class),
                SelectFilter::make('status')
                    ->options(CompetitionStatus::class),
                SelectFilter::make('tenant')
                    ->label('Tenant')
                    ->options(fn () => \App\Models\Tenant::select('id', 'name')->pluck('name', 'id')->toArray())
                    ->query(function ($query, array $data) {
                        if ($data['value']) {
                            return $query->whereHas('tenants', function ($q) use ($data) {
                                $q->where('tenant_id', $data['value']);
                            });
                        }

                        return $query;
                    })
                    ->searchable(),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}

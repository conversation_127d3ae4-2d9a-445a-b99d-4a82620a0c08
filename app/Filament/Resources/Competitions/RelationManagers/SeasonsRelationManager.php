<?php

namespace App\Filament\Resources\Competitions\RelationManagers;

use App\Filament\Resources\Seasons\Schemas\SeasonForm;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;

class SeasonsRelationManager extends RelationManager
{
    protected static string $relationship = 'seasons';

    protected static ?string $recordTitleAttribute = 'name';

    public function form(Schema $schema): Schema
    {
        $schema = SeasonForm::configure($schema);

        // Find and hide the competition_id field from the schema
        $components = $schema->getComponents();
        foreach ($components as $component) {
            if ($component instanceof Section) {
                $children = $component->getChildComponents();
                foreach ($children as $child) {
                    if ($child instanceof \Filament\Forms\Components\Select && $child->getName() === 'competition_id') {
                        $child->hidden();
                    }
                }
            }
        }

        return $schema->components($components);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name')
                    ->label('Season Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                TextColumn::make('start_date')
                    ->label('Start Date')
                    ->date('M j, Y')
                    ->sortable(),
                TextColumn::make('end_date')
                    ->label('End Date')
                    ->date('M j, Y')
                    ->sortable(),
            ])
            ->filters([
                Filter::make('active')
                    ->label('Active Seasons')
                    ->query(fn ($query) => $query->where('start_date', '<=', now())->where('end_date', '>=', now())),
            ])
            ->headerActions([
                CreateAction::make()
                    ->mutateDataUsing(function (array $data): array {
                        $data['competition_id'] = $this->getOwnerRecord()->id;

                        return $data;
                    }),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ]);
    }
}

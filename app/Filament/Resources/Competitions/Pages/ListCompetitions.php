<?php

namespace App\Filament\Resources\Competitions\Pages;

use App\Enums\CompetitionType;
use App\Filament\Resources\Competitions\CompetitionResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Filament\Schemas\Components\Tabs\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListCompetitions extends ListRecords
{
    protected static string $resource = CompetitionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make()
                ->label('All Competitions'),
            'league' => Tab::make()
                ->label('League')
                ->icon('heroicon-m-trophy')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('type', CompetitionType::LEAGUE)),
            'cup' => Tab::make()
                ->label('Cup')
                ->icon('heroicon-m-gift')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('type', CompetitionType::CUP)),
            'mixed' => Tab::make()
                ->label('Mixed')
                ->icon('heroicon-m-squares-plus')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('type', CompetitionType::MIXED)),
        ];
    }
}

<?php

namespace App\Filament\Resources\Competitions\Schemas;

use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class CompetitionInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([

                Section::make('Competition Overview')
                    ->schema([
                        ImageEntry::make('logo')
                            ->label('Logo')
                            ->disk('public')
                            ->columnSpanFull(),

                        TextEntry::make('name')
                            ->label('Competition Name')
                            ->weight('bold')
                            ->size('xl'),

                        TextEntry::make('slug')
                            ->label('URL Slug'),

                        TextEntry::make('type')
                            ->badge()
                            ->label('Competition Type'),

                        TextEntry::make('status')
                            ->badge()
                            ->label('Status'),

                        TextEntry::make('currentSeason.name')
                            ->label('Current Season')
                            ->placeholder('No active season'),
                    ])
                    ->columns(2),

                Section::make('System Information')
                    ->description('Record timestamps and metadata')
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Created')
                            ->dateTime('M j, Y g:i A')
                            ->icon('heroicon-o-plus-circle'),
                        TextEntry::make('updated_at')
                            ->label('Last Updated')
                            ->dateTime('M j, Y g:i A')
                            ->since()
                            ->icon('heroicon-o-pencil-square'),
                    ])
                    ->columns(2)
                    ->collapsible(),

            ]);
    }
}

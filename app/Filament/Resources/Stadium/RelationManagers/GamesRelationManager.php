<?php

namespace App\Filament\Resources\Stadium\RelationManagers;

use Filament\Actions\CreateAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class GamesRelationManager extends RelationManager
{
    protected static string $relationship = 'games';

    protected static ?string $recordTitleAttribute = 'game_display_name';

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('game_display_name')
            ->columns([
                TextColumn::make('game_display_name')
                    ->label('Game')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('gameweek.name')
                    ->label('Gameweek')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('game_date')
                    ->label('Date')
                    ->dateTime('M j, Y g:i A')
                    ->sortable(),
                TextColumn::make('homeTeam.name')
                    ->label('Home Team')
                    ->searchable(),
                TextColumn::make('awayTeam.name')
                    ->label('Away Team')
                    ->searchable(),
                TextColumn::make('status')
                    ->badge()
                    ->searchable(),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->headerActions([
                CreateAction::make(),
            ]);
    }
}

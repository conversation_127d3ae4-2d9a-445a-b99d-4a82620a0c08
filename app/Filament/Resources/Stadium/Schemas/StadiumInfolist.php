<?php

namespace App\Filament\Resources\Stadium\Schemas;

use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Enums\FontWeight;

class StadiumInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make()
                    ->columns(2)
                    ->schema([
                        Section::make('Stadium Identity')
                            ->description('Basic stadium information and location')
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Stadium Name')
                                    ->weight(FontWeight::Bold)
                                    ->size('xl')
                                    ->badge()
                                    ->columnSpan(1),
                                TextEntry::make('location')
                                    ->label('Location')
                                    ->placeholder('Not specified')
                                    ->icon('heroicon-o-map-pin')
                                    ->columnSpan(1),
                                TextEntry::make('city')
                                    ->label('City')
                                    ->placeholder('Not specified')
                                    ->icon('heroicon-o-building-office')
                                    ->columnSpan(1),
                                TextEntry::make('country')
                                    ->label('Country')
                                    ->placeholder('Not specified')
                                    ->icon('heroicon-o-globe-alt')
                                    ->columnSpan(1),
                            ])
                            ->columnSpan(1)
                            ->columns(2)
                            ->extraAttributes([
                                'class' => 'bg-gradient-to-br from-white to-gray-50 border-2 border-gray-200 rounded-xl shadow-lg p-6',
                            ]),
                        Section::make('Stadium Details')
                            ->description('Physical characteristics and history')
                            ->schema([
                                TextEntry::make('capacity')
                                    ->label('Seating Capacity')
                                    ->formatStateUsing(fn ($state) => number_format($state))
                                    ->icon('heroicon-o-user-group')
                                    ->badge()
                                    ->color('primary')
                                    ->columnSpan(2),
                                TextEntry::make('year_opened')
                                    ->label('Year Opened')
                                    ->placeholder('Unknown')
                                    ->icon('heroicon-o-calendar-days')
                                    ->columnSpan(2),
                            ])
                            ->columnSpan(1)
                            ->columns(2),
                    ])->columnSpanFull(),
                Section::make('Visuals')
                    ->description('Stadium image')
                    ->schema([
                        ImageEntry::make('image_url')
                            ->label('Stadium Image')
                            ->disk('public')
                            ->placeholder('No image available')
                            ->imageHeight(400)
                            ->imageWidth(1150)
                            ->columnSpan('full')
                            ->extraImgAttributes(['class' => 'rounded-xl']),

                    ])
                    ->columnSpanFull()
                    ->collapsible(),

                Section::make('Teams and Games')
                    ->description('Teams that play at this stadium and games scheduled')
                    ->schema([
                        RepeatableEntry::make('homeTeams')
                            ->label('Home Teams')
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Team')
                                    ->icon('heroicon-o-users'),
                            ])
                            ->columns(1)
                            ->hidden(fn ($state): bool => blank($state)),
                        TextEntry::make('no_home_teams')
                            ->label('Home Teams')
                            ->placeholder('No home teams assigned')
                            ->icon('heroicon-o-users')
                            ->hidden(fn (callable $get): bool => filled($get('homeTeams'))),
                        TextEntry::make('games_count')
                            ->label('Total Games ')
                            ->state(function ($record) {
                                return $record->games()->count();
                            })
                            ->icon('heroicon-o-calendar'),
                    ])
                    ->columns(2)
                    ->collapsible(),

                Section::make('System Information')
                    ->description('Record timestamps and metadata')
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Created')
                            ->dateTime('M j, Y g:i A')
                            ->icon('heroicon-o-plus-circle'),
                        TextEntry::make('updated_at')
                            ->label('Last Updated')
                            ->dateTime('M j, Y g:i A')
                            ->since()
                            ->icon('heroicon-o-pencil-square'),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }
}

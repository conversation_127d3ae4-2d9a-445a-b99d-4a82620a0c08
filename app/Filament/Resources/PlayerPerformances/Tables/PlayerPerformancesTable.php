<?php

namespace App\Filament\Resources\PlayerPerformances\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class PlayerPerformancesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('game_id')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('player_id')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('minutes_played')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('goals_scored')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('assists')
                    ->numeric()
                    ->sortable(),
                IconColumn::make('clean_sheet')
                    ->boolean(),
                TextColumn::make('goals_conceded')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('own_goals')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('penalities_saved')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('penalities_missed')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('penalties_caused')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('penalties_committed')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('saves')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('yellow_cards')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('red_cards')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('team_id')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}

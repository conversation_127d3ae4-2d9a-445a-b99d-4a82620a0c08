<?php

namespace App\Filament\Resources\PlayerPerformances;

use App\Enums\FantasyIcon;
use App\Filament\Resources\PlayerPerformances\Pages\CreatePlayerPerformance;
use App\Filament\Resources\PlayerPerformances\Pages\EditPlayerPerformance;
use App\Filament\Resources\PlayerPerformances\Pages\ListPlayerPerformances;
use App\Filament\Resources\PlayerPerformances\Pages\ViewPlayerPerformance;
use App\Filament\Resources\PlayerPerformances\Schemas\PlayerPerformanceForm;
use App\Filament\Resources\PlayerPerformances\Schemas\PlayerPerformanceInfolist;
use App\Filament\Resources\PlayerPerformances\Tables\PlayerPerformancesTable;
use App\Models\PlayerPerformance;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Table;
use UnitEnum;

class PlayerPerformanceResource extends Resource
{
    protected static ?string $model = PlayerPerformance::class;

    protected static string|BackedEnum|null $navigationIcon = FantasyIcon::Ball;

    protected static ?int $navigationSort = 4;

    protected static string|UnitEnum|null $navigationGroup = 'Competitions management';

    protected static ?string $navigationLabel = 'Player Performances';

    protected static ?string $modelLabel = 'Player Performance';

    protected static ?string $pluralModelLabel = 'Player Performances';

    protected static ?string $recordTitleAttribute = 'player.name';

    public static function form(Schema $schema): Schema
    {
        return PlayerPerformanceForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return PlayerPerformanceInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return PlayerPerformancesTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListPlayerPerformances::route('/'),
            'create' => CreatePlayerPerformance::route('/create'),
            'view' => ViewPlayerPerformance::route('/{record}'),
            'edit' => EditPlayerPerformance::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}

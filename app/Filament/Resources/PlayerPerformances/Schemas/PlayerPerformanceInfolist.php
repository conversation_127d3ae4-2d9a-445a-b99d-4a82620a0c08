<?php

namespace App\Filament\Resources\PlayerPerformances\Schemas;

use App\Services\FantasyPointsCalculationService;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class PlayerPerformanceInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Game Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('game')
                                    ->label('Match')
                                    ->formatStateUsing(fn ($record) => 
                                        "{$record->game->homeTeam->name} vs {$record->game->awayTeam->name}"
                                    ),

                                TextEntry::make('game.gameweek.name')
                                    ->label('Gameweek'),

                                TextEntry::make('game.game_date')
                                    ->label('Date & Time')
                                    ->dateTime(),

                                TextEntry::make('game.status')
                                    ->label('Game Status')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'scheduled' => 'gray',
                                        'in_progress' => 'warning',
                                        'finished' => 'success',
                                        'postponed' => 'danger',
                                        default => 'gray',
                                    }),
                            ]),
                    ]),

                Section::make('Player Information')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('player.name')
                                    ->label('Player Name'),

                                TextEntry::make('player.position')
                                    ->label('Position')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'GK' => 'warning',
                                        'DEF' => 'success',
                                        'MID' => 'info',
                                        'FWD' => 'danger',
                                        default => 'gray',
                                    }),

                                TextEntry::make('team.name')
                                    ->label('Team Played For')
                                    ->description('Team the player represented in this game'),
                            ]),
                    ]),

                Section::make('Performance Statistics')
                    ->schema([
                        Grid::make(4)
                            ->schema([
                                TextEntry::make('minutes_played')
                                    ->label('Minutes Played')
                                    ->suffix(' min')
                                    ->color(fn ($state) => match (true) {
                                        $state >= 60 => 'success',
                                        $state >= 30 => 'warning',
                                        $state > 0 => 'gray',
                                        default => 'danger',
                                    }),

                                TextEntry::make('goals_scored')
                                    ->label('Goals')
                                    ->color(fn ($state) => $state > 0 ? 'success' : 'gray'),

                                TextEntry::make('assists')
                                    ->label('Assists')
                                    ->color(fn ($state) => $state > 0 ? 'info' : 'gray'),

                                IconEntry::make('clean_sheet')
                                    ->label('Clean Sheet')
                                    ->boolean(),
                            ]),

                        Grid::make(4)
                            ->schema([
                                TextEntry::make('yellow_cards')
                                    ->label('Yellow Cards')
                                    ->color(fn ($state) => $state > 0 ? 'warning' : 'gray'),

                                TextEntry::make('red_cards')
                                    ->label('Red Cards')
                                    ->color(fn ($state) => $state > 0 ? 'danger' : 'gray'),

                                TextEntry::make('goals_conceded')
                                    ->label('Goals Conceded')
                                    ->color(fn ($state) => $state > 0 ? 'danger' : 'gray'),

                                TextEntry::make('saves')
                                    ->label('Saves')
                                    ->color(fn ($state) => $state > 0 ? 'info' : 'gray'),
                            ]),

                        Grid::make(4)
                            ->schema([
                                TextEntry::make('penalities_saved')
                                    ->label('Penalties Saved')
                                    ->color(fn ($state) => $state > 0 ? 'success' : 'gray'),

                                TextEntry::make('penalities_missed')
                                    ->label('Penalties Missed')
                                    ->color(fn ($state) => $state > 0 ? 'danger' : 'gray'),

                                TextEntry::make('penalties_caused')
                                    ->label('Penalties Earned')
                                    ->color(fn ($state) => $state > 0 ? 'info' : 'gray'),

                                TextEntry::make('penalties_committed')
                                    ->label('Penalties Committed')
                                    ->color(fn ($state) => $state > 0 ? 'danger' : 'gray'),
                            ]),

                        Grid::make(1)
                            ->schema([
                                TextEntry::make('own_goals')
                                    ->label('Own Goals')
                                    ->color(fn ($state) => $state > 0 ? 'danger' : 'gray'),
                            ]),
                    ]),

                Section::make('Fantasy Points Calculation')
                    ->schema([
                        TextEntry::make('fantasy_points')
                            ->label('Total Fantasy Points')
                            ->getStateUsing(function ($record) {
                                $service = app(FantasyPointsCalculationService::class);
                                $points = $service->calculatePlayerPoints($record);
                                return $points['total'];
                            })
                            ->size('lg')
                            ->weight('bold')
                            ->color(fn ($state) => match (true) {
                                $state > 10 => 'success',
                                $state > 5 => 'info',
                                $state > 0 => 'warning',
                                $state < 0 => 'danger',
                                default => 'gray',
                            }),

                        TextEntry::make('points_breakdown')
                            ->label('Points Breakdown')
                            ->getStateUsing(function ($record) {
                                $service = app(FantasyPointsCalculationService::class);
                                $points = $service->calculatePlayerPoints($record);
                                
                                $breakdown = [];
                                foreach ($points['breakdown'] as $item) {
                                    $total = $item['points'] * $item['count'];
                                    $sign = $total > 0 ? '+' : '';
                                    $breakdown[] = "{$item['action']}: {$sign}{$total} pts";
                                }
                                
                                return implode(' | ', $breakdown);
                            })
                            ->columnSpanFull(),
                    ]),

                Section::make('Record Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('created_at')
                                    ->label('Created At')
                                    ->dateTime(),

                                TextEntry::make('updated_at')
                                    ->label('Updated At')
                                    ->dateTime(),
                            ]),
                    ])
                    ->collapsible(),
            ]);
    }
}

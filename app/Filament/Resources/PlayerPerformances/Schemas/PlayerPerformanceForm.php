<?php

namespace App\Filament\Resources\PlayerPerformances\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Schema;

class PlayerPerformanceForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('game_id')
                    ->required()
                    ->numeric(),
                TextInput::make('player_id')
                    ->required()
                    ->numeric(),
                TextInput::make('minutes_played')
                    ->required()
                    ->numeric(),
                TextInput::make('goals_scored')
                    ->required()
                    ->numeric(),
                TextInput::make('assists')
                    ->required()
                    ->numeric(),
                Toggle::make('clean_sheet')
                    ->required(),
                TextInput::make('goals_conceded')
                    ->required()
                    ->numeric(),
                TextInput::make('own_goals')
                    ->required()
                    ->numeric(),
                TextInput::make('penalities_saved')
                    ->required()
                    ->numeric(),
                TextInput::make('penalities_missed')
                    ->required()
                    ->numeric(),
                TextInput::make('penalties_caused')
                    ->required()
                    ->numeric(),
                TextInput::make('penalties_committed')
                    ->required()
                    ->numeric(),
                TextInput::make('saves')
                    ->required()
                    ->numeric(),
                TextInput::make('yellow_cards')
                    ->required()
                    ->numeric(),
                TextInput::make('red_cards')
                    ->required()
                    ->numeric(),
                TextInput::make('team_id')
                    ->required()
                    ->numeric(),
            ]);
    }
}

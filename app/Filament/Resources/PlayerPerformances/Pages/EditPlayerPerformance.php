<?php

namespace App\Filament\Resources\PlayerPerformances\Pages;

use App\Filament\Resources\PlayerPerformances\PlayerPerformanceResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditPlayerPerformance extends EditRecord
{
    protected static string $resource = PlayerPerformanceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}

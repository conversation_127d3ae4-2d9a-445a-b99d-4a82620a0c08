<?php

namespace App\Filament\Resources\PlayerPerformances\Pages;

use App\Filament\Resources\PlayerPerformances\PlayerPerformanceResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListPlayerPerformances extends ListRecords
{
    protected static string $resource = PlayerPerformanceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}

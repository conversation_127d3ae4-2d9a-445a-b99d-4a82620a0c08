<?php

namespace App\Filament\Resources\PlayerPerformances\Pages;

use App\Filament\Resources\PlayerPerformances\PlayerPerformanceResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewPlayerPerformance extends ViewRecord
{
    protected static string $resource = PlayerPerformanceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}

<?php

namespace App\Filament\Resources\Teams\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\ColorColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class TeamsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('logo')
                    ->label('Logo')
                    ->circular()
                    ->width(50)
                    ->disk('public'),
                TextColumn::make('name')
                    ->label('Team Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                TextColumn::make('short_name')
                    ->label('Short Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('code_name')
                    ->label('Code')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary'),
                ColorColumn::make('shirt')
                    ->label('Home Kit')
                    ->tooltip('Home kit color'),
                ColorColumn::make('gk_shirt')
                    ->label('GK Kit')
                    ->tooltip('Goalkeeper kit color'),
                TextColumn::make('players_count')
                    ->label('Players')
                    ->counts('players')
                    ->badge()
                    ->color('info'),
                TextColumn::make('seasons_count')
                    ->label('Seasons')
                    ->counts('seasons')
                    ->badge()
                    ->color('warning'),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([

            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}

<?php

namespace App\Filament\Resources\Teams\Schemas;

use App\Infolists\Components\ShirtDisplay;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Enums\FontWeight;

class TeamInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Team Identity')
                    ->description('Basic team information and identification')
                    ->schema([
                        ImageEntry::make('logo')
                            ->label('')
                            ->disk('public')
                            ->hiddenLabel()
                            ->extraAttributes([
                                'class' => 'rounded-full border-4 border-primary-500 shadow-md w-24 h-24 mx-auto',
                            ])
                            ->columnSpanFull(),

                        TextEntry::make('name')
                            ->label('Team Name')
                            ->hiddenLabel()
                            ->weight(FontWeight::Bold)
                            ->size('2xl')
                            ->color('gray-900')
                            ->extraAttributes([
                                'class' => 'text-center mt-4',
                            ])
                            ->columnSpanFull(),

                        TextEntry::make('name_ar')
                            ->label('Team Name (Arabic)')
                            ->weight(FontWeight::Medium)
                            ->placeholder('Not specified')
                            ->icon('heroicon-m-language'),

                        TextEntry::make('short_name')
                            ->label('Short Name')
                            ->weight(FontWeight::Medium)
                            ->placeholder('N/A')
                            ->icon('heroicon-m-tag'),

                        TextEntry::make('code_name')
                            ->label('Team Code')
                            ->badge()
                            ->color('success')
                            ->weight(FontWeight::Bold)
                            ->icon('heroicon-m-finger-print'),
                    ])
                    ->columns([
                        'sm' => 1,
                        'md' => 3,
                    ])
                    ->columnSpan('full')
                    ->extraAttributes([
                        'class' => 'bg-gradient-to-br from-white to-blue-50 border border-blue-200 rounded-2xl shadow-xl p-6',
                    ]),

                Section::make('Team Kits')
                    ->description('Team uniform designs with logo and colors')
                    ->columns(2)
                    ->schema([
                        ShirtDisplay::make('shirt')
                            ->label('Home ')
                            ->logoField('logo'),
                        ShirtDisplay::make('gk_shirt')
                            ->label('Goalkeeper Kit')
                            ->logoField('logo'),
                    ])
                    ->columnSpanFull(),

                Section::make('Club Information')
                    ->description('Additional team details and history')
                    ->schema([
                        TextEntry::make('president')
                            ->label('Club President')
                            ->placeholder('Not specified')
                            ->icon('heroicon-o-user-circle'),
                        TextEntry::make('founded_at')
                            ->label('Founded')
                            ->date('Y')
                            ->placeholder('Unknown')
                            ->icon('heroicon-o-calendar-days'),
                        TextEntry::make('homeStadium')
                            ->label('Home Stadium')
                            ->placeholder('Not specified')
                            ->icon('heroicon-o-map-pin')
                            ->state(function ($record) {
                                $homeStadium = $record->homeStadium()->first();

                                return $homeStadium ? $homeStadium->name : 'Not specified';
                            }),
                        TextEntry::make('otherStadiumsCount')
                            ->label('Other Stadiums')
                            ->state(function ($record) {
                                return $record->stadiums()->wherePivot('is_home', false)->count();
                            })
                            ->placeholder('0')
                            ->icon('heroicon-o-building-library'),
                    ])
                    ->columns(2),

                Section::make('System Information')
                    ->description('Record timestamps and metadata')
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Created')
                            ->dateTime('M j, Y g:i A')
                            ->icon('heroicon-o-plus-circle'),
                        TextEntry::make('updated_at')
                            ->label('Last Updated')
                            ->dateTime('M j, Y g:i A')
                            ->since()
                            ->icon('heroicon-o-pencil-square'),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }
}

<?php

namespace App\Filament\Resources\Teams\RelationManagers;

use App\Enums\PlayerPosition;
use App\Filament\Resources\Players\Schemas\PlayerForm;
use App\Filament\Resources\Players\Schemas\PlayerInfolist;
use Filament\Actions\AttachAction;
use Filament\Actions\CreateAction;
use Filament\Actions\DetachAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class PlayersRelationManager extends RelationManager
{
    protected static string $relationship = 'players';

    protected static ?string $recordTitleAttribute = 'name';

    public function form(Schema $schema): Schema
    {
        return PlayerForm::configure($schema);
    }

    public function infolist(Schema $schema): Schema
    {
        return PlayerInfolist::configure($schema);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                ImageColumn::make('image')
                    ->label('Photo')
                    ->circular()
                    ->disk('public')
                    ->width(40),
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                TextColumn::make('position')
                    ->badge()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('country')
                    ->searchable()
                    ->sortable()
                    ->icon('heroicon-m-flag'),
                TextColumn::make('birthday')
                    ->label('Age')
                    ->formatStateUsing(function ($state) {
                        if (! $state) {
                            return '-';
                        }
                        try {
                            $birthday = \Carbon\Carbon::parse($state);
                            if ($birthday->isFuture()) {
                                return 'Invalid date';
                            }
                            $age = (int) $birthday->diffInYears(now());
                            if ($age < 16 || $age > 50) {
                                return "Invalid age ({$age})";
                            }

                            return "{$age} years";
                        } catch (\Exception) {
                            return 'Invalid date';
                        }
                    }),
                TextColumn::make('market_value')
                    ->label('Market Value')
                    ->money('EUR', divideBy: 100)
                    ->placeholder('Not set')
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('position')
                    ->options(PlayerPosition::class),
                Filter::make('young_players')
                    ->label('Under 25')
                    ->query(fn ($query) => $query->where('birthday', '>', now()->subYears(25))),
            ])
            ->recordActions([
                ViewAction::make()
                    ->label('View Player'),
                EditAction::make()
                    ->label('Edit Player')
                    ->authorize(true), DetachAction::make(),
            ])
            ->headerActions([
                CreateAction::make(),
                AttachAction::make()
                    ->preloadRecordSelect()
                    ->recordSelectSearchColumns(['name', 'country']),

            ]);
    }
}

<?php

namespace App\Filament\Resources\Teams\Pages;

use App\Filament\Resources\Teams\TeamResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;

class EditTeam extends EditRecord
{
    protected static string $resource = TeamResource::class;

    protected ?int $homeStadiumId = null;

    protected array $otherStadiums = [];

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Store stadium data separately to handle after save
        $this->homeStadiumId = $data['home_stadium_id'] ?? null;
        $this->otherStadiums = $data['other_stadiums'] ?? [];

        // Remove stadium fields from data to prevent direct assignment
        unset($data['home_stadium_id'], $data['other_stadiums']);

        return $data;
    }

    protected function afterSave(): void
    {
        // Use the Team model's syncStadiums method to properly handle relationships
        // This method will prevent duplicates and handle the pivot table correctly
        $this->record->syncStadiums($this->homeStadiumId, $this->otherStadiums);
    }
}

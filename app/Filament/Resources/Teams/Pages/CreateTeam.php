<?php

namespace App\Filament\Resources\Teams\Pages;

use App\Filament\Resources\Teams\TeamResource;
use Filament\Resources\Pages\CreateRecord;

class CreateTeam extends CreateRecord
{
    protected static string $resource = TeamResource::class;

    protected ?int $homeStadiumId = null;

    protected array $otherStadiums = [];

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Store stadium data separately to handle after create
        $this->homeStadiumId = $data['home_stadium_id'] ?? null;
        $this->otherStadiums = $data['other_stadiums'] ?? [];

        // Remove stadium fields from data to prevent direct assignment
        unset($data['home_stadium_id'], $data['other_stadiums']);

        return $data;
    }

    protected function afterCreate(): void
    {
        // Use the unified syncStadiums method with indexed array format
        $this->record->syncStadiums($this->homeStadiumId, $this->otherStadiums);
    }
}

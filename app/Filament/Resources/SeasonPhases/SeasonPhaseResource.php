<?php

namespace App\Filament\Resources\SeasonPhases;

use App\Filament\Clusters\Seasons\SeasonsCluster;
use App\Filament\Resources\SeasonPhases\Pages\CreateSeasonPhase;
use App\Filament\Resources\SeasonPhases\Pages\EditSeasonPhase;
use App\Filament\Resources\SeasonPhases\Pages\ListSeasonPhases;
use App\Filament\Resources\SeasonPhases\Pages\ViewSeasonPhase;
use App\Filament\Resources\SeasonPhases\Schemas\SeasonPhaseForm;
use App\Filament\Resources\SeasonPhases\Schemas\SeasonPhaseInfolist;
use App\Filament\Resources\SeasonPhases\Tables\SeasonPhasesTable;
use App\Models\SeasonPhase;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class SeasonPhaseResource extends Resource
{
    protected static ?string $model = SeasonPhase::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?int $navigationSort = 6;

    protected static ?string $cluster = SeasonsCluster::class;

    public static function form(Schema $schema): Schema
    {
        return SeasonPhaseForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return SeasonPhaseInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return SeasonPhasesTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSeasonPhases::route('/'),
            'create' => CreateSeasonPhase::route('/create'),
            'view' => ViewSeasonPhase::route('/{record}'),
            'edit' => EditSeasonPhase::route('/{record}/edit'),
        ];
    }
}

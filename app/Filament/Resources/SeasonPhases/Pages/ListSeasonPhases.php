<?php

namespace App\Filament\Resources\SeasonPhases\Pages;

use App\Filament\Resources\SeasonPhases\SeasonPhaseResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListSeasonPhases extends ListRecords
{
    protected static string $resource = SeasonPhaseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}

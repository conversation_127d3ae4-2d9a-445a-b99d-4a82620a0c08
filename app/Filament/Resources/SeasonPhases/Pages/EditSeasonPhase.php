<?php

namespace App\Filament\Resources\SeasonPhases\Pages;

use App\Filament\Resources\SeasonPhases\SeasonPhaseResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;

class EditSeasonPhase extends EditRecord
{
    protected static string $resource = SeasonPhaseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}

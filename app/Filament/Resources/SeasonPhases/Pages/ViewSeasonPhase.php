<?php

namespace App\Filament\Resources\SeasonPhases\Pages;

use App\Filament\Resources\SeasonPhases\SeasonPhaseResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewSeasonPhase extends ViewRecord
{
    protected static string $resource = SeasonPhaseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }
}

<?php

namespace App\Filament\Resources\SeasonPhases\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class SeasonPhaseInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema->components([
            Section::make('General Information')
                ->columns(2)
                ->schema([
                    TextEntry::make('name')
                        ->label('Phase Name')
                        ->columnSpan(2)
                        ->weight('medium'),

                    TextEntry::make('format')
                        ->badge()
                        ->label('Format'),

                    TextEntry::make('teams_count')
                        ->numeric()
                        ->label('Teams'),

                    TextEntry::make('status')
                        ->badge()
                        ->label('Status'),
                ]),

            Section::make('Related To')
                ->columns(2)
                ->schema([
                    TextEntry::make('season.competition.name')
                        ->label('Competition'),

                    TextEntry::make('season.name')
                        ->label('Season'),
                ]),

            Section::make('Metadata')
                ->columns(2)
                ->collapsed()
                ->collapsible()
                ->schema([
                    TextEntry::make('created_at')
                        ->label('Created At')
                        ->dateTime(),

                    TextEntry::make('updated_at')
                        ->label('Updated At')
                        ->dateTime(),
                ]),
        ]);
    }
}

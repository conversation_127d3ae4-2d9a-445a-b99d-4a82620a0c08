<?php

namespace App\Filament\Resources\SeasonPhases\Schemas;

use App\Enums\SeasonPhase\GamePhaseStatus;
use App\Enums\SeasonPhase\SeasonPhaseFormat;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class SeasonPhaseForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Basic Information')
                    ->description('Configure the season phase details and association')
                    ->columns(2)
                    ->schema([
                        Select::make('season_id')
                            ->label('Season')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->relationship(
                                'season',
                                'name',
                                modifyQueryUsing: fn ($query) => $query->with('competition')
                            )
                            ->getOptionLabelFromRecordUsing(
                                fn ($record) => "{$record->competition?->name} - {$record->name}"
                            )
                            ->helperText('Select the season this phase belongs to')
                            ->columnSpan(1),

                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., Regular Season, Playoffs')
                            ->helperText('Enter a descriptive name for the phase')
                            ->columnSpan(1),
                    ]),

                Section::make('Phase Configuration')
                    ->description('Configure the format and rules for this phase')
                    ->columns(3)
                    ->schema([
                        Select::make('format')
                            ->required()
                            ->native(false)
                            ->options(SeasonPhaseFormat::class)
                            ->helperText('Select the competition format')
                            ->columnSpan(1),
                        TextInput::make('teams_count')
                            ->required()
                            ->numeric()
                            ->minValue(2)
                            ->maxValue(100)
                            ->helperText('Number of teams participating')
                            ->columnSpan(1),
                        Select::make('status')
                            ->required()
                            ->native(false)
                            ->options(GamePhaseStatus::class)
                            ->helperText('Current status of the phase')
                            ->columnSpan(1),
                    ]),

                Hidden::make('config')
                    ->label('Configuration (JSON)')
                    ->helperText('Enter JSON configuration for phase rules (e.g., {"rounds": 30, "points_win": 3})')
                    ->default('{"rounds": 30, "home_away": true, "points_win": 3, "points_draw": 1, "points_loss": 0}'),

            ]);
    }
}

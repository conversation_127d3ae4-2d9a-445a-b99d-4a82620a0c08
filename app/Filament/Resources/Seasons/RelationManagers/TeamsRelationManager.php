<?php

namespace App\Filament\Resources\Seasons\RelationManagers;

use App\Filament\Resources\Teams\Schemas\TeamForm;
use Filament\Actions\AttachAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables;
use Filament\Tables\Table;

class TeamsRelationManager extends RelationManager
{
    protected static string $relationship = 'teams';

    protected static ?string $recordTitleAttribute = 'name';

    public function form(Schema $schema): Schema
    {
        return TeamForm::configure($schema);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\ImageColumn::make('logo')
                    ->label('Logo')
                    ->circular()
                    ->width(40),
                Tables\Columns\TextColumn::make('name')
                    ->label('Team Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                Tables\Columns\TextColumn::make('code_name')
                    ->label('Code')
                    ->badge()
                    ->color('primary'),
                Tables\Columns\ColorColumn::make('shirt')
                    ->label('Kit')
                    ->tooltip('Home kit color'),
                Tables\Columns\TextColumn::make('players_count')
                    ->label('Players')
                    ->counts('players')
                    ->badge()
                    ->color('info'),
                Tables\Columns\TextColumn::make('founded_at')
                    ->label('Founded')
                    ->date('Y')
                    ->sortable()
                    ->placeholder('Unknown'),
            ])
            ->filters([
                Tables\Filters\Filter::make('has_players')
                    ->label('Has Players')
                    ->query(fn ($query) => $query->has('players')),
                Tables\Filters\Filter::make('founded_recently')
                    ->label('Founded After 2000')
                    ->query(fn ($query) => $query->where('founded_at', '>', '2000-01-01')),
            ])
            ->headerActions([
                AttachAction::make()
                    ->preloadRecordSelect()
                    ->recordSelectSearchColumns(['name', 'short_name', 'code_name']),
            ]);

    }
}

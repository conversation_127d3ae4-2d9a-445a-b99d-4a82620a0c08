<?php

namespace App\Filament\Resources\Seasons\RelationManagers;

use App\Enums\GameweekStatus;
use App\Filament\Resources\Games\Schemas\GameForm;
use Filament\Actions\CreateAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables;
use Filament\Tables\Table;

class GameweeksRelationManager extends RelationManager
{
    protected static string $relationship = 'gameweeks';

    protected static ?string $recordTitleAttribute = 'name';

    // public function form(Schema $schema): Schema
    // {
    //     return GameForm::configure($schema);
    // }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                Tables\Columns\TextColumn::make('seasonPhase.name')
                    ->label('Phase')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_date')
                    ->label('Start')
                    ->dateTime('M j, H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_date')
                    ->label('End')
                    ->dateTime('M j, H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(GameweekStatus::class),
                Tables\Filters\Filter::make('current_week')
                    ->label('Current Week')
                    ->query(fn ($query) => $query->where('start_date', '<=', now())
                        ->where('end_date', '>=', now())),
            ])
            ->headerActions([
                // CreateAction::make(),
            ]);
    }
}

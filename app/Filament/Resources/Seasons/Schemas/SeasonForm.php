<?php

namespace App\Filament\Resources\Seasons\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class SeasonForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Basic Information')
                    ->description('Configure the season details and association')
                    ->columns(2)
                    ->schema([
                        Select::make('competition_id')
                            ->label('Competition')
                            ->required()
                            ->relationship('competition', 'name')
                            ->searchable()
                            ->preload()
                            ->helperText('Select the competition this season belongs to')
                            ->columnSpan(1),
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., 2024-25 Season')
                            ->helperText('Enter a descriptive name for the season')
                            ->columnSpan(1),
                    ]),

                Section::make('Schedule')
                    ->description('Set the season start and end dates')
                    ->columns(2)
                    ->schema([
                        DatePicker::make('start_date')
                            ->label('Start Date')
                            ->required()
                            ->native(false)
                            ->helperText('When does this season begin?')
                            ->columnSpan(1),
                        DatePicker::make('end_date')
                            ->label('End Date')
                            ->native(false)
                            ->required()
                            ->after('start_date')
                            ->helperText('When does this season end?')
                            ->columnSpan(1),
                    ]),
            ]);
    }
}

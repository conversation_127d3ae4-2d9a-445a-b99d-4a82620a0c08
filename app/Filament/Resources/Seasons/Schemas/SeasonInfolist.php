<?php

namespace App\Filament\Resources\Seasons\Schemas;

use Carbon\Carbon;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class SeasonInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                // Header Section with Competition Info
                Section::make('Competition Overview')
                    ->description('Basic season and competition information')
                    ->icon('heroicon-m-trophy')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('competition.name')
                                    ->label('Competition')
                                    ->badge()
                                    ->color(fn ($record) => $record->competition?->type?->getColor() ?? 'primary')
                                    ->icon(fn ($record) => $record->competition?->type?->getIcon() ?? 'heroicon-m-trophy')
                                    ->size('lg')
                                    ->weight('bold'),

                                TextEntry::make('competition.type')
                                    ->label('Competition Type')
                                    ->badge()
                                    ->color(fn ($state) => $state?->getColor() ?? 'gray')
                                    ->icon(fn ($state) => $state?->getIcon() ?? 'heroicon-m-question-mark-circle'),
                            ]),

                        ImageEntry::make('competition.logo')
                            ->label('Competition Logo')
                            ->disk('public')
                            ->circular()
                            ->grow(false),

                        Grid::make(2)
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Season Name')
                                    ->size('lg')
                                    ->weight('bold')
                                    ->icon('heroicon-m-calendar-days'),

                                TextEntry::make('competition.status')
                                    ->label('Competition Status')
                                    ->badge()
                                    ->color(fn ($state) => $state?->getColor() ?? 'gray')
                                    ->icon(fn ($state) => $state?->getIcon() ?? 'heroicon-m-question-mark-circle'),

                                // IconEntry::make('is_current')
                                //     ->label('Current Season')
                                //     ->boolean()
                                //     ->state(fn ($record) => $record->competitions()->exists())
                                //     ->trueIcon('heroicon-m-star')
                                //     ->falseIcon('heroicon-m-minus')
                                //     ->trueColor('warning')
                                //     ->falseColor('gray'),
                            ]),
                    ]),

                // Season Timeline
                Section::make('Season Timeline')
                    ->description('Important dates and duration information')
                    ->icon('heroicon-m-clock')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('start_date')
                                    ->label('Start Date')
                                    ->date('M j, Y')
                                    ->icon('heroicon-m-play')
                                    ->color('success'),

                                TextEntry::make('end_date')
                                    ->label('End Date')
                                    ->date('M j, Y')
                                    ->icon('heroicon-m-stop')
                                    ->color('danger'),

                                TextEntry::make('duration')
                                    ->label('Duration')
                                    ->state(function ($record) {
                                        if ($record->start_date && $record->end_date) {
                                            $startDate = is_string($record->start_date)
                                                ? Carbon::parse($record->start_date)
                                                : $record->start_date;

                                            $endDate = is_string($record->end_date)
                                                ? Carbon::parse($record->end_date)
                                                : $record->end_date;

                                            $diff = $startDate->diff($endDate);

                                            $parts = [];

                                            if ($diff->y > 0) {
                                                $parts[] = $diff->y.' year'.($diff->y > 1 ? 's' : '');
                                            }

                                            if ($diff->m > 0) {
                                                $parts[] = $diff->m.' month'.($diff->m > 1 ? 's' : '');
                                            }

                                            if ($diff->d > 0) {
                                                $parts[] = $diff->d.' day'.($diff->d > 1 ? 's' : '');
                                            }

                                            return implode(', ', $parts);
                                        }

                                        return 'Not specified';
                                    })
                                    ->icon('heroicon-m-calendar')
                                    ->color('primary'),

                                TextEntry::make('progress')
                                    ->label('Progress')
                                    ->state(function ($record) {
                                        if (! $record->start_date || ! $record->end_date) {
                                            return 'N/A';
                                        }

                                        $startDate = is_string($record->start_date)
                                            ? Carbon::parse($record->start_date)
                                            : $record->start_date;
                                        $endDate = is_string($record->end_date)
                                            ? Carbon::parse($record->end_date)
                                            : $record->end_date;
                                        $now = Carbon::now();

                                        if ($now->isBefore($startDate)) {
                                            $daysUntilStart = $now->diffInDays($startDate);

                                            return "Starts in {$daysUntilStart} day".($daysUntilStart > 1 ? 's' : '');
                                        } elseif ($now->isAfter($endDate)) {
                                            $daysSinceEnd = $endDate->diffInDays($now);

                                            return "Ended {$daysSinceEnd} day".($daysSinceEnd > 1 ? 's' : '').' ago';
                                        } else {
                                            $totalDays = $startDate->diffInDays($endDate);
                                            $elapsedDays = $startDate->diffInDays($now);
                                            $percentage = $totalDays > 0 ? round(($elapsedDays / $totalDays) * 100) : 0;

                                            return "{$percentage}% complete";
                                        }
                                    })
                                    ->icon('heroicon-m-chart-bar')
                                    ->color(function ($record) {
                                        if (! $record->start_date || ! $record->end_date) {
                                            return 'gray';
                                        }

                                        $now = Carbon::now();
                                        $startDate = is_string($record->start_date)
                                            ? Carbon::parse($record->start_date)
                                            : $record->start_date;
                                        $endDate = is_string($record->end_date)
                                            ? Carbon::parse($record->end_date)
                                            : $record->end_date;

                                        if ($now->isBefore($startDate)) {
                                            return 'info';
                                        } elseif ($now->isAfter($endDate)) {
                                            return 'gray';
                                        } else {
                                            return 'success';
                                        }
                                    }),
                            ]),
                    ]),

                // Season Structure & Statistics
                Section::make('Season Structure')
                    ->description('Teams, phases, and gameweeks information')
                    ->icon('heroicon-m-squares-2x2')
                    ->schema([
                        Grid::make(4)
                            ->schema([
                                TextEntry::make('teams_count')
                                    ->label('Teams')
                                    ->state(fn ($record) => $record->teams()->count())
                                    ->icon('heroicon-m-user-group')
                                    ->color('primary')
                                    ->suffix(' teams'),

                                TextEntry::make('phases_count')
                                    ->label('Phases')
                                    ->state(fn ($record) => $record->seasonPhases()->count())
                                    ->icon('heroicon-m-squares-plus')
                                    ->color('info')
                                    ->suffix(' phases'),

                                TextEntry::make('gameweeks_count')
                                    ->label('Gameweeks')
                                    ->state(fn ($record) => $record->gameweeks()->count())
                                    ->icon('heroicon-m-calendar-days')
                                    ->color('success')
                                    ->suffix(' gameweeks'),

                                TextEntry::make('fantasy_teams_count')
                                    ->label('Fantasy Teams')
                                    ->state(fn ($record) => $record->fantasyTeams()->count())
                                    ->icon('heroicon-m-star')
                                    ->color('warning')
                                    ->suffix(' teams'),
                            ]),
                    ]),

                // Current Activity
                Section::make('Current Activity')
                    ->description('Active phases and gameweeks')
                    ->icon('heroicon-m-bolt')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('current_phase')
                                    ->label('Current Phase')
                                    ->badge(),

                                TextEntry::make('current_gameweek')
                                    ->label('Current Gameweek')
                                    ->state(function ($record) {
                                        $currentGameweek = $record->currentGameweek()->first();
                                        if ($currentGameweek) {
                                            return $currentGameweek->name;
                                        }

                                        return 'No active gameweek';
                                    })
                                    ->icon('heroicon-m-calendar')
                                    ->color(function ($record) {
                                        return $record->currentGameweek()->exists() ? 'success' : 'gray';
                                    }),
                            ]),

                        Grid::make(3)
                            ->schema([
                                TextEntry::make('upcoming_gameweeks')
                                    ->label('Upcoming Gameweeks')
                                    ->state(function ($record) {
                                        return $record->gameweeks()
                                            ->where('gameweeks.status', 'upcoming')
                                            ->count();
                                    })
                                    ->icon('heroicon-m-clock')
                                    ->color('info')
                                    ->suffix(' upcoming'),

                                TextEntry::make('ongoing_gameweeks')
                                    ->label('Ongoing Gameweeks')
                                    ->state(function ($record) {
                                        return $record->gameweeks()
                                            ->where('gameweeks.status', 'ongoing')
                                            ->count();
                                    })
                                    ->icon('heroicon-m-play')
                                    ->color('success')
                                    ->suffix(' ongoing'),

                                TextEntry::make('completed_gameweeks')
                                    ->label('Completed Gameweeks')
                                    ->state(function ($record) {
                                        return $record->gameweeks()
                                            ->where('gameweeks.status', 'completed')
                                            ->count();
                                    })
                                    ->icon('heroicon-m-check-circle')
                                    ->color('gray')
                                    ->suffix(' completed'),
                            ]),
                    ]),

                // Fantasy Activity
                Section::make('Fantasy Activity')
                    ->description('Fantasy leagues and participation statistics')
                    ->icon('heroicon-m-sparkles')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('leagues_count')
                                    ->label('Fantasy Leagues')
                                    ->state(fn ($record) => $record->leagues()->count())
                                    ->icon('heroicon-m-trophy')
                                    ->color('warning')
                                    ->suffix(' leagues'),

                                TextEntry::make('total_fantasy_players')
                                    ->label('Total Fantasy Players')
                                    ->state(function ($record) {
                                        return $record->gameweeks()
                                            ->withCount('fantasyPlayers')
                                            ->get()
                                            ->sum('fantasy_players_count');
                                    })
                                    ->icon('heroicon-m-users')
                                    ->color('primary')
                                    ->suffix(' selections'),

                                TextEntry::make('avg_fantasy_teams_per_league')
                                    ->label('Avg Teams per League')
                                    ->state(function ($record) {
                                        $leaguesCount = $record->leagues()->count();
                                        $fantasyTeamsCount = $record->fantasyTeams()->count();

                                        if ($leaguesCount > 0) {
                                            return round($fantasyTeamsCount / $leaguesCount, 1);
                                        }

                                        return '0';
                                    })
                                    ->icon('heroicon-m-calculator')
                                    ->color('info')
                                    ->suffix(' teams/league'),
                            ]),
                    ]),

                // System Information
                Section::make('System Information')
                    ->description('Creation and modification timestamps')
                    ->icon('heroicon-m-information-circle')
                    ->collapsible()
                    ->collapsed()
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('created_at')
                                    ->label('Created')
                                    ->dateTime('M j, Y \a\t g:i A')
                                    ->icon('heroicon-m-plus-circle')
                                    ->color('success'),

                                TextEntry::make('updated_at')
                                    ->label('Last Updated')
                                    ->dateTime('M j, Y \a\t g:i A')
                                    ->icon('heroicon-m-pencil-square')
                                    ->color('warning'),
                            ]),
                    ]),
            ]);
    }
}

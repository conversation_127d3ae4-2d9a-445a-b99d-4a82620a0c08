<?php

namespace App\Filament\Resources\Games\Schemas;

use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\TextSize;

class GameInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Match Overview')
                    ->description('Game details and schedule information')
                    ->schema([
                        TextEntry::make('gameweek.name')
                            ->label('Gameweek')
                            ->badge()
                            ->color('primary'),
                        TextEntry::make('game_date')
                            ->label('Date & Time')
                            ->dateTime()
                            ->dateTime('M j, Y g:i A')
                            ->icon('heroicon-o-calendar')
                            ->weight(FontWeight::Medium),
                    ])
                    ->columns(2),

                Section::make('Stadium')
                    ->description('Stadium information')
                    ->schema([
                        TextEntry::make('stadium.name')
                            ->label('Stadium')
                            ->icon('heroicon-o-map-pin'),
                        TextEntry::make('stadium.capacity')
                            ->label('Capacity')
                            ->icon('heroicon-o-user-group'),
                        ImageEntry::make('stadium.image_url')
                            ->label('Stadium Image')
                            ->disk('public')
                            ->visible(fn ($record) => $record->stadium && $record->stadium->image_url)
                            // ->imageHeight(200)
                            // ->imageWidth(400)
                            ->columnSpanFull(),
                    ])
                    ->columns(3),

                Section::make('Teams & Result')
                    ->description('Competing teams and match outcome')
                    ->schema([
                        TextEntry::make('homeTeam.name')
                            ->label('Home Team')
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large)
                            ->icon('heroicon-o-home'),

                        TextEntry::make('match_score')
                            ->label('Score')
                            ->state(function ($record) {
                                if ($record->home_score !== null && $record->away_score !== null) {
                                    return $record->home_score.' - '.$record->away_score;
                                }

                                return 'vs';
                            })
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large),

                        TextEntry::make('awayTeam.name')
                            ->label('Away Team')
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large)
                            ->icon('heroicon-o-arrow-down-right'),
                    ])
                    ->columns(3),

                Section::make('Game Status')
                    ->description('Current status and additional information')
                    ->schema([
                        TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->color(fn ($state): string => match ($state?->value ?? $state) {
                                'scheduled' => 'gray',
                                'live' => 'warning',
                                'finished' => 'success',
                                'postponed' => 'danger',
                                'cancelled' => 'danger',
                                default => 'gray',
                            })
                            ->icon(fn ($state): string => match ($state?->value ?? $state) {
                                'scheduled' => 'heroicon-o-clock',
                                'live' => 'heroicon-o-play',
                                'finished' => 'heroicon-o-check-circle',
                                'postponed' => 'heroicon-o-pause',
                                'cancelled' => 'heroicon-o-x-circle',
                                default => 'heroicon-o-question-mark-circle',
                            }),

                        TextEntry::make('result_summary')
                            ->label('Result')
                            ->state(function ($record) {
                                $status = $record->status?->value ?? $record->status;
                                if ($status === 'finished' && $record->home_score !== null && $record->away_score !== null) {
                                    if ($record->home_score > $record->away_score) {
                                        return $record->homeTeam->name.' wins';
                                    } elseif ($record->away_score > $record->home_score) {
                                        return $record->awayTeam->name.' wins';
                                    } else {
                                        return 'Draw';
                                    }
                                }

                                return 'TBD';
                            })
                            ->badge()
                            ->color(function ($record, $state): string {
                                if ($state === 'Draw') {
                                    return 'warning';
                                }
                                $status = $record->status?->value ?? $record->status;
                                if ($status !== 'finished') {
                                    return 'gray';
                                }

                                return 'success';
                            })
                            ->visible(fn ($record) => ($record->status?->value ?? $record->status) === 'finished'),
                    ])
                    ->columns(2),

            ]);
    }
}

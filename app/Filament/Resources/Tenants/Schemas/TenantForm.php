<?php

namespace App\Filament\Resources\Tenants\Schemas;

use Filament\Forms\Components\ColorPicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class TenantForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Basic Information')
                    ->description('Configure the basic tenant details')
                    ->icon('heroicon-o-building-office')
                    ->columns(3)
                    ->schema([
                        TextInput::make('name')
                            ->label('Tenant Name')
                            ->required()
                            ->placeholder('e.g., Coaching Foot')
                            ->maxLength(255)
                            ->columnSpan(1),
                        TextInput::make('domain')
                            ->label('Domain')
                            ->required()
                            ->placeholder('e.g., fantasy.test')
                            ->maxLength(255)
                            ->columnSpan(1),
                        TextInput::make('database')
                            ->label('Database Name')
                            ->required()
                            ->placeholder('e.g., fantasy_db')
                            ->maxLength(255)
                            ->columnSpan(1),
                        Toggle::make('is_active')
                            ->label('Active Status')
                            ->helperText('Enable or disable this tenant')
                            ->default(true)
                            ->columnSpan(1),
                    ])->columnSpanFull(),

                Section::make('Visual Branding')
                    ->description('Customize the visual appearance and branding')
                    ->icon('heroicon-o-paint-brush')
                    ->columns(3)
                    ->schema([
                        FileUpload::make('logo')
                            ->label('Logo')
                            ->directory('logos')
                            ->disk('public')
                            ->image()
                            ->columnSpan(1),

                        FileUpload::make('favicon')
                            ->label('Favicon')
                            ->directory('favicons')
                            ->disk('public')
                            ->image()
                            ->columnSpan(1),
                        FileUpload::make('background_image')
                            ->label('Background Image')
                            ->directory('backgrounds')
                            ->disk('public')
                            ->image()
                            ->imageEditor()
                            ->imageResizeMode('cover')
                            ->imageCropAspectRatio('16:9')
                            ->maxSize(5120)
                            ->helperText('Upload a background image (recommended: 1920x1080px)')
                            ->columnSpan(1),
                        ColorPicker::make('primary_color')
                            ->label('Primary Color')
                            ->helperText('Main brand color for buttons and highlights')
                            ->columnSpan(1),
                        ColorPicker::make('secondary_color')
                            ->label('Secondary Color')
                            ->helperText('Accent color for secondary elements')
                            ->columnSpan(1),
                    ]),

                Section::make('Localization')
                    ->description('Language and regional settings')
                    ->icon('heroicon-o-language')
                    ->schema([
                        TagsInput::make('available_languages')
                            ->label('Available Languages')
                            ->placeholder('Add language codes (e.g., en, fr, ar)')
                            ->helperText('Enter language codes that will be available for this tenant')
                            ->suggestions(['en', 'fr', 'ar', 'es', 'de', 'it', 'pt'])
                            ->columnSpan('full'),
                    ]),
            ]);
    }
}

<?php

namespace App\Filament\Resources\Tenants\Schemas;

use Filament\Infolists\Components\ColorEntry;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Grid as ComponentsGrid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\IconSize;

class TenantInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                // Hero Section with Logo and Main Info
                Section::make()
                    ->schema([
                        Section::make([
                            // Logo and Favicon Section
                            ComponentsGrid::make(2)
                                ->schema([
                                    ImageEntry::make('logo')
                                        ->label('Tenant Logo')
                                        ->disk('public')
                                        ->circular()
                                        ->extraAttributes(['class' => 'shadow-lg ring-4 ring-primary-500/20'])
                                        ->tooltip('Tenant logo'),
                                    ImageEntry::make('favicon')
                                        ->label('Favicon')
                                        ->disk('public')
                                        ->square()
                                        ->extraAttributes(['class' => 'shadow-md ring-2 ring-gray-300/50'])
                                        ->tooltip('Tenant favicon'),
                                ]),

                            // Main Information
                            ComponentsGrid::make(1)
                                ->schema([
                                    TextEntry::make('name')
                                        ->label('Tenant Name')
                                        ->weight(FontWeight::Bold)
                                        ->size('xl')
                                        ->color('primary')
                                        ->icon('heroicon-o-building-office-2')
                                        ->copyable()
                                        ->copyMessage('Tenant name copied!')
                                        ->tooltip('Click to copy tenant name'),

                                    ComponentsGrid::make(2)
                                        ->schema([
                                            TextEntry::make('domain')
                                                ->label('Domain')
                                                ->weight(FontWeight::Medium)
                                                ->size('lg')
                                                ->icon('heroicon-o-globe-alt')
                                                ->copyable()
                                                ->copyMessage('Domain copied!')
                                                ->url(fn ($record) => 'https://'.$record->domain)
                                                ->openUrlInNewTab()
                                                ->color('primary')
                                                ->tooltip('Click to visit domain'),

                                            IconEntry::make('is_active')
                                                ->label('Status')
                                                ->boolean()
                                                ->trueIcon('heroicon-o-check-circle')
                                                ->falseIcon('heroicon-o-x-circle')
                                                ->trueColor('success')
                                                ->falseColor('danger')
                                                ->size(IconSize::Large)
                                                ->tooltip(fn ($state) => $state ? 'Tenant is active' : 'Tenant is inactive'),
                                        ]),
                                ]),
                        ]),
                    ])
                    ->compact()
                    ->extraAttributes(['class' => 'bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20']),

                // Technical Configuration
                Section::make('Technical Configuration')
                    ->description('Database and system configuration')
                    ->icon('heroicon-o-server-stack')
                    ->schema([
                        ComponentsGrid::make(2)
                            ->schema([
                                TextEntry::make('database')
                                    ->label('Database Name')
                                    ->icon('heroicon-o-circle-stack')
                                    ->weight(FontWeight::Medium)
                                    ->copyable()
                                    ->copyMessage('Database name copied!')
                                    ->badge()
                                    ->color('gray')
                                    ->tooltip('Database identifier for this tenant'),

                                TextEntry::make('available_languages')
                                    ->label('Supported Languages')
                                    ->icon('heroicon-o-language')
                                    ->placeholder('Not specified')
                                    ->weight(FontWeight::Medium)
                                    ->formatStateUsing(function ($state) {
                                        if (! $state) {
                                            return 'Not specified';
                                        }

                                        // Handle if it's JSON or comma-separated
                                        if (is_string($state)) {
                                            $languages = json_decode($state, true) ?? explode(',', $state);
                                        } else {
                                            $languages = (array) $state;
                                        }

                                        return collect($languages)
                                            ->map(fn ($lang) => trim($lang))
                                            ->filter()
                                            ->join(', ');
                                    })
                                    ->badge()
                                    ->separator(',')
                                    ->color(fn ($state) => $state ? 'success' : 'gray')
                                    ->tooltip('Languages available in this tenant'),
                            ]),
                    ])
                    ->collapsible()
                    ->persistCollapsed(),

                // Visual Branding
                Section::make('Visual Branding')
                    ->description('Colors, images, and visual identity')
                    ->icon('heroicon-o-paint-brush')
                    ->schema([
                        // Background Image
                        ImageEntry::make('background_image')
                            ->label('Background Image')
                            ->disk('public')
                            ->height(200)
                            ->width('100%')
                            ->extraAttributes(['class' => 'rounded-lg shadow-md'])
                            ->tooltip('Tenant background image')
                            ->columnSpanFull(),

                        // Color Palette
                        ComponentsGrid::make(2)
                            ->schema([
                                ColorEntry::make('primary_color')
                                    ->label('Primary Brand Color')
                                    ->copyable()
                                    ->copyMessage('Primary color copied!')
                                    ->tooltip('Main brand color used throughout the tenant')
                                    ->extraAttributes(['class' => 'ring-2 ring-offset-2 ring-gray-200 dark:ring-gray-700']),

                                ColorEntry::make('secondary_color')
                                    ->label('Secondary Brand Color')
                                    ->copyable()
                                    ->copyMessage('Secondary color copied!')
                                    ->tooltip('Secondary brand color for accents and highlights')
                                    ->extraAttributes(['class' => 'ring-2 ring-offset-2 ring-gray-200 dark:ring-gray-700']),
                            ]),
                    ])
                    ->collapsible()
                    ->persistCollapsed(),

                // System Information
                Section::make('System Information')
                    ->description('Record timestamps and metadata')
                    ->icon('heroicon-o-information-circle')
                    ->schema([
                        ComponentsGrid::make(2)
                            ->schema([
                                TextEntry::make('created_at')
                                    ->label('Created')
                                    ->dateTime('M j, Y \a\t g:i A')
                                    ->icon('heroicon-o-plus-circle')
                                    ->color('success')
                                    ->weight(FontWeight::Medium)
                                    ->tooltip('When this tenant was first created')
                                    ->since()
                                    ->formatStateUsing(function ($state) {
                                        return $state->format('M j, Y \a\t g:i A').' ('.$state->diffForHumans().')';
                                    }),

                                TextEntry::make('updated_at')
                                    ->label('Last Updated')
                                    ->dateTime('M j, Y \a\t g:i A')
                                    ->icon('heroicon-o-pencil-square')
                                    ->color('warning')
                                    ->weight(FontWeight::Medium)
                                    ->tooltip('When this tenant was last modified')
                                    ->since()
                                    ->formatStateUsing(function ($state) {
                                        return $state->format('M j, Y \a\t g:i A').' ('.$state->diffForHumans().')';
                                    }),
                            ]),
                    ])
                    ->collapsible()
                    ->collapsed()
                    ->persistCollapsed()
                    ->compact(),
            ]);
    }
}

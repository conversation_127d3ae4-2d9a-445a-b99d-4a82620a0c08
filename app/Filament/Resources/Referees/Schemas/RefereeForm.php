<?php

namespace App\Filament\Resources\Referees\Schemas;

use App\Data\Countries;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class RefereeForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Referee Profile')
                    ->description('Personal information and profile photo')
                    ->schema([
                        FileUpload::make('image')
                            ->label('Profile Photo')
                            ->image()
                            ->avatar()
                            ->directory('referees')
                            ->imageEditor()
                            ->columnSpanFull(),

                        TextInput::make('name')
                            ->label('Full Name')
                            ->required()
                            ->string()
                            ->autofocus()
                            ->maxLength(255)
                            ->placeholder('Enter full name'),

                        TextInput::make('name_ar')
                            ->label('Name (Arabic)')
                            ->string()
                            ->maxLength(255)
                            ->placeholder('اسم الحكم'),

                        DatePicker::make('birthday')
                            ->label('Date of Birth')
                            ->native(false)
                            ->displayFormat('d/m/Y')
                            ->placeholder('Select birth date')
                            ->maxDate(now()->subYears(16))
                            ->minDate(now()->subYears(70)),

                        Select::make('country')
                            ->label('Nationality')
                            ->options(Countries::all())
                            ->searchable()
                            ->preload()
                            ->placeholder('Select country'),
                    ])
                    ->columns(2)
                    ->collapsible(),

                Section::make('Professional Information')
                    ->description('Details about referee’s licensing and experience')
                    ->schema([
                        TextInput::make('experience_years')
                            ->label('Years of Experience')
                            ->required()
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(40)
                            ->placeholder('0')
                            ->suffix('years')
                            ->helperText('Total years of refereeing experience'),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }
}

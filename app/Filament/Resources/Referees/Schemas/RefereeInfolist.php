<?php

namespace App\Filament\Resources\Referees\Schemas;

use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class RefereeInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema->schema([
            // Main layout with proper column structure
            Group::make()
                ->schema([
                    Section::make('Personal Information')
                        ->description('Basic personal details of the referee')
                        ->icon('heroicon-o-user')
                        ->schema([
                            TextEntry::make('name')
                                ->label('Full Name')
                                ->weight('semibold')
                                ->size('lg')
                                ->icon('heroicon-m-user'),

                            TextEntry::make('name_ar')
                                ->label('Name (Arabic)')
                                ->icon('heroicon-m-language'),

                            TextEntry::make('birthday')
                                ->label('Date of Birth')
                                ->date('d/m/Y')
                                ->icon('heroicon-m-cake')
                                ->badge()
                                ->color('info'),

                            TextEntry::make('country')
                                ->label('Country')
                                ->icon('heroicon-m-flag')
                                ->badge()
                                ->color('success'),
                        ])
                        ->columns(2)
                        ->collapsible(),

                    Section::make('Professional Details')
                        ->description('Career and professional information')
                        ->icon('heroicon-o-briefcase')
                        ->schema([
                            TextEntry::make('experience_years')
                                ->label('Years of Experience')
                                ->numeric()
                                ->icon('heroicon-m-academic-cap')
                                ->badge()
                                ->color('warning')
                                ->suffix(' years'),

                            // TextEntry::make('level')
                            //     ->label('Referee Level')
                            //     ->badge()
                            //     ->icon('heroicon-m-star')
                            //     ->color(fn ($state) => match ($state) {
                            //         'International' => 'success',
                            //         'National' => 'warning',
                            //         'Regional' => 'info',
                            //         default => 'gray'
                            //     })
                            //     ->placeholder('Not specified'),

                            // TextEntry::make('specialization')
                            //     ->label('Specialization')
                            //     ->icon('heroicon-m-trophy')
                            //     ->listWithLineBreaks()
                            //     ->bulleted()
                            //     ->placeholder('General referee'),
                        ])
                        ->columns(2)
                        ->collapsible(),
                ])
                ->columnSpan(2),

            // Sidebar
            Group::make()
                ->schema([
                    Section::make()
                        ->schema([
                            ImageEntry::make('image')
                                ->label('Profile Photo')
                                ->circular()
                                ->size(150)
                                ->defaultImageUrl(url('/images/default-referee.png')),
                        ]),

                    Section::make('Statistics')
                        ->schema([
                            TextEntry::make('matches_officiated')
                                ->label('Matches Officiated')
                                ->numeric()
                                ->icon('heroicon-m-trophy')
                                ->placeholder('0'),

                            // TextEntry::make('rating')
                            //     ->label('Average Rating')
                            //     ->numeric(2)
                            //     ->icon('heroicon-m-star')
                            //     ->suffix('/5.0')
                            //     ->color(fn ($state) => $state >= 4 ? 'success' : ($state >= 3 ? 'warning' : 'danger'))
                            //     ->placeholder('No rating'),
                        ])
                        ->collapsible()
                        ->collapsed(),

                    Section::make('System Information')
                        ->schema([
                            TextEntry::make('created_at')
                                ->label('Created')
                                ->dateTime('d/m/Y H:i')
                                ->icon('heroicon-m-plus-circle')
                                ->color('gray'),

                            TextEntry::make('updated_at')
                                ->label('Last Updated')
                                ->dateTime('d/m/Y H:i')
                                ->icon('heroicon-m-pencil-square')
                                ->color('gray')
                                ->since(),
                        ])
                        ->collapsible()
                        ->collapsed(),
                ])
                ->columnSpan(1),
        ])
            ->columns(3);
    }
}

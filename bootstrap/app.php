<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->web(append: [
            \App\Http\Middleware\HandleInertiaRequests::class,
            \App\Http\Middleware\SetLocaleFromTenant::class,
            \App\Http\Middleware\EnsureCompetitionExists::class,
            \App\Http\Middleware\EnsureFantasyTeamExists::class,
            \App\Http\Middleware\EnsureValidFantasySquad::class,
        ]);

        $middleware
            ->group('tenant', [
                \Spatie\Multitenancy\Http\Middleware\NeedsTenant::class,
                \Spatie\Multitenancy\Http\Middleware\EnsureValidTenantSession::class,
            ]);

        $middleware->alias([
            'phone.verified' => \App\Http\Middleware\EnsurePhoneVerified::class,
        ]);
    })
    ->withSchedule(function (\Illuminate\Console\Scheduling\Schedule $schedule) {
        // Clear OTP codes every 3 days at 2:00 AM
        $schedule->command('otp:clear-old')
            ->cron('0 2 */3 * *') // Every 3 days at 2:00 AM
            ->withoutOverlapping()
            ->runInBackground();
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();

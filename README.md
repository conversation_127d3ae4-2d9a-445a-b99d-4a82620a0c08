# Coaching Foot v3

A modern football fantasy application built with Laravel 12, Filament 4, InertiaJS 2, React and Tailwind 4. Create your fantasy team, compete in leagues, and track player performances across football seasons.

## 🚀 Features

### Core Fantasy Football Features

-   **Fantasy Team Management**: Create and manage your fantasy football teams
-   **Player Trading System**: Transfer players in and out of your team
-   **League Competitions**: Join or create private leagues with friends
-   **Global Rankings**: Compete against all users worldwide
-   **Gameweek Scoring**: Points calculated based on real player performances
-   **Multi-Season Support**: Participate across different football seasons

### User Experience

-   **Modern React Frontend**: Built with Inertia.js for seamless SPA experience
-   **Admin Dashboard**: Comprehensive Filament-based admin panel
-   **Responsive Design**: Tailwind CSS for mobile-first design
-   **Real-time Updates**: Live scoring and rankings

### Business Features

-   **Subscription System**: Premium features and monetization
-   **Payment Processing**: Secure payment handling
-   **Multi-tenant Architecture**: Support for multiple competitions

## 🛠️ Tech Stack

### Backend

-   **Laravel 12**: Modern PHP framework
-   **PHP 8.3+**: Latest PHP features and performance
-   **Filament 4**: Admin panel and forms
-   **PostgreSQL**: Database support

### Frontend

-   **React**: Component-based UI library
-   **Inertia.js 2**: Modern monolith approach
-   **Tailwind CSS 4**: Utility-first CSS framework
-   **Vite 6**: Fast build tool and dev server

### Development Tools

-   **Laravel Pint**: Code style fixer
-   **Laravel Debugbar**: Development debugging
-   **Pest**: Testing framework
-   **Faker**: Test data generation

## 📊 Database Schema Overview

### Core Models

#### User Management

-   `User`: Application users with subscriptions and fantasy teams
-   `Subscription`: Premium feature access
-   `Payment`: Transaction records

#### Football Data

-   `Competition`: Football competitions (Premier League, La Liga, etc.)
-   `Season`: Football seasons within competitions
-   `SeasonPhase`: Phases within seasons (regular season, playoffs)
-   `Team`: Real football teams
-   `Player`: Real football players
-   `Game`: Individual football matches
-   `Gameweek`: Time periods for fantasy scoring

#### Fantasy System

-   `FantasyTeam`: User's fantasy teams for specific seasons
-   `FantasyPlayer`: Players selected in fantasy teams
-   `FantasyTransfer`: Player trading history
-   `FantasyPoint`: Points earned by players
-   `FantasyTeamLineup`: Team formations and lineups

#### Competition & Rankings

-   `League`: Private leagues between users
-   `RankingGlobal`: Overall user rankings
-   `RankingLeague`: League-specific rankings
-   `RankingFavoriteTeam`: Rankings by favorite team
-   `RankingJoinedGameweek`: Gameweek participation tracking

#### Performance Tracking

-   `PlayerPerformance`: Real player statistics
-   `FantasyPoint`: Fantasy points calculation

## 🚀 Getting Started

### Prerequisites

-   PHP 8.3 or higher
-   Composer
-   Node.js 20+ and npm
-   PostgreSQL

### Installation

1. **Clone the repository**

    ```bash
    <NAME_EMAIL>:Morgnus/fantasy.git
    cd fantasy
    ```

2. **Install PHP dependencies**

    ```bash
    composer install
    ```

3. **Install Node.js dependencies**

    ```bash
    npm install
    ```

4. **Environment setup**

    ```bash
    cp .env.example .env
    php artisan key:generate
    ```

5. **Database setup**

    ```bash
    # Configure your database in .env file
    php artisan migrate
    php artisan db:seed
    ```

6. **Build frontend assets**

    ```bash
    npm run build
    # or for development
    npm run dev
    ```

7. **Start the development server**
    ```bash
    php artisan serve
    ```

### Development Workflow

1. **Start the backend server**

    ```bash
    php artisan serve
    ```

2. **Start the frontend build process**
    ```bash
    npm run dev
    ```

## 🔧 Configuration

### Environment Variables

Key environment variables to configure:

```env
APP_NAME="Coaching Foot v3"
APP_ENV=local
APP_KEY=base64:...
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=coaching_foot_v3
DB_USERNAME=root
DB_PASSWORD=

# Add your specific configuration
```

### Admin Panel Access

The admin panel is built with Filament and provides:

-   User management
-   Competition setup
-   Player data management
-   League administration
-   System monitoring

## 🧪 Testing

```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run with coverage
php artisan test --coverage
```

## 📝 Code Style

```bash
# Fix code style
./vendor/bin/pint

# Check code style
./vendor/bin/pint --test
```

## 🚀 Deployment

### Production Setup

1. Set `APP_ENV=production` in `.env`
2. Set `APP_DEBUG=false`
3. Configure production database
4. Run `composer install --optimize-autoloader --no-dev`
5. Run `npm run build`
6. Run `php artisan migrate --force`
7. Run `php artisan config:cache`
8. Run `php artisan route:cache`
9. Run `php artisan view:cache`

### Performance Optimization

-   Enable OPcache in production
-   Use Redis for caching and sessions
-   Configure queue workers for background jobs
-   Set up proper database indexing

<?php

namespace Tests\Feature;

use App\Jobs\ClearOldOtpCodesJob;
use App\Models\OtpCode;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class OtpCleanupTest extends TestCase
{
    use RefreshDatabase;

    public function test_clear_old_otp_codes_command_deletes_old_records(): void
    {
        // Create old OTP codes (older than 3 days)
        $oldOtp1 = new OtpCode([
            'phone' => '+1234567890',
            'code' => '123456',
            'expires_at' => Carbon::now()->subDays(5),
            'used' => false,
        ]);
        $oldOtp1->timestamps = false;
        $oldOtp1->created_at = Carbon::now()->subDays(5);
        $oldOtp1->updated_at = Carbon::now()->subDays(5);
        $oldOtp1->save();

        $oldOtp2 = new OtpCode([
            'phone' => '+1234567891',
            'code' => '654321',
            'expires_at' => Carbon::now()->subDays(4),
            'used' => true,
        ]);
        $oldOtp2->timestamps = false;
        $oldOtp2->created_at = Carbon::now()->subDays(4);
        $oldOtp2->updated_at = Carbon::now()->subDays(4);
        $oldOtp2->save();

        // Create recent OTP codes (within 3 days)
        $recentOtp = new OtpCode([
            'phone' => '+1234567892',
            'code' => '789012',
            'expires_at' => Carbon::now()->subDays(1),
            'used' => false,
        ]);
        $recentOtp->timestamps = false;
        $recentOtp->created_at = Carbon::now()->subDays(1);
        $recentOtp->updated_at = Carbon::now()->subDays(1);
        $recentOtp->save();

        // Verify initial state
        $this->assertEquals(3, OtpCode::count());

        // Run the cleanup command
        Artisan::call('otp:clear-old', ['--days' => 3]);

        // Verify old records are deleted and recent ones remain
        $this->assertEquals(1, OtpCode::count());
        $this->assertTrue(OtpCode::where('id', $recentOtp->id)->exists());
        $this->assertFalse(OtpCode::where('id', $oldOtp1->id)->exists());
        $this->assertFalse(OtpCode::where('id', $oldOtp2->id)->exists());
    }

    public function test_clear_old_otp_codes_dry_run_does_not_delete(): void
    {
        // Create old OTP code
        $oldOtp = new OtpCode([
            'phone' => '+1234567890',
            'code' => '123456',
            'expires_at' => Carbon::now()->subDays(5),
            'used' => false,
        ]);
        $oldOtp->timestamps = false;
        $oldOtp->created_at = Carbon::now()->subDays(5);
        $oldOtp->updated_at = Carbon::now()->subDays(5);
        $oldOtp->save();

        // Verify initial state
        $this->assertEquals(1, OtpCode::count());

        // Run the cleanup command with dry-run
        Artisan::call('otp:clear-old', ['--days' => 3, '--dry-run' => true]);

        // Verify no records are deleted
        $this->assertEquals(1, OtpCode::count());
    }

    public function test_clear_old_otp_codes_job_deletes_old_records(): void
    {
        Queue::fake();

        // Create old OTP code
        $oldOtp = new OtpCode([
            'phone' => '+1234567890',
            'code' => '123456',
            'expires_at' => Carbon::now()->subDays(5),
            'used' => false,
        ]);
        $oldOtp->timestamps = false;
        $oldOtp->created_at = Carbon::now()->subDays(5);
        $oldOtp->updated_at = Carbon::now()->subDays(5);
        $oldOtp->save();

        // Create recent OTP code
        $recentOtp = new OtpCode([
            'phone' => '+1234567891',
            'code' => '654321',
            'expires_at' => Carbon::now()->subDays(1),
            'used' => false,
        ]);
        $recentOtp->timestamps = false;
        $recentOtp->created_at = Carbon::now()->subDays(1);
        $recentOtp->updated_at = Carbon::now()->subDays(1);
        $recentOtp->save();

        // Verify initial state
        $this->assertEquals(2, OtpCode::count());

        // Dispatch and handle the job directly (since queue is faked)
        $job = new ClearOldOtpCodesJob(3);
        $job->handle();

        // Verify old records are deleted and recent ones remain
        $this->assertEquals(1, OtpCode::count());
        $this->assertTrue(OtpCode::where('id', $recentOtp->id)->exists());
        $this->assertFalse(OtpCode::where('id', $oldOtp->id)->exists());
    }

    public function test_otp_code_model_methods(): void
    {
        // Create old OTP codes
        $oldOtp1 = new OtpCode([
            'phone' => '+1234567890',
            'code' => '123456',
            'expires_at' => Carbon::now()->subDays(5),
            'used' => false,
        ]);
        $oldOtp1->timestamps = false;
        $oldOtp1->created_at = Carbon::now()->subDays(5);
        $oldOtp1->updated_at = Carbon::now()->subDays(5);
        $oldOtp1->save();

        $oldOtp2 = new OtpCode([
            'phone' => '+1234567891',
            'code' => '654321',
            'expires_at' => Carbon::now()->subDays(4),
            'used' => true,
        ]);
        $oldOtp2->timestamps = false;
        $oldOtp2->created_at = Carbon::now()->subDays(4);
        $oldOtp2->updated_at = Carbon::now()->subDays(4);
        $oldOtp2->save();

        // Create recent OTP code
        $recentOtp = new OtpCode([
            'phone' => '+1234567892',
            'code' => '789012',
            'expires_at' => Carbon::now()->subDays(1),
            'used' => false,
        ]);
        $recentOtp->timestamps = false;
        $recentOtp->created_at = Carbon::now()->subDays(1);
        $recentOtp->updated_at = Carbon::now()->subDays(1);
        $recentOtp->save();

        // Test countOldCodes method
        $this->assertEquals(2, OtpCode::countOldCodes(3));
        $this->assertEquals(3, OtpCode::countOldCodes(0));

        // Test clearOldCodes method
        $deletedCount = OtpCode::clearOldCodes(3);
        $this->assertEquals(2, $deletedCount);
        $this->assertEquals(1, OtpCode::count());
    }
}

<?php

namespace Tests\Unit;

use App\Models\FantasyTeam;
use App\Models\FantasyTeamGameweek;
use App\Models\Gameweek;
use App\Models\RankingGlobal;
use App\Models\Tenant;
use App\Services\FantasyStatsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FantasyStatsServiceTest extends TestCase
{
    use RefreshDatabase;

    protected FantasyStatsService $statsService;
    protected FantasyTeam $fantasyTeam;
    protected Gameweek $gameweek;
    protected Tenant $tenant;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->statsService = new FantasyStatsService();
        
        // Create test data
        $this->tenant = Tenant::factory()->create();
        $this->fantasyTeam = FantasyTeam::factory()->create([
            'tenant_id' => $this->tenant->id,
        ]);
        $this->gameweek = Gameweek::factory()->create();
    }

    public function test_get_gameweek_points_returns_zero_when_no_data(): void
    {
        $points = $this->statsService->getGameweekPoints($this->fantasyTeam, $this->gameweek);
        
        $this->assertEquals(0, $points);
    }

    public function test_get_gameweek_points_returns_correct_points(): void
    {
        FantasyTeamGameweek::factory()->create([
            'fantasy_team_id' => $this->fantasyTeam->id,
            'gameweek_id' => $this->gameweek->id,
            'total_points' => 85,
        ]);

        $points = $this->statsService->getGameweekPoints($this->fantasyTeam, $this->gameweek);
        
        $this->assertEquals(85, $points);
    }

    public function test_get_transfers_used_returns_correct_count(): void
    {
        FantasyTeamGameweek::factory()->create([
            'fantasy_team_id' => $this->fantasyTeam->id,
            'gameweek_id' => $this->gameweek->id,
            'transfers_made' => 3,
        ]);

        $transfers = $this->statsService->getTransfersUsed($this->fantasyTeam, $this->gameweek);
        
        $this->assertEquals(3, $transfers);
    }

    public function test_get_gameweek_rank_returns_null_when_no_ranking(): void
    {
        $rank = $this->statsService->getGameweekRank($this->fantasyTeam, $this->gameweek, $this->tenant);
        
        $this->assertNull($rank);
    }

    public function test_get_gameweek_rank_returns_correct_rank(): void
    {
        RankingGlobal::factory()->create([
            'fantasy_team_id' => $this->fantasyTeam->id,
            'gameweek_id' => $this->gameweek->id,
            'tenant_id' => $this->tenant->id,
            'rank_gameweek' => 123,
        ]);

        $rank = $this->statsService->getGameweekRank($this->fantasyTeam, $this->gameweek, $this->tenant);
        
        $this->assertEquals(123, $rank);
    }

    public function test_get_highest_gameweek_points_returns_maximum(): void
    {
        // Create multiple gameweek records with different points
        FantasyTeamGameweek::factory()->create([
            'fantasy_team_id' => $this->fantasyTeam->id,
            'total_points' => 65,
        ]);
        
        FantasyTeamGameweek::factory()->create([
            'fantasy_team_id' => $this->fantasyTeam->id,
            'total_points' => 120, // This should be the highest
        ]);
        
        FantasyTeamGameweek::factory()->create([
            'fantasy_team_id' => $this->fantasyTeam->id,
            'total_points' => 85,
        ]);

        $highest = $this->statsService->getHighestGameweekPoints($this->fantasyTeam);
        
        $this->assertEquals(120, $highest);
    }

    public function test_get_average_gameweek_points_calculates_correctly(): void
    {
        // Create gameweek records: 60, 80, 90 points (average = 76.67, rounded = 76.7)
        FantasyTeamGameweek::factory()->create([
            'fantasy_team_id' => $this->fantasyTeam->id,
            'total_points' => 60,
        ]);
        
        FantasyTeamGameweek::factory()->create([
            'fantasy_team_id' => $this->fantasyTeam->id,
            'total_points' => 80,
        ]);
        
        FantasyTeamGameweek::factory()->create([
            'fantasy_team_id' => $this->fantasyTeam->id,
            'total_points' => 90,
        ]);

        $average = $this->statsService->getAverageGameweekPoints($this->fantasyTeam);
        
        $this->assertEquals(76.7, $average);
    }

    public function test_get_fantasy_team_stats_returns_complete_data(): void
    {
        // Create test data
        FantasyTeamGameweek::factory()->create([
            'fantasy_team_id' => $this->fantasyTeam->id,
            'gameweek_id' => $this->gameweek->id,
            'total_points' => 75,
            'transfers_made' => 2,
        ]);

        RankingGlobal::factory()->create([
            'fantasy_team_id' => $this->fantasyTeam->id,
            'gameweek_id' => $this->gameweek->id,
            'tenant_id' => $this->tenant->id,
            'rank_gameweek' => 456,
        ]);

        $stats = $this->statsService->getFantasyTeamStats($this->fantasyTeam, $this->gameweek);
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('gameweek_points', $stats);
        $this->assertArrayHasKey('transfers_used', $stats);
        $this->assertArrayHasKey('gameweek_rank', $stats);
        $this->assertArrayHasKey('highest_points', $stats);
        $this->assertArrayHasKey('average_points', $stats);
        $this->assertArrayHasKey('total_points', $stats);
        
        $this->assertEquals(75, $stats['gameweek_points']);
        $this->assertEquals(2, $stats['transfers_used']);
        $this->assertEquals(456, $stats['gameweek_rank']);
    }
}

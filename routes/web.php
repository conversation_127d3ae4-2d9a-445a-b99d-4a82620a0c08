<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\CompetitionController;
use App\Http\Controllers\FixturesController;
use App\Http\Controllers\GameController;
use App\Http\Controllers\LeagueController;
use App\Http\Controllers\LocaleController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\TransferController;
use Illuminate\Support\Facades\Route;

// Fallback login route for Laravel's default auth system
Route::get('/login', function () {
    return redirect()->route('auth.onboarding');
})->name('login');

// Authentication routes
Route::prefix('auth')->name('auth.')->group(function () {
    // Onboarding and phone submission
    Route::get('/onboarding', [AuthController::class, 'showOnboarding'])->name('onboarding');
    Route::post('/phone', [AuthController::class, 'submitPhone'])->name('phone.submit');

    // Profile completion
    Route::get('/profile', [AuthController::class, 'showProfile'])->name('profile.show');
    Route::post('/profile', [AuthController::class, 'storeProfile'])->name('profile.store');

    // OTP verification
    Route::get('/otp', [AuthController::class, 'showOtp'])->name('otp.show');
    Route::post('/otp', [AuthController::class, 'verifyOtp'])->name('otp.verify');
    Route::post('/otp/resend', [AuthController::class, 'resendOtp'])->name('otp.resend');

    // Logout
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
});

    // Protected routes - require authentication and phone verification
    Route::middleware(['auth', 'phone.verified'])->group(function () {
        Route::get('/', [\App\Http\Controllers\HomeController::class, 'index'])->name('home');

        Route::get('/my-team', [GameController::class, 'myTeam'])->name('game.team');
        Route::post('/my-team/update', [GameController::class, 'updateTeam'])->name('game.team.update');
        Route::get('/points', [GameController::class, 'points'])->name('game.points');

    // Transfer routes
    Route::get('/transfers', [TransferController::class, 'index'])->name('game.transfers');
    Route::get('/transfers/available-players', [TransferController::class, 'availablePlayers'])->name('game.transfers.available-players');
    Route::post('/transfers', [TransferController::class, 'processTransfers'])->name('game.transfers.process');

    Route::get('/leagues', [LeagueController::class, 'index'])->name('game.leagues');
    Route::post('/leagues', [LeagueController::class, 'store'])->name('leagues.store');
    Route::get('/leagues/gameweeks', [LeagueController::class, 'getGameweeks'])->name('leagues.gameweeks');
    Route::post('/leagues/{league}/join', [LeagueController::class, 'join'])->name('leagues.join');
    Route::post('/leagues/join-by-code', [LeagueController::class, 'joinByCode'])->name('leagues.join-by-code');
    Route::get('/leagues/{league}', [LeagueController::class, 'show'])->name('leagues.show');
    Route::get('/leagues/{league}/ranking', [LeagueController::class, 'ranking'])->name('leagues.ranking');
    Route::get('/leagues/{league}/transfers', [LeagueController::class, 'transfers'])->name('leagues.transfers');
    Route::get('/leagues/{league}/standings', [LeagueController::class, 'standings'])->name('leagues.standings');
    Route::get('/leagues/{league}/global-rankings', [LeagueController::class, 'globalRankings'])->name('leagues.global-rankings');
    Route::delete('/leagues/{league}', [LeagueController::class, 'destroy'])->name('leagues.leave');
    // Route::get('/leagues/{league}/news', [LeagueController::class, 'leagueNews'])->name('leagues.news');

    Route::get('/fixtures', [FixturesController::class, 'index'])->name('game.fixtures');

    // Fantasy team creation route
    Route::get('/fantasy-team/create', [\App\Http\Controllers\FantasyTeamController::class, 'create'])->name('fantasy-team.create');
    Route::post('/fantasy-team', [\App\Http\Controllers\FantasyTeamController::class, 'store'])->name('fantasy-team.store');

    // Fantasy team squad setup routes (step 2)
    Route::get('/fantasy-team/squad', [\App\Http\Controllers\FantasyTeamController::class, 'squad'])->name('fantasy-team.squad');
    Route::post('/fantasy-team/squad', [\App\Http\Controllers\FantasyTeamController::class, 'storeSquad'])->name('fantasy-team.squad.store');
    Route::get('/fantasy-team/available-players', [\App\Http\Controllers\FantasyTeamController::class, 'availablePlayers'])->name('fantasy-team.available-players');

    // Player performance endpoint
    Route::get('/players/{playerId}/performances', [\App\Http\Controllers\PlayerController::class, 'playerPerformance'])->name('players.performances');

    // Competition management routes
    Route::post('/competition/{competition}/set-current', [CompetitionController::class, 'setCurrent'])->name('competition.set-current');

    // Profile management routes
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/edit', [ProfileController::class, 'edit'])->name('edit');
        Route::put('/update', [ProfileController::class, 'update'])->name('update');
        Route::delete('/avatar', [ProfileController::class, 'deleteAvatar'])->name('avatar.delete');
    });

    // Locale switcher
    Route::post('/locale', [LocaleController::class, 'setLocale'])->name('locale.set');
});

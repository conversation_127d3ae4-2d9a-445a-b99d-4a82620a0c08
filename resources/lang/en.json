{"My Team": "My Team", "Manage your squad and formation": "Manage your squad and formation", "Transfers": "Transfers", "Buy and sell players": "Buy and sell players", "Leagues": "Leagues", "Join leagues and compete": "Join leagues and compete", "Fixtures": "Fixtures", "View upcoming matches": "View upcoming matches", "Dashboard": "Dashboard", "Welcome to": "Welcome to", "Build your dream team and compete with friends!": "Build your dream team and compete with friends!", "Ready to manage your team?": "Ready to manage your team?", "Team Value": "Team Value", "Total Points": "Total Points", "Global League Rank": "Global League Rank", "Current Week": "Current Week", "Recent Activity": "Recent Activity", "No recent activity": "No recent activity", "Upcoming Fixtures": "Upcoming Fixtures", "No upcoming fixtures": "No upcoming fixtures", "transferred in": "transferred in", "transferred out": "transferred out", "Cost: £": "Cost: £", "m": "m", "Transfer date:": "Transfer date:", "IN": "IN", "OUT": "OUT", "Fantasy Football - Build your dream team and compete with friends!": "Fantasy Football - Build your dream team and compete with friends!", "Edit Profile": "Edit Profile", "Language": "Language", "Log out": "Log out", "Fantasy Team Manager": "Fantasy Team Manager", "Squad Value": "Squad Value", "Balance": "Balance", "Changes saved successfully!": "Changes saved successfully!", "Failed to save changes. Please try again.": "Failed to save changes. Please try again.", "Changes have been reset. Click a player for options.": "Changes have been reset. Click a player for options.", "Reset": "Reset", "Confirm Changes": "Confirm Changes", "Select a player on the bench to swap with.": "Select a player on the bench to swap with.", "Select a player on the pitch to swap with.": "Select a player on the pitch to swap with.", "Click a player for options.": "Click a player for options.", "Formation not found, using fallback": "Formation {{<PERSON><PERSON><PERSON>}} not found, using 4-4-2 fallback", "No formation position available for player, using fallback": "No formation position available for {{player<PERSON>ame}} ({{position}}), using fallback", "Not enough funds. Need {{amount}}.": "Not enough funds. Need {{amount}}.", "Team must have exactly one Goalkeeper.": "Team must have exactly one Goalkeeper.", "Team must have 3 to 5 Defenders.": "Team must have 3 to 5 Defenders.", "Team must have 3 to 5 Midfielders.": "Team must have 3 to 5 Midfielders.", "Team must have 1 to 3 Forwards.": "Team must have 1 to 3 Forwards.", "Invalid formation: {{formationKey}}.": "Invalid formation: {{formationKey}}.", "Position:": "Position:", "Price:": "Price:", "Status:": "Status:", "On Pitch": "On Pitch", "On Bench": "On Bench", "More detailed player statistics and information can be displayed here.": "More detailed player statistics and information can be displayed here.", "Close": "Close", "Player Actions": "Player Actions", "Swap Player": "Swap Player", "Make Captain": "Make Captain", "Make Vice-Captain": "Make Vice-Captain", "Cancel": "Cancel", "Are you sure you want to make the following changes?": "Are you sure you want to make the following changes?", "Confirm": "Confirm", "Team updated": "Team updated", "Players Swapped:": "Players Swapped:", "New Captain:": "New Captain:", "New Vice-Captain:": "New Vice-Captain:", "No changes to confirm.": "No changes to confirm.", "points": "Points", "myTeam": {"title": "Fantasy Team Manager", "squadValue": "Squad Value", "balance": "Balance", "pitch": "Pitch", "bench": "Bench", "playerActions": "Player Actions", "position": "Position:", "price": "Price:", "status": "Status:", "onPitch": "On Pitch", "onBench": "On Bench", "playerStatsInfo": "More detailed player statistics and information can be displayed here.", "close": "Close", "playerInfo": {"position": "Position", "price": "Price", "birthday": "Birthday", "age": "Age", "country": "Country", "status": "Status", "performance": "Performance (Season)", "noPerformance": "No performances yet this season.", "statsPlaceholder": "Detailed stats and fixtures can be shown here."}, "actions": {"close": "Close"}, "swapPlayer": "Swap Player", "makeCaptain": "Make Captain", "makeViceCaptain": "Make Vice-Captain", "confirmChanges": "Confirm Changes", "playersIn": "Players In:", "playersOut": "Players Out:", "newCaptain": "New Captain:", "newViceCaptain": "New Vice-Captain:", "cancel": "Cancel", "confirm": "Confirm", "clickPlayerForOptions": "Click a player for options.", "selectPlayerToSwap": "Selected {{player<PERSON>ame}}. Choose an eligible player to swap.", "selectionCancelled": "Selection cancelled.", "invalidSwap": "Invalid swap. Please select a highlighted player.", "substitutionSuccess": "{{playerIn}} has been substituted for {{playerOut}}.", "selectCaptains": "Please select both a captain and a vice-captain.", "changesSaved": "Changes saved successfully!", "saveFailed": "Failed to save changes. Please try again.", "changesReset": "Changes have been reset.", "reset": "Reset"}, "stats": {"gamesPlayed": "Games played", "ownGoals": "Own goals", "penaltiesCommitted": "Pens committed", "penaltiesCaused": "Pens caused", "goals": "Goals", "penaltiesMissed": "<PERSON><PERSON> missed", "goalsConceded": "Goals conceded", "penaltiesSaved": "Pens saved", "saves": "Saves", "yellowCards": "Yellow cards", "redCards": "Red cards"}, "fixtures": {"title": "Fixtures", "viewMatches": "View matches for {{competition}} - {{season}}", "currentCompetition": "Current Competition", "currentSeason": "Current Season", "errorLoading": "Error Loading Fixtures", "noFixtures": "No fixtures available for this gameweek", "gameweek": "Gameweek {{number}}", "current": "Current", "gameweekNavigation": "Gameweek Navigation", "paginationText": "{{current}} of {{total}}", "previousGameweeks": "Previous gameweeks", "nextGameweeks": "Next gameweeks", "ongoing": "Ongoing", "completed": "Completed", "upcoming": "Upcoming", "unknown": "Unknown", "match": "Match", "matches": "Matches", "noFixturesFound": "No fixtures found", "noFixturesScheduled": "There are no fixtures scheduled for this gameweek.", "gameweekFixtures": "{{gameweek}} Fixtures", "gameweekNumber": "Gameweek {{number}}"}, "transferManager": {"placeholders": {"addPlayer": "Add {{position}}"}, "validation": {"samePosition": "Players must be in the same position.", "notEnoughFunds": "Not enough funds. You need an extra {{amount}}m.", "maxPlayers": "You can only have a maximum of {{max}} players from {{team}}."}, "playerInfo": {"position": "Position", "team": "Team", "price": "Price", "age": "Age", "country": "Country", "statsPlaceholder": "More detailed player statistics and information can be displayed here."}, "actions": {"close": "Close", "resetFilters": "Reset", "createTeam": "Create Team", "confirmTransfers": "Confirm Transfers", "transfer": "transfer player"}, "player": {"info": "Player Info"}, "titles": {"squad": "Your Squad", "transferMarket": "Transfer Market"}, "filters": {"allPositions": "All Positions", "allTeams": "All Teams", "maxPrice": "Max Price"}, "market": {"player": "Player", "team": "Team", "pos": "Pos", "price": "Price"}, "budget": {"squadValue": "Squad Value", "remaining": "Remaining", "freeTransfers": "Free Transfers", "unlimited": "Unlimited"}, "captain": {"title": "Select Captain & Vice-Captain", "selectCaptain": "Select Captain", "selectViceCaptain": "Select Vice-Captain", "captain": "Captain", "viceCaptain": "Vice-Captain"}, "confirmation": {"title": "Confirm Your Changes", "changes": "Are you sure you want to make the following changes?", "playersIn": "Players In:", "playersOut": "Players Out:", "captain": "New Captain:", "viceCaptain": "New Vice-Captain:", "none": "None", "cost": "Total Cost", "pointsDeducted": "{{points}} points will be deducted.", "free": "This is a free transfer."}, "messages": {"selectionCancelled": "Selection cancelled.", "selectPlayerToAdd": "Select a player from the market to add as a {{position}}.", "selectPlayerToTransfer": "Select a player from the market to replace {{name}}.", "playerAdded": "{{name}} added to squad.", "changesReset": "Changes have been reset.", "squadCreated": "Squad created successfully!", "transfersConfirmed": "Transfers confirmed successfully!"}, "selectPlaceholderToAddPlayer": "Select a placeholder to add a player.", "selectPlayerToTransferOut": "Select a player from your squad to transfer out.", "pagination": {"previous": "Previous", "next": "Next", "pageXOfY": "Page {{current}} of {{total}}"}}, "budget": {"squadValue": "Squad Value", "remainingBudget": "Remaining Budget", "freeTransfers": "Free Transfers"}, "fantasyTeam": {"title": "Create Fantasy Team", "heroTitle": "Create Your Fantasy Team", "heroSubtitle": "Set up your team for {{competition}} - {{season}}", "teamSetup": "Team Setup", "teamSetupDescription": "Choose your team name and customize your kit", "teamName": "Team Name", "teamNamePlaceholder": "Enter your team name", "favoriteTeam": "Favorite Team", "favoriteTeamPlaceholder": "Select your favorite team", "kitCustomization": "Kit Customization", "kitType": "Kit Type", "kitTypePlaceholder": "Select kit type", "primaryColor": "Primary Color", "secondaryColor": "Secondary Color", "kitPreview": "Kit Preview", "createTeam": "Create Fantasy Team", "creatingTeam": "Creating Team...", "startingBudget": "Starting Budget:", "changeSettingsLater": "You can change your team name and kit colors later in your profile settings.", "validation": {"teamNameRequired": "Please enter a team name", "teamNameTooShort": "Team name must be at least 2 characters", "teamNameTooLong": "Team name must be less than 50 characters", "invalidHexColor": "Please enter a valid hex color code"}, "messages": {"teamCreatedSuccess": "Fantasy team created successfully!"}, "shirtTypes": {"striped": "Striped Kit", "filled": "Filled Kit", "halved": "<PERSON><PERSON>", "sash": "<PERSON><PERSON>", "hoops": "Hooped Kit", "checkered": "Checkered Kit"}, "teamAbbreviation": "Team Abbreviation (3 letters)", "selectShirtColor": "Select Shirt Color", "selectSecondaryColor": "Select Secondary Color", "createTeamButton": "Create Team", "squad": {"unknown": "Unknown", "squad": "Squad", "createYourSquad": "Create Your Squad", "buildYourDreamTeam": "Build your dream team with the best players"}}, "pagination": {"previous": "Previous", "next": "Next", "showing": "Showing", "of": "of", "page": "Page"}, "Page": "Page", "of": "of", "leaderboard": {"title": "Leagues", "heroTitle": "Leagues", "heroSubtitle": "Join leagues and compete with friends", "tabs": {"myLeagues": "My Leagues", "joinLeague": "Join League", "createLeague": "Create League", "globalRanking": "Global Ranking"}, "standings": {"title": "Friends League - Current Standings", "rank": "Rank", "name": "Name", "team": "Team", "totalPoints": "Total Points", "lastWeek": "Last Week"}, "join": {"privateTitle": "Join Private League", "placeholder": "Enter league code", "joining": "Joining...", "joinButton": "Join League"}, "create": {"title": "Create New League", "leagueName": "League Name", "leagueNamePlaceholder": "Enter league name", "leagueType": "League Type", "leagueTypePlaceholder": "Select league type", "leagueLogo": "League Logo", "logoPlaceholder": "Click to upload a logo", "logoHelp": "Recommended size: 512x512px. Max 2MB", "privateLabel": "Private (Invite Only)", "publicLabel": "Public (Anyone can join)", "leagueInformation": "League Information", "organization": "Organization:", "season": "Season:", "currentCompetition": "Current Active Competition", "owner": "Owner:", "you": "You", "currentUser": "Current User", "inviteCode": "Invite Code:", "autoGenerated": "Auto-generated", "creating": "Creating...", "createButton": "Create League"}}, "leagueCard": {"members": "Members:", "rank": "Rank:", "points": "Points:", "entry": "Entry:", "prize": "Prize:", "code": "Code:", "leave": "Leave League", "join": "Join League", "types": {"private": "Private", "public": "Public"}, "alert": {"title": "Are you sure?", "description": "This action cannot be undone. You will be removed from the league.", "cancel": "Cancel", "confirm": "Confirm"}}, "ranking": {"leagueRanking": "League Ranking", "topThreeTeams": "Top 3 Teams", "leagueStandings": "League Standings", "teams": "Teams", "selectSeason": "Select Season", "selectPhase": "Select Phase", "selectGameweek": "Select Gameweek", "rank": "Rank", "team": "Team", "points": "Points", "pts": "pts", "first": "1st", "second": "2nd", "third": "3rd", "previous": "Previous", "next": "Next", "pageXOfY": "Page {{current}} of {{total}}", "noRankingsMessage": "No rankings available for this period. Be the first to make your mark!"}, "leagueNews": {"transferHighlights": "Transfer Highlights", "leagueTransferNews": "League Transfer News", "noTransferHighlights": "No transfer highlights available", "mostActiveTeam": "Most Active Team", "costlyTransfer": "Costly Transfer", "popularPick": "Popular Pick", "quietTransferWindow": "Quiet Transfer Window", "noTransfersMessage": "No transfers made this gameweek. Teams are keeping faith in their squads!", "madeTransfers": "{{count}} transfer(s) this gameweek", "spendingPoints": "spending {{cost}} points", "paidPoints": "paid {{cost}} points for transfers", "transferredInBy": "{{player}} was transferred in by {{count}} teams", "playerSwap": "Player Swap", "playerSigned": "Player Signed", "playerReleased": "Player Released", "transferActivity": "Transfer Activity", "swappedPlayers": "{{team}} swapped {{playerOut}} for {{playerIn}}", "signedPlayer": "{{team}} signed {{player}}", "releasedPlayer": "{{team}} released {{player}}", "madeTransferChange": "{{team}} made a transfer change", "costPoints": "(Cost: {{cost}} points)", "madeTransfersWithCost": "made {{count}} transfer(s) this gameweek, spending {{cost}} points", "madeTransfersNoCost": "made {{count}} transfer(s) this gameweek", "signedPlayerFree": "{{team}} signed {{player}} (Free Transfer)", "signedPlayerCost": "{{team}} signed {{player}} for {{cost}} points", "swappedPlayersFree": "{{team}} swapped {{playerOut}} for {{playerIn}} (Free Transfer)", "swappedPlayersCost": "{{team}} swapped {{playerOut}} for {{playerIn}} for {{cost}} points"}, "transfers": {"title": "Transfers", "subtitle": "Buy and sell players to build your dream team", "freeTransfer": "Free Transfer", "cost": "Cost"}, "No news available for this gameweek": "No news available for this gameweek", "pointsStats": {"yourPoints": "Your Points", "transfersUsed": "Transfers Used", "gwRank": "GW Rank", "highestPoints": "Highest Points", "averagePoints": "Average Points"}, "common": {"by": "by", "gameweek": "GW"}, "globalRanking": {"title": "Global Ranking", "description": "View the global rankings across all teams", "filters": "Filters", "phase": "Phase", "gameweek": "Gameweek", "selectPhase": "Select Phase", "selectGameweek": "Select Gameweek", "rankings": "Global Rankings", "teams": "teams", "loading": "Loading...", "noData": "No ranking data available for the selected gameweek", "rank": "Rank", "change": "Change", "manager": "Manager", "team": "Team", "points": "Points", "previous": "Previous", "next": "Next", "pageXOfY": "Page {{current}} of {{total}}"}, "playerPerformance": {"title": "Player Performance", "playedFor": "Played for:", "noData": {"title": "No Performance Data", "message": "This player didn't participate in any match during the selected gameweek."}, "pointsBreakdown": {"title": "Fantasy Points Breakdown", "total": "Total Points"}, "performance": {"title": "Performance Statistics", "minutesPlayed": "Minutes Played", "goalsScored": "Goals Scored", "assists": "Assists", "cleanSheet": "Clean Sheet", "yellowCards": "Yellow Cards", "redCards": "Red Cards", "penaltiesSaved": "Penalties Saved", "penaltiesMissed": "Penalties Missed", "saves": "Saves", "goalsConceded": "Goals Conceded"}, "actions": {"playedMinutes": "Played {{minutes}}+ minutes", "playedMinutesRange": "Played {{min}}-{{max}} minutes", "goalScored": "Goal scored", "assist": "Assist", "cleanSheet": "Clean sheet", "yellowCard": "Yellow card", "redCard": "Red card", "redCardSecondYellow": "Red card (2nd yellow)", "penaltySaved": "Penalty saved", "penaltyEarned": "Penalty earned", "penaltyMissed": "Penalty missed", "penaltyCommitted": "Penalty committed", "ownGoal": "Own goal", "goalConceded": "Goal conceded", "hatTrick": "Hat-trick bonus"}}}
/**
 * Format a date string into a human-readable format
 * 
 * @param dateString Date string to format
 * @returns Formatted date string
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-GB', {
    weekday: 'short',
    day: '2-digit',
    month: 'short',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
}

/**
 * Format price to currency format
 * 
 * @param price Price to format
 * @returns Formatted price string
 */
export function formatPrice(price: number): string {
  return `£${price.toFixed(1)}m`;
}

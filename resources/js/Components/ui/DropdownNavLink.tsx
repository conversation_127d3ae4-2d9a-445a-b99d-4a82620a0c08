import React from 'react';
import { Link } from '@inertiajs/react';
import { cn } from '@/lib/utils';

interface DropdownNavLinkProps {
    href: string;
    children: React.ReactNode;
    active?: boolean;
    className?: string;
}

const DropdownNavLink = React.forwardRef<HTMLAnchorElement, DropdownNavLinkProps>(
    ({ href, children, active = false, className, ...props }, ref) => {
        return (
            <Link
                href={href}
                ref={ref}
                className={cn(
                    'block w-full px-4 py-2 text-left text-sm leading-5 text-gray-700 transition duration-150 ease-in-out hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800 dark:focus:bg-gray-800',
                    { 'font-bold text-black dark:text-white': active },
                    className
                )}
                {...props}
            >
                {children}
            </Link>
        );
    }
);

DropdownNavLink.displayName = 'DropdownNavLink';

export default DropdownNavLink;

import React, { createContext, useContext } from 'react';
import { Link } from '@inertiajs/react';
import * as RadixNavigationMenu from '@radix-ui/react-navigation-menu';
import { cn } from '@/lib/utils';

// 1. Create a context to track whether we are inside a NavigationMenu
const NavMenuContext = createContext(false);

// 2. Create a custom NavigationMenu.Root that provides the context
const NavigationMenuRoot = React.forwardRef<React.ElementRef<typeof RadixNavigationMenu.Root>, React.ComponentPropsWithoutRef<typeof RadixNavigationMenu.Root>>(({ children, ...props }, ref) => (
    <RadixNavigationMenu.Root {...props} ref={ref}>
        <NavMenuContext.Provider value={true}>
            {children}
        </NavMenuContext.Provider>
    </RadixNavigationMenu.Root>
));
NavigationMenuRoot.displayName = RadixNavigationMenu.Root.displayName;

// 3. Export all other parts of RadixNavigationMenu as-is
const NavigationMenuList = RadixNavigationMenu.List;
const NavigationMenuItem = RadixNavigationMenu.Item;
const NavigationMenuLink = RadixNavigationMenu.Link;

// 4. Create the context-aware NavLink component
interface NavLinkProps {
    href: string;
    children: React.ReactNode;
    active?: boolean;
    className?: string;
}

const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(
    ({ href, children, active = false, className, ...props }, ref) => {
        const inNavMenu = useContext(NavMenuContext);

        const linkClasses = cn(
            'group inline-flex h-10 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium text-gray-200 transition-colors hover:bg-slate-100 hover:text-slate-900 focus:bg-slate-100 focus:text-slate-900 focus:outline-none disabled:pointer-events-none disabled:opacity-50',
            { 'bg-slate-100 text-slate-900': active },
            className
        );

        const linkContent = (
            <Link href={href} className={linkClasses} {...props} ref={ref}>
                {children}
            </Link>
        );

        // If we're inside a NavMenu, wrap it with the necessary Radix items
        if (inNavMenu) {
            return (
                <NavigationMenuItem>
                    <NavigationMenuLink asChild>{linkContent}</NavigationMenuLink>
                </NavigationMenuItem>
            );
        }

        // Otherwise, return the plain link
        return linkContent;
    }
);

NavLink.displayName = 'NavLink';

export { NavLink, NavigationMenuRoot, NavigationMenuList, NavigationMenuItem, NavigationMenuLink };

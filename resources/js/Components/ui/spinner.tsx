import React from "react";
import { Spinner as RadixSpinner } from "@radix-ui/themes";

// Radix Themes Spinner supports size tokens like 1 | 2 | 3 | 4...
export type SpinnerSize = 1 | 2 | 3;

export interface SpinnerProps {
  size?: SpinnerSize; // 1, 2, or 3 (Radix sizes)
  colorClassName?: string; // Tailwind class applied to color (uses currentColor)
  label?: string;
  className?: string;
}

export const Spinner: React.FC<SpinnerProps> = ({
  size = 2,
  colorClassName = "text-primary",
  label,
  className = "",
}) => {
  return (
    <div
      className={`inline-flex items-center gap-2 ${className} ${colorClassName}`}
      role="status"
      aria-live="polite"
      aria-busy="true"
    >
      <RadixSpinner size={size} />
      {label ? <span className="text-sm text-foreground/70">{label}</span> : null}
    </div>
  );
};

export default Spinner;

import * as React from "react";
import * as ProgressPrimitive from "@radix-ui/react-progress";

export function Progress({
    value,
    className,
}: {
    value: number;
    className?: string;
}) {
    return (
        <ProgressPrimitive.Root
            value={value}
            className={`relative h-1 w-full overflow-hidden `}
        >
            <ProgressPrimitive.Indicator
                className={`h-full ${className} transition-all duration-300`}
                style={{ width: `${value}%` }}
            />
        </ProgressPrimitive.Root>
    );
}

import { Card, CardContent } from "@/Components/ui/card";

interface HeroSectionProps {
    title: string;
    subtitle: string;
    userWelcome?: {
        firstName: string;
        message: string;
    };
}

export default function HeroSection({
    title,
    subtitle,
    userWelcome,
}: HeroSectionProps) {
    return (
        <div className="text-center space-y-6">
            <div className="space-y-4">
                <h1 className="text-4xl font-bold text-primary">{title}</h1>
                <p className="text-xl text-gray-200 max-w-2xl mx-auto">
                    {subtitle}
                </p>
            </div>

            {userWelcome && (
                <Card className="max-w-md mx-auto border-primary bg-green-50/20">
                    <CardContent className="pt-6">
                        <p className="text-gray-200">
                            Welcome back,{" "}
                            <span className="font-semibold">
                                {userWelcome.firstName}
                            </span>
                            ! {userWelcome.message}
                        </p>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}

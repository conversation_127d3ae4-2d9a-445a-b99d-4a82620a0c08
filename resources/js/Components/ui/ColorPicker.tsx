import React from 'react';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import * as Form from '@radix-ui/react-form';

interface ColorPickerProps {
    name: string;
    label: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    error?: string;
    placeholder?: string;
}

const ColorPicker: React.FC<ColorPickerProps> = ({ name, label, value, onChange, error, placeholder }) => {
    return (
        <Form.Field name={name} className="space-y-3">
            <Form.Label asChild>
                <Label className="mb-2">{label}</Label>
            </Form.Label>
            <div className="flex items-center space-x-3">
                <div
                    className="w-10 h-10 rounded border-2 border-gray-300 cursor-pointer"
                    style={{ backgroundColor: value }}
                    onClick={() => document.getElementById(name)?.click()}
                />
                <Form.Control asChild>
                    <Input
                        id={name}
                        type="color"
                        value={value}
                        onChange={onChange}
                        className={`flex-1 ${error ? 'border-red-500' : ''}`}
                        placeholder={placeholder}
                    />
                </Form.Control>
            </div>
            {error && (
                <p className="text-sm text-red-600">
                    {error}
                </p>
            )}
        </Form.Field>
    );
};

export default ColorPicker;

import { router } from "@inertiajs/react";
import { Settings, LogOut, Languages } from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
    DropdownMenuSub,
    DropdownMenuSubTrigger,
    DropdownMenuSubContent,
} from "@/Components/ui/dropdown-menu";

import { Auth, PageProps } from "@/types";
import { usePage } from "@inertiajs/react";
import { useTranslation } from "react-i18next";

export default function UserAvatarDropdown({ auth }: { auth: Auth }) {
    const { t, i18n } = useTranslation();
    const { app } = usePage<PageProps>().props;
    const { available_languages, locale } = app;
    const { user } = auth;

    if (!user) {
        return null;
    }

    const handleEditProfile = () => {
        router.visit(route("profile.edit"));
    };

    const handleLogout = () => {
        router.post(route("auth.logout"));
    };

    const handleLocaleChange = (newLocale: string) => {
        router.post(
            route("locale.set"),
            { locale: newLocale },
            {
                preserveState: true,
                onSuccess: () => {
                    router.reload({ only: ["app"] });
                },
            }
        );
    };

    const getInitials = () => {
        const firstInitial = user.first_name?.charAt(0) || "";
        const lastInitial = user.last_name?.charAt(0) || "";
        return (firstInitial + lastInitial).toUpperCase();
    };

    const getFullName = () => {
        return `${user.first_name || ""} ${user.last_name || ""}`.trim();
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <button className="flex items-center space-x-2 px-2 py-1 rounded-full text-gray-200 hover:text-gray-700 hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                    <div className="relative">
                        {user.avatar ? (
                            <img
                                src={user.avatar}
                                alt={getFullName()}
                                className="h-8 w-8 rounded-full object-cover"
                            />
                        ) : (
                            <div className="h-8 w-8 rounded-full bg-green-600 flex items-center justify-center text-white text-sm font-medium">
                                {getInitials()}
                            </div>
                        )}
                        <div className="absolute -bottom-0.5 -right-0.5 h-3 w-3 bg-green-400 border-2 border-white rounded-full"></div>
                    </div>
                    <span className="hidden sm:block text-sm font-medium">
                        {getFullName()}
                    </span>
                </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel>
                    <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                            {getFullName()}
                        </p>
                        <p className="text-xs leading-none text-gray-500">
                            {user.email}
                        </p>
                    </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                    onClick={handleEditProfile}
                    className="cursor-pointer"
                >
                    <Settings className="mr-2 h-4 w-4" />
                    <span>{t("Edit Profile")}</span>
                </DropdownMenuItem>
                <DropdownMenuSub>
                    <DropdownMenuSubTrigger>
                        <Languages className="mr-2 h-4 w-4" />
                        <span>{t("Language")}</span>
                    </DropdownMenuSubTrigger>
                    <DropdownMenuSubContent>
                        {available_languages.map((lang: string) => (
                            <DropdownMenuItem
                                key={lang}
                                onClick={() => handleLocaleChange(lang)}
                                className={locale === lang ? "bg-gray-100" : ""}
                            >
                                {lang.toUpperCase()}
                            </DropdownMenuItem>
                        ))}
                    </DropdownMenuSubContent>
                </DropdownMenuSub>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                    onClick={handleLogout}
                    className="cursor-pointer text-red-600 focus:text-red-600"
                >
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>{t("Log out")}</span>
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}

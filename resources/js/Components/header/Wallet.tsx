import { Wallet as WalletIcon } from "lucide-react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/Components/ui/dialog";
import { Auth } from "@/types";
import { useTranslation } from "react-i18next";

export default function Wallet({ auth }: { auth: Auth }) {
    const { t } = useTranslation();
    const { user } = auth;

    if (!user) {
        return null;
    }

    const walletBalance = user.wallet_balance ?? 0;

    return (
        <Dialog>
            <DialogTrigger asChild>
                <button className="flex items-center space-x-2 px-3 py-2 rounded-full text-gray-200 hover:text-gray-700 hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                    <WalletIcon className="h-5 w-5" />
                    <span className="hidden sm:block text-sm font-medium">
                        {walletBalance}
                    </span>
                </button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>{t("Select a package")}</DialogTitle>
                </DialogHeader>
                <div className="py-4">
                    <p>{t("Package selection will be implemented here.")}</p>
                </div>
            </DialogContent>
        </Dialog>
    );
}

import React from "react";
import { router, Link, usePage } from "@inertiajs/react";
import { PageProps } from "@/types/inertia";
import { Progress } from "./ui/Progress";
import { NavLink, NavigationMenuRoot, NavigationMenuList } from "./ui/NavLink";
import UserAvatarDropdown from "./header/UserAvatarDropdown";
import CompetitionSelector from "./header/CompetitionSelector";
import Wallet from "./header/Wallet";
import { Toaster } from "./ui/toaster";
import { Menu, X } from "lucide-react";
import { Button } from "./ui/button";
import { cn } from "@/lib/utils";
import {
    useTenantLogo,
    useTenantName,
    useTenantFavicon,
    useBackgroundImage,
    usePrimaryColor,
    useSecondaryColor,
} from "@/hooks/useTenant";
import { hexToHslString } from "@/lib/colors";
import { useFlashMessages } from "@/hooks/useFlashMessages";
import { useTranslation } from "react-i18next";

export default function Layout({
    auth,
    children,
}: React.PropsWithChildren<PageProps>) {
    const { t, i18n } = useTranslation();
    const { app } = usePage<PageProps>().props;
    const { locale, translations } = app;

    React.useEffect(() => {
        if (locale && translations) {
            i18n.addResourceBundle(
                locale,
                "translation",
                translations,
                true,
                true
            );
            i18n.changeLanguage(locale);
        }
    }, [locale, translations, i18n]);
    const user = auth?.user || null;
    const tenantName = useTenantName();
    const tenantLogo = useTenantLogo();
    const tenantFavicon = useTenantFavicon();
    const backgroundImage = useBackgroundImage();
    const primaryColor = usePrimaryColor();
    const secondaryColor = useSecondaryColor();
    const [isNavigating, setIsNavigating] = React.useState(false);
    const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false);

    // Handle flash messages
    useFlashMessages();

    React.useEffect(() => {
        const handleStart = () => setIsNavigating(true);
        const handleFinish = () => setIsNavigating(false);

        const removeStartListener = router.on("start", handleStart);
        const removeFinishListener = router.on("finish", handleFinish);

        return () => {
            removeStartListener();
            removeFinishListener();
        };
    }, []);

    // Dynamically update favicon when tenant changes
    React.useEffect(() => {
        // Remove existing dynamic favicon links (but keep default ones if no tenant favicon)
        const existingDynamicFavicons = document.querySelectorAll(
            'link[rel*="icon"][data-tenant="true"]'
        );
        existingDynamicFavicons.forEach((link) => link.remove());

        if (tenantFavicon) {
            const faviconUrl = `/storage/${tenantFavicon}`;

            // Remove all favicon links when we have a tenant favicon
            const allFavicons = document.querySelectorAll('link[rel*="icon"]');
            allFavicons.forEach((link) => link.remove());

            // Add new tenant favicon
            const link = document.createElement("link");
            link.rel = "icon";
            link.type = "image/x-icon";
            link.href = faviconUrl;
            link.setAttribute("data-tenant", "true");
            document.head.appendChild(link);

            // Also add apple-touch-icon for better mobile support
            const appleTouchIcon = document.createElement("link");
            appleTouchIcon.rel = "apple-touch-icon";
            appleTouchIcon.href = faviconUrl;
            appleTouchIcon.setAttribute("data-tenant", "true");
            document.head.appendChild(appleTouchIcon);
        }
    }, [tenantFavicon]);

    const primaryHsl = hexToHslString(primaryColor || "");
    const secondaryHsl = hexToHslString(secondaryColor || "");

    const tenantStyleRules = `
        :root {
            ${primaryHsl ? `--primary: ${primaryHsl};` : ""}
            ${secondaryHsl ? `--secondary: ${secondaryHsl};` : ""}
        }
    `;

    return (
        <>
            <style>{tenantStyleRules}</style>
            <div
                className="min-h-screen bg-cover bg-center bg-no-repeat bg-fixed bg-gradient-to-br from-[#0e1348] to-[#1a2282]"
                style={{
                    backgroundImage: `url('/storage/${backgroundImage}')`,
                }}
            >
                {/* Global Loading Indicator */}
                {/* {isNavigating && (
                    <div className="fixed top-0 left-0 right-0 z-50">
                        <Progress value={100} className="bg-primary" />
                    </div>
                )} */}

                {/* Navigation */}
                <header className="">
                    <div className="container mx-auto px-4 flex h-16 items-center justify-between">
                        <div className="flex items-center space-x-8">
                            <Link
                                href={route("home")}
                                className="flex items-center space-x-2 text-xl font-bold text-slate-900"
                            >
                                {tenantLogo ? (
                                    <img
                                        src={`/storage/${tenantLogo}`}
                                        alt="Tenant Logo"
                                        className="h-8"
                                    />
                                ) : (
                                    <span>{tenantName}</span>
                                )}
                            </Link>
                        </div>

                        {/* Competition Selector and User Avatar Dropdown */}
                        <div className="flex items-center space-x-4">
                            <div className="hidden md:block">
                                <CompetitionSelector />
                            </div>
                            <div className="block md:hidden">
                                {auth?.user && <Wallet auth={auth} />}
                            </div>
                            {auth?.user && <UserAvatarDropdown auth={auth} />}
                        </div>
                    </div>
                    <div className="transparent-card py-2">
                        <div className="container mx-auto px-4">
                            {/* Desktop Navigation */}
                            <NavigationMenuRoot className="relative z-10 hidden w-full flex-1 items-center justify-between md:flex">
                                <NavigationMenuList className="group flex flex-1 list-none items-center justify-center space-x-1">
                                    <NavLink
                                        href={route("home")}
                                        active={route().current("home")}
                                    >
                                        {t("Dashboard")}
                                    </NavLink>
                                    <NavLink
                                        href={route("game.team")}
                                        active={route().current("game.team")}
                                    >
                                        {t("My Team")}
                                    </NavLink>
                                    <NavLink
                                        href={route("game.points")}
                                        active={route().current("game.points")}
                                    >
                                        {t("points")}
                                    </NavLink>
                                    <NavLink
                                        href={route("game.transfers")}
                                        active={route().current(
                                            "game.transfers"
                                        )}
                                    >
                                        {t("Transfers")}
                                    </NavLink>
                                    <NavLink
                                        href={route("game.leagues")}
                                        active={route().current("game.leagues")}
                                    >
                                        {t("leaderboard.title")}
                                    </NavLink>
                                    <NavLink
                                        href={route("game.fixtures")}
                                        active={route().current(
                                            "game.fixtures"
                                        )}
                                    >
                                        {t("Fixtures")}
                                    </NavLink>
                                </NavigationMenuList>
                                {auth?.user && <Wallet auth={auth} />}
                            </NavigationMenuRoot>

                            {/* Mobile Navigation Button */}
                            <div className="md:hidden flex justify-between items-center">
                                <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => setMobileMenuOpen(true)}
                                >
                                    <Menu className="h-6 w-6" />
                                </Button>
                                <div className="block md:hidden">
                                    <CompetitionSelector />
                                </div>
                            </div>
                        </div>
                    </div>
                </header>

                {/* Main Content */}
                <main>{children}</main>

                {/* Toast Notifications */}
                <Toaster />

                {/* Footer */}
                <footer className="transparent-card">
                    <div className="container mx-auto px-4 py-8">
                        <div className="text-center text-sm space-y-2">
                            <p className="text-gray-200">
                                {t(
                                    "Fantasy Football - Build your dream team and compete with friends!"
                                )}
                            </p>
                        </div>
                    </div>
                </footer>

                {/* Mobile Menu Panel */}
                <div
                    className={cn(
                        "fixed inset-0 z-50 transform transition-transform duration-300 ease-in-out",
                        mobileMenuOpen ? "translate-x-0" : "translate-x-full"
                    )}
                >
                    <div
                        className="fixed inset-0 bg-black/30 backdrop-blur-xs"
                        onClick={() => setMobileMenuOpen(false)}
                    ></div>
                    <div className="fixed top-0 right-0 h-full w-72 max-w-[80vw] bg-background shadow-lg">
                        <div className="flex justify-end p-4">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => setMobileMenuOpen(false)}
                            >
                                <X className="h-6 w-6" />
                            </Button>
                        </div>
                        <nav className="flex flex-col p-4 space-y-2">
                            <NavLink
                                href={route("home")}
                                active={route().current("home")}
                                className="w-full justify-start text-lg text-foreground hover:bg-accent focus:bg-accent"
                            >
                                {t("Dashboard")}
                            </NavLink>
                            <NavLink
                                href={route("game.team")}
                                active={route().current("game.team")}
                                className="w-full justify-start text-lg text-foreground hover:bg-accent focus:bg-accent"
                            >
                                {t("My Team")}
                            </NavLink>
                            <NavLink
                                href={route("game.points")}
                                active={route().current("game.points")}
                                className="w-full justify-start text-lg text-foreground hover:bg-accent focus:bg-accent"
                            >
                                {t("Points")}
                            </NavLink>
                            <NavLink
                                href={route("game.transfers")}
                                active={route().current("game.transfers")}
                                className="w-full justify-start text-lg text-foreground hover:bg-accent focus:bg-accent"
                            >
                                {t("Transfers")}
                            </NavLink>
                            <NavLink
                                href={route("game.leagues")}
                                active={route().current("game.leagues")}
                                className="w-full justify-start text-lg text-foreground hover:bg-accent focus:bg-accent"
                            >
                                {t("Leagues")}
                            </NavLink>
                            <NavLink
                                href={route("game.fixtures")}
                                active={route().current("game.fixtures")}
                                className="w-full justify-start text-lg text-foreground hover:bg-accent focus:bg-accent"
                            >
                                {t("Fixtures")}
                            </NavLink>
                        </nav>
                    </div>
                </div>
            </div>
        </>
    );
}

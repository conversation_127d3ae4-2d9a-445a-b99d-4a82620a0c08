import { useTranslation } from "react-i18next";
import { Player } from "@/types/fantasy";
import React, { FC, useEffect, useRef } from "react";

const ActionMenu: FC<{
    player: Player;
    position: { top: number; left: number };
    onClose: () => void;
    onInitiateSwap: (player: Player) => void;
    onSetCaptain: (player: Player) => void;
    onSetViceCaptain: (player: Player) => void;
}> = ({
    player,
    position,
    onClose,
    onInitiateSwap,
    onSetCaptain,
    onSetViceCaptain,
}) => {
    const { t } = useTranslation();
    const menuRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                menuRef.current &&
                !menuRef.current.contains(event.target as Node)
            ) {
                onClose();
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [onClose]);

    return (
        <div
            ref={menuRef}
            className="absolute bg-white rounded-md shadow-lg z-20 border border-gray-200"
            style={{ top: position.top, left: position.left }}
        >
            <ul className="divide-y divide-gray-200">
                <li>
                    <button
                        onClick={() => onInitiateSwap(player)}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                        {t("myTeam.swapPlayer")}
                    </button>
                </li>
                <li>
                    <button
                        onClick={() => onSetCaptain(player)}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                        {t("myTeam.makeCaptain")}
                    </button>
                </li>
                <li>
                    <button
                        onClick={() => onSetViceCaptain(player)}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                        {t("myTeam.makeViceCaptain")}
                    </button>
                </li>
            </ul>
        </div>
    );
};

export default ActionMenu;
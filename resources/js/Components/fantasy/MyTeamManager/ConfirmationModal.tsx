import { useTranslation } from "react-i18next";
import { ConfirmationModalProps } from "@/types/fantasy";
import React, { FC } from "react";
import {
    ArrowLeftCircle,
    ArrowRightCircle,
    Shield,
    ShieldCheck,
} from "lucide-react";

const ConfirmationModal: FC<ConfirmationModalProps> = ({
    changes,
    onConfirm,
    onCancel,
}) => {
    const { t } = useTranslation();
    return (
        <div className="fixed inset-0 bg-black/10 backdrop-blur-lg flex justify-center items-center z-50">
            <div className="bg-white rounded-lg p-6 w-11/12 max-w-lg text-gray-800">
                <h2 className="text-2xl font-bold mb-4">
                    {t("myTeam.confirmChanges")}
                </h2>
                <div className="space-y-4">
                    {changes.playersIn.length > 0 && (
                        <div>
                            <h3 className="font-bold text-green-600 flex items-center">
                                <ArrowRightCircle className="mr-2" />
                                {t("myTeam.playersIn")}
                            </h3>
                            <ul>
                                {changes.playersIn.map((p) => (
                                    <li key={p.id}>{p.name}</li>
                                ))}
                            </ul>
                        </div>
                    )}
                    {changes.playersOut.length > 0 && (
                        <div>
                            <h3 className="font-bold text-red-600 flex items-center">
                                <ArrowLeftCircle className="mr-2" />
                                {t("myTeam.playersOut")}
                            </h3>
                            <ul>
                                {changes.playersOut.map((p) => (
                                    <li key={p.id}>{p.name}</li>
                                ))}
                            </ul>
                        </div>
                    )}
                    {changes.captain && (
                        <div>
                            <h3 className="font-bold text-purple-600 flex items-center">
                                <ShieldCheck className="mr-2" />
                                {t("myTeam.newCaptain")}
                            </h3>
                            <p>{changes.captain.name}</p>
                        </div>
                    )}
                    {changes.viceCaptain && (
                        <div>
                            <h3 className="font-bold text-gray-600 flex items-center">
                                <Shield className="mr-2" />
                                {t("myTeam.newViceCaptain")}
                            </h3>
                            <p>{changes.viceCaptain.name}</p>
                        </div>
                    )}
                </div>
                <div className="flex justify-end space-x-4 mt-6">
                    <button
                        onClick={onCancel}
                        className="bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded hover:bg-gray-400"
                    >
                        {t("myTeam.cancel")}
                    </button>
                    <button
                        onClick={onConfirm}
                        className="bg-green-500 text-white font-bold py-2 px-4 rounded hover:bg-green-700"
                    >
                        {t("myTeam.confirm")}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ConfirmationModal;

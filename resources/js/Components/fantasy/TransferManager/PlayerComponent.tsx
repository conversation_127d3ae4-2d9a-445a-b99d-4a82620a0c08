import React, { <PERSON> } from "react";
import { useTranslation } from "react-i18next";
import { TransferPlayer } from "@/types/fantasy";
import PlayerShirtComponent from "../common/PlayerShirtComponent";

const PlayerComponent: FC<{
    player: TransferPlayer;
    onClick: (player: TransferPlayer, element: HTMLElement) => void;
    onInfoClick: (player: TransferPlayer) => void;
    isSelected: boolean;
    position?: { x: number; y: number };
}> = ({ player, onClick, onInfoClick, isSelected, position }) => {
    const { t } = useTranslation();
    const isPlaceholder =
        typeof player.id === "string" && player.id.startsWith("placeholder");

    const playerStyle: React.CSSProperties = position
        ? {
              position: "absolute",
              left: `${position.x}%`,
              top: `${position.y}%`,
              transform: "translate(-50%, -50%)",
              cursor: "pointer",
              textAlign: "center",
              width: "90px",
              transition: "all 0.3s ease",
              border: isSelected
                  ? "3px solid #68d391"
                  : "3px solid transparent",
              borderRadius: "8px",
              padding: "2px",
          }
        : {};

    const playerRef = React.useRef<HTMLDivElement>(null);

    return (
        <div
            ref={playerRef}
            style={playerStyle}
            onClick={() =>
                playerRef.current && onClick(player, playerRef.current)
            }
            className="flex flex-col items-center group"
        >
            <div className="relative w-full">
                {isPlaceholder ? (
                    <div
                        className={`w-12 h-12 mx-auto rounded-full flex items-center justify-center text-white font-bold shadow-lg bg-gray-600 border-2 border-dashed`}
                    >
                        +
                    </div>
                ) : (
                    <div className="px-1">
                        <PlayerShirtComponent player={player as any} />
                    </div>
                )}

                <div
                    className={`text-xs truncate ${
                        isPlaceholder
                            ? "text-gray-200"
                            : "bg-black/30 bg-opacity-70 text-white"
                    } px-1 py-1 rounded mt-1`}
                >
                    {player.name}
                </div>
                {!isPlaceholder && (
                    <div className="text-xs inline-block text-white bg-black/30 shadow-lg px-2 rounded-xl">
                        {player.price}
                    </div>
                )}
            </div>
        </div>
    );
};

export default PlayerComponent;

import React, { <PERSON> } from "react";
import { useTranslation } from "react-i18next";
import StatsCard from "../StatsCard";

const BudgetDisplay: FC<{
    squadValue: number;
    balance: number;
    freeTransfers: number;
    isCreationMode: boolean;
}> = ({ squadValue, balance, freeTransfers, isCreationMode }) => {
    const { t } = useTranslation();
    return (
        <div
            className={`my-4 grid gap-6 ${
                isCreationMode ? "grid-cols-2" : "grid-cols-3"
            } text-white`}
        >
            <StatsCard
                value={squadValue.toFixed(1)}
                label={t("budget.squadValue")}
                className="text-center"
            />
            <StatsCard
                value={balance.toFixed(1)}
                label={t("budget.remainingBudget")}
                className="text-center"
            />
            {!isCreationMode && (
                <StatsCard
                    value={freeTransfers}
                    label={t("budget.freeTransfers")}
                    className="text-center"
                />
            )}
        </div>
    );
};

export default BudgetDisplay;

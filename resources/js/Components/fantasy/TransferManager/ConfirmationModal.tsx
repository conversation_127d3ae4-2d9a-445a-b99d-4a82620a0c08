import React, { FC } from "react";
import { useTranslation } from "react-i18next";
import { TransferPlayer } from "@/types/fantasy";

const ConfirmationModal: FC<{
    changes: {
        playersIn: TransferPlayer[];
        playersOut: TransferPlayer[];
        captain: TransferPlayer | null;
        viceCaptain: TransferPlayer | null;
    };
    cost: number;
    onConfirm: () => void;
    onCancel: () => void;
}> = ({ changes, cost, onConfirm, onCancel }) => {
    const { t } = useTranslation();
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white rounded-lg p-6 w-11/12 max-w-lg">
                <h2 className="text-2xl font-bold mb-4 text-gray-800">
                    Confirm Transfers
                </h2>
                <div className="space-y-4">
                    {changes.playersIn.length > 0 ? (
                        <>
                            <>
                                <div>
                                    <h3 className="font-bold text-green-600">
                                        Players In
                                    </h3>
                                    <ul>
                                        {changes.playersIn.map((p) => (
                                            <li key={p.id}>
                                                {p.name} ({p.price.toFixed(1)})
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                                <div>
                                    <h3 className="font-bold text-red-600">
                                        Players Out
                                    </h3>
                                    <ul>
                                        {changes.playersOut.map((p) => (
                                            <li key={p.id}>
                                                {p.name} ({p.price.toFixed(1)})
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </>
                            <div className="border-t pt-4 mt-4">
                                <h3 className="font-bold text-gray-800">
                                    Transfer Cost
                                </h3>
                                <p
                                    className={
                                        cost > 0
                                            ? "text-red-600"
                                            : "text-green-600"
                                    }
                                >
                                    {cost > 0 ? `-${cost} points` : "Free"}
                                </p>
                            </div>
                        </>
                    ) : (
                        <p>No transfers made.</p>
                    )}
                </div>
                <div className="flex justify-end space-x-4 mt-6">
                    <button
                        onClick={onCancel}
                        className="bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded hover:bg-gray-400"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={onConfirm}
                        className="bg-green-500 text-white font-bold py-2 px-4 rounded hover:bg-green-700"
                    >
                        Confirm
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ConfirmationModal;
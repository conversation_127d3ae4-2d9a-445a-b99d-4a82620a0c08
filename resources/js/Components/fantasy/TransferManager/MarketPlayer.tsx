import React, { FC } from "react";
import { useTranslation } from "react-i18next";
import { TransferPlayer } from "@/types/fantasy";
import { InfoIcon } from "lucide-react";

const MarketPlayer: FC<{
    player: TransferPlayer;
    onPlayerClick: (player: TransferPlayer) => void;
    onPlayerInfoClick: (player: TransferPlayer) => void;
    isEligible: boolean;
    isInSquad: boolean;
}> = ({ player, onPlayerClick, onPlayerInfoClick, isEligible, isInSquad }) => {
    const { t } = useTranslation();
    // Helper to build absolute logo URL from storage path (simple form)
    const buildTeamLogoUrl = (teamLogoRaw?: string | null) => {
        const appUrl = (import.meta as any)?.env?.VITE_APP_URL || window.location.origin;
        return teamLogoRaw
            ? `${appUrl}/storage/${teamLogoRaw.replace(/^\/?(?:storage\/)?/, "")}`
            : "/images/base-team-logo.png";
    };
    return (
        <tr
            onClick={() => isEligible && !isInSquad && onPlayerClick(player)}
            className={`border-b border-gray-700 ${isInSquad
                ? "bg-gray-800 text-gray-500"
                : isEligible
                    ? "bg-transparent cursor-pointer"
                    : "bg-gray-900 text-gray-500"
                }`}
        >
            <td className="pl-1">
                <button
                    onClick={(e) => {
                        e.stopPropagation();
                        onPlayerInfoClick(player);
                    }}
                    className="text-gray-500 cursor-pointer hover:text-gray-100"
                >
                    <InfoIcon />
                </button>
            </td>
            <td className="p-2 flex items-center space-x-2">
                <span>{player.name}</span>
            </td>
            <td className="p-2">
                <div className="flex items-center gap-2">
                    {/* Team logo with fallback; use app URL + /storage + player.team_data.logo */}
                    <img
                        src={buildTeamLogoUrl(player.team_data?.logo)}
                        alt={`${player.team} logo`
                        }
                        className="w-5 h-5 rounded-sm object-contain bg-gray-800"
                        onError={(e) => {
                            const target = e.currentTarget as HTMLImageElement;
                            if (target.src.indexOf("/images/base-team-logo.png") === -1) {
                                target.src = "/images/base-team-logo.png";
                            }
                        }}
                    />
                    <span>{player.team}</span>
                </div>
            </td>
            <td className="p-2">{player.position}</td>
            <td className="p-2 text-right">{player.price.toFixed(1)}</td>
        </tr>
    );
};

export default MarketPlayer;

import React, { FC, useMemo, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { TransferPlayer, TeamOption } from "@/types/fantasy";
import MarketPlayer from "./MarketPlayer";
import { validateTransfer } from "@/lib/fantasy/transfer-management";
import { Paginated } from "@/types/pagination";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/Components/ui/select";

interface TransferFilters {
    position: string;
    team: string;
    sort: string;
    maxPrice: number | null;
    page?: number;
}

const TransferMarket: FC<{
    paginatedPlayers: Paginated<TransferPlayer>;
    onPlayerClick: (player: TransferPlayer) => void;
    onPlayerInfoClick: (player: TransferPlayer) => void;
    isCreationMode?: boolean;
    selectedPlayerId: number | string | null;
    squad: TransferPlayer[];
    balance: number;
    filters: TransferFilters;
    setFilters: React.Dispatch<React.SetStateAction<TransferFilters>>;
    onFetchPlayers?: (filters?: any) => Promise<void>;
    isLoadingPlayers?: boolean;
    teams?: TeamOption[] | string[]; // All teams from current season (may include logos)
}> = ({
    paginatedPlayers,
    onPlayerClick,
    onPlayerInfoClick,
    selectedPlayerId,
    squad,
    balance,
    filters,
    setFilters,
    isCreationMode,
    onFetchPlayers,
    isLoadingPlayers = false,
    teams: allTeams = [],
}) => {
    const { t } = useTranslation();
    const { data: players = [] } = paginatedPlayers || {
        data: [],
    };

    const squadIds = useMemo(() => new Set(squad.map((p) => p.id)), [squad]);

    // Normalize teams for dropdown: [{ name, logo? }]
    // Logos should come from players' team_data.logo when available.
    const normalizedTeams: TeamOption[] = useMemo(() => {
        // Build a map of team => logo from current players
        const logoMap = new Map<string, string | null>();
        for (const p of players) {
            const teamName = p.team;
            const logo = p.team_data?.logo ?? null;
            if (!logoMap.has(teamName) && (logo && logo.length > 0)) {
                logoMap.set(teamName, logo);
            } else if (!logoMap.has(teamName)) {
                logoMap.set(teamName, null);
            }
        }

        if (allTeams && allTeams.length > 0) {
            // If items are strings, map using logoMap; if objects, prefer player-derived logo
            const arr = (allTeams as (TeamOption | string)[]).map((t) => {
                const name = typeof t === 'string' ? t : t.name;
                const backendLogo = typeof t === 'string' ? null : (t.logo ?? null);
                const derivedLogo = logoMap.get(name) ?? null;
                return { name, logo: derivedLogo ?? backendLogo } as TeamOption;
            });
            return arr.sort((a, b) => a.name.localeCompare(b.name));
        }

        // Fallback: use unique team names from players with their derived logos
        const names = Array.from(new Set(players.map((p) => p.team)));
        const arr = names.map((name) => ({ name, logo: logoMap.get(name) ?? null } as TeamOption));
        return arr.sort((a, b) => a.name.localeCompare(b.name));
    }, [allTeams, players]);

    const [isLoading, setIsLoading] = React.useState(false);
    
    // Use external loading state if provided, otherwise use internal state
    const currentlyLoading = isLoadingPlayers || isLoading;

    // Helper to build absolute logo URL from storage path
    const buildTeamLogoUrl = (teamLogoRaw?: string | null) => {
        const appUrl = (import.meta as any)?.env?.VITE_APP_URL || window.location.origin;
        return teamLogoRaw
            ? `${appUrl}/storage/${teamLogoRaw.replace(/^\/?(?:storage\/)?/, "")}`
            : "/images/base-team-logo.png";
    };

    const handleFilterChange = (newFilters: Partial<TransferFilters>) => {
        const updatedFilters = { ...filters, ...newFilters, page: 1 };
        setFilters(updatedFilters);
        if (onFetchPlayers) {
            onFetchPlayers(updatedFilters);
        }
    };

    const handlePageChange = (url: string | null) => {
        // Prevent multiple clicks while already loading
        if (url && !currentlyLoading) {
            setIsLoading(true);
            try {
                const page = new URL(url).searchParams.get("page");
                const newFilters = { ...filters, page: page ? parseInt(page, 10) : 1 };
                setFilters(newFilters);
                if (onFetchPlayers) {
                    onFetchPlayers(newFilters)
                        .catch((error) => {
                            console.error('Error fetching players:', error);
                        })
                        .finally(() => {
                            setIsLoading(false);
                        });
                } else {
                    // Fallback: just update filters and reset loading
                    setTimeout(() => setIsLoading(false), 100);
                }
            } catch (error) {
                console.error('Error parsing pagination URL:', error);
                setIsLoading(false);
            }
        }
    };

    useEffect(() => {
        setIsLoading(false);
    }, [paginatedPlayers]);
    return (
        <div className="transparent-card p-4 rounded-lg w-full lg:w-1/2">
            <h2 className="text-white text-xl font-bold mb-4 text-center">
                {t("transferManager.titles.transferMarket")}
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-xs">
                <Select
                    value={filters.position}
                    onValueChange={(value) =>
                        handleFilterChange({
                            position: value,
                        })
                    }
                >
                    <SelectTrigger className="bg-gray-700 text-white border-gray-600">
                        <SelectValue placeholder={t("transferManager.filters.allPositions")} />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">
                            {t("transferManager.filters.allPositions")}
                        </SelectItem>
                        <SelectItem value="GK">GK</SelectItem>
                        <SelectItem value="DEF">DEF</SelectItem>
                        <SelectItem value="MID">MID</SelectItem>
                        <SelectItem value="FWD">FWD</SelectItem>
                    </SelectContent>
                </Select>
                <Select
                    value={filters.team}
                    onValueChange={(value) =>
                        handleFilterChange({
                            team: value,
                        })
                    }
                >
                    <SelectTrigger className="bg-gray-700 text-white border-gray-600">
                        {/* Force text-only label in trigger to avoid rendering logos */}
                        <SelectValue asChild>
                            <span>
                                {filters.team && filters.team !== 'all'
                                    ? filters.team
                                    : t("transferManager.filters.allTeams")}
                            </span>
                        </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">
                            {t("transferManager.filters.allTeams")}
                        </SelectItem>
                        {normalizedTeams.map((team) => (
                            <SelectItem key={team.name} value={team.name}>
                                <span className="flex items-center gap-2">
                                    <img
                                        src={buildTeamLogoUrl(team.logo)}
                                        alt={`${team.name} logo`}
                                        className="w-4 h-4 rounded-sm object-contain bg-gray-800"
                                        onError={(e) => {
                                            const target = e.currentTarget as HTMLImageElement;
                                            if (target.src.indexOf("/images/base-team-logo.png") === -1) {
                                                target.src = "/images/base-team-logo.png";
                                            }
                                        }}
                                    />
                                    <span>{team.name}</span>
                                </span>
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                <input
                    type="number"
                    placeholder={t("transferManager.filters.maxPrice")}
                    value={filters.maxPrice ?? ""}
                    onChange={(e) =>
                        handleFilterChange({
                            maxPrice: e.target.value
                                ? parseFloat(e.target.value)
                                : null,
                        })
                    }
                    className="bg-gray-700 text-white p-2 rounded-md"
                />
                <button
                    onClick={() =>
                        handleFilterChange({
                            position: "all",
                            team: "all",
                            sort: "price_desc",
                            maxPrice: null,
                        })
                    }
                    className="bg-red-500 text-white p-2 rounded-md"
                >
                    {t("transferManager.actions.resetFilters")}
                </button>
            </div>
            <div className="overflow-y-auto h-fit">
                <table className="w-full text-left text-white text-xs">
                    <thead className="sticky top-0 bg-gray-900">
                        <tr>
                            <th className="p-2"></th>
                            <th className="p-2">
                                {t("transferManager.market.player")}
                            </th>
                            <th className="p-2">
                                {t("transferManager.market.team")}
                            </th>
                            <th className="p-2">
                                {t("transferManager.market.pos")}
                            </th>
                            <th className="p-2 text-right">
                                {t("transferManager.market.price")}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {players.map((p) => {
                            const isInSquad = squadIds.has(p.id);
                            const selectedSquadPlayer = squad.find(
                                (sp) => sp.id === selectedPlayerId
                            );
                            const isEligible = selectedSquadPlayer
                                ? validateTransfer(
                                      selectedSquadPlayer,
                                      p,
                                      squad,
                                      balance,
                                      t
                                  ).valid
                                : false;
                            return (
                                <MarketPlayer
                                    key={p.id}
                                    player={p}
                                    onPlayerClick={onPlayerClick}
                                    onPlayerInfoClick={onPlayerInfoClick}
                                    isEligible={isEligible}
                                    isInSquad={isInSquad}
                                />
                            );
                        })}
                    </tbody>
                </table>
            </div>
            <div className="flex justify-between items-center mt-4 text-xs">
                <button
                    onClick={() =>
                        handlePageChange(paginatedPlayers?.prev_page_url || null)
                    }
                    disabled={!paginatedPlayers?.prev_page_url || currentlyLoading || (paginatedPlayers?.current_page ?? 1) <= 1}
                    className={`${
                        currentlyLoading 
                            ? 'bg-gray-700 cursor-wait' 
                            : 'bg-gray-600 hover:bg-gray-500 cursor-pointer'
                    } text-white font-bold py-2 px-4 rounded transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:bg-gray-600 disabled:transform-none`}
                >
                    {currentlyLoading ? (
                        <span className="flex items-center gap-1">
                            <span className="animate-pulse">⏳</span>
                            Loading...
                        </span>
                    ) : (
                        t("pagination.previous")
                    )}
                </button>
                <span className="text-white">
                    {t("pagination.page", {
                        current: paginatedPlayers?.current_page ?? 1,
                        total: paginatedPlayers?.last_page ?? 1,
                    })}
                </span>
                <button
                    onClick={() =>
                        handlePageChange(paginatedPlayers?.next_page_url || null)
                    }
                    disabled={!paginatedPlayers?.next_page_url || currentlyLoading || (paginatedPlayers?.current_page ?? 1) >= (paginatedPlayers?.last_page ?? 1)}
                    className={`${
                        currentlyLoading 
                            ? 'bg-gray-700 cursor-wait' 
                            : 'bg-gray-600 hover:bg-gray-500 cursor-pointer'
                    } text-white font-bold py-2 px-4 rounded transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:bg-gray-600 disabled:transform-none`}
                >
                    {currentlyLoading ? (
                        <span className="flex items-center gap-1">
                            <span className="animate-pulse">⏳</span>
                            Loading...
                        </span>
                    ) : (
                        t("pagination.next")
                    )}
                </button>
            </div>
        </div>
    );
};

export default TransferMarket;

import { useTranslation } from "react-i18next";
import { TransferPlayer } from "@/types/fantasy";
import { FC, useEffect, useRef } from "react";

const ActionMenu: FC<{
    player: TransferPlayer;
    position: { top: number; left: number };
    onClose: () => void;
    onInitiateTransfer: (player: TransferPlayer) => void;
    onPlayerInfo: (player: TransferPlayer) => void;
}> = ({ player, position, onClose, onInitiateTransfer, onPlayerInfo }) => {
    const { t } = useTranslation();
    const menuRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                menuRef.current &&
                !menuRef.current.contains(event.target as Node)
            ) {
                onClose();
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [onClose]);

    return (
        <div
            ref={menuRef}
            className="absolute bg-white rounded-md shadow-lg z-20 border border-gray-200"
            style={{ top: position.top, left: position.left }}
        >
            <ul className="divide-y divide-gray-200">
                <li>
                    <button
                        onClick={() => onInitiateTransfer(player)}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                        {t("transferManager.actions.transfer")}
                    </button>
                </li>
                <li>
                    <button
                        onClick={() => onPlayerInfo(player)}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                        {t("transferManager.player.info")}
                    </button>
                </li>
            </ul>
        </div>
    );
};

export default ActionMenu;

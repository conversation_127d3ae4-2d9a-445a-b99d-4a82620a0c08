import React, { <PERSON> } from "react";
import { useTranslation } from "react-i18next";
import { TransferPlayer } from "@/types/fantasy";

const CaptainSelection: FC<{
    squad: TransferPlayer[];
    captain: TransferPlayer | null;
    viceCaptain: TransferPlayer | null;
    onCaptainSelect: (player: TransferPlayer) => void;
    onViceCaptainSelect: (player: TransferPlayer) => void;
}> = ({
    squad,
    captain,
    viceCaptain,
    onCaptainSelect,
    onViceCaptainSelect,
}) => {
    const { t } = useTranslation();
    const realPlayers = squad.filter((p) => typeof p.id === "number");

    if (realPlayers.length === 0) {
        return null;
    }

    return (
        <div className="bg-gray-800 rounded-lg p-4 mb-4">
            <h3 className="text-white text-lg font-bold mb-4">
                Select Captain & Vice-Captain
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Captain Selection */}
                <div>
                    <h4 className="text-white text-md font-semibold mb-2">
                        Captain (2x points)
                    </h4>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                        {realPlayers.map((player) => (
                            <button
                                key={`captain-${player.id}`}
                                onClick={() => onCaptainSelect(player)}
                                className={`w-full text-left p-2 rounded text-sm transition-colors ${
                                    captain?.id === player.id
                                        ? "bg-yellow-600 text-white"
                                        : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                                }`}
                            >
                                <div className="flex justify-between items-center">
                                    <span>{player.name}</span>
                                    <span className="text-xs">
                                        {player.position}
                                    </span>
                                </div>
                            </button>
                        ))}
                    </div>
                </div>

                {/* Vice-Captain Selection */}
                <div>
                    <h4 className="text-white text-md font-semibold mb-2">
                        Vice-Captain (1.5x points)
                    </h4>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                        {realPlayers.map((player) => (
                            <button
                                key={`vice-captain-${player.id}`}
                                onClick={() => onViceCaptainSelect(player)}
                                className={`w-full text-left p-2 rounded text-sm transition-colors ${
                                    viceCaptain?.id === player.id
                                        ? "bg-blue-600 text-white"
                                        : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                                }`}
                            >
                                <div className="flex justify-between items-center">
                                    <span>{player.name}</span>
                                    <span className="text-xs">
                                        {player.position}
                                    </span>
                                </div>
                            </button>
                        ))}
                    </div>
                </div>
            </div>

            {/* Current Selection Display */}
            <div className="mt-4 flex flex-col sm:flex-row gap-4 text-sm">
                <div className="text-white">
                    <span className="font-semibold">Captain:</span>{" "}
                    {captain ? captain.name : "None selected"}
                </div>
                <div className="text-white">
                    <span className="font-semibold">Vice-Captain:</span>{" "}
                    {viceCaptain ? viceCaptain.name : "None selected"}
                </div>
            </div>
        </div>
    );
};

export default CaptainSelection;
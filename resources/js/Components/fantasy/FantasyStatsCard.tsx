import React from "react";
import { useTranslation } from "react-i18next";
import StatsCard from "./StatsCard";

interface FantasyStatsCardProps {
    value: string | number;
    labelKey: string;
    className?: string;
    isLoading?: boolean;
    prefix?: string;
    suffix?: string;
}

export default function FantasyStatsCard({
    value,
    labelKey,
    className = "",
    isLoading = false,
    prefix = "",
    suffix = "",
}: FantasyStatsCardProps) {
    const { t } = useTranslation();

    const displayValue = isLoading ? "..." : `${prefix}${value}${suffix}`;
    const label = t(labelKey);

    return (
        <StatsCard
            value={displayValue}
            label={label}
            className={className}
        />
    );
}

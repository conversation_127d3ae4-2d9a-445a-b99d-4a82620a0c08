import React, { <PERSON> } from 'react';
import { Player } from '@/types/fantasy';
import InteractivePlayerComponent from './InteractivePlayerComponent';
import PointsPlayerComponent from '../PointsManager/PlayerComponent';

const PlayerComponent: FC<{
    player: Player;
    onPlayerClick?: (player: Player, element: HTMLElement) => void;
    onPlayerInfoClick?: (player: Player) => void;
    isSelected?: boolean;
    isEligible?: boolean;
}> = (props) => {
    if (props.onPlayerClick) {
        return <InteractivePlayerComponent {...props as any} />;
    }
    return <PointsPlayerComponent player={props.player} onPlayerClick={props.onPlayerClick} />;
};

export default PlayerComponent;

import { useTranslation } from "react-i18next";
import { Player } from "@/types/fantasy";
import React, { FC, useRef } from "react";
import PlayerShirtComponent from "../common/PlayerShirtComponent";

const PlayerComponent: FC<{
    player: Player;
    onPlayerClick: (player: Player, element: HTMLElement) => void;
    onPlayerInfoClick: (player: Player) => void;
    isEligible: boolean;
    isSelected: boolean;
}> = ({ player, onPlayerClick, onPlayerInfoClick, isEligible, isSelected }) => {
    const playerRef = useRef<HTMLDivElement>(null);
    const { t } = useTranslation();

    const handleClick = () => {
        if (playerRef.current) {
            onPlayerClick(player, playerRef.current);
        }
    };

    const baseClasses =
        "w-20 h-20 flex flex-col items-center justify-center rounded-md cursor-pointer transition-all duration-200 ease-in-out transform hover:scale-105";
    const eligibilityClasses = isSelected
        ? "bg-white/20 shadow-lg"
        : isEligible
        ? "bg-white/50 animate-pulse"
        : "bg-white/0";

    return (
        <div
            ref={playerRef}
            className={`${baseClasses} ${eligibilityClasses}`}
            style={
                player.onPitch && player.pitchPosition
                    ? {
                          position: "absolute",
                          left: `${player.pitchPosition.x}%`,
                          top: `${player.pitchPosition.y}%`,
                          transform: "translate(-50%, -50%)",
                      }
                    : {}
            }
            onClick={handleClick}
        >
            <PlayerShirtComponent player={player} />
            {/* player name overflow ellipsis ... */}
            <div className="flex flex-col items-center justify-center bg-white/20 w-full text-center p-1">
                <p className="text-xs text-gray-800 truncate w-full">
                    {player.name}
                </p>
            </div>
            {/* <div className="">
                <p className="text-xs text-gray-800">
                    {t("myTeam.price")} {player.price.toFixed(1)}
                </p>
            </div> */}
            {(player.is_captain || player.is_vice_captain) && (
                <div className="absolute -top-2 -right-2 bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center font-bold text-sm">
                    {player.is_captain ? "C" : "V"}
                </div>
            )}
        </div>
    );
};

export default PlayerComponent;

import React, { <PERSON> } from "react";

const MessageBox: FC<{ message: string; type: "info" | "error" }> = ({
    message,
    type,
}) => {
    const baseClasses = "p-2 text-xs font-semibold";
    const typeClasses = {
        info: "text-blue-100",
        error: "text-red-300",
    };
    return (
        <div className={`${baseClasses} ${typeClasses[type]}`}>{message}</div>
    );
};

export default MessageBox;

import React, { FC, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { Player, TransferPlayer } from "@/types/fantasy";
import { useBackgroundImage } from "@/hooks/useTenant";
import axios from "axios";
import Spinner from "@/Components/ui/spinner";
import {
    CalendarDays,
    Trophy,
    Shield,
    ShieldCheck,
    ShieldX,
    AlertTriangle,
    Frown,
    Hand,
    Flag,
    Square,
    Target,
    Activity,
    BookOpen,
    Zap,
} from "lucide-react";

const PlayerInfoModal: FC<{
    player: Player | TransferPlayer | null;
    onClose: () => void;
    context: "myTeam" | "transferManager";
}> = ({ player, onClose, context }) => {
    // Call all hooks unconditionally to respect Rules of Hooks
    const { t, i18n } = useTranslation();
    const tenantBgRaw = useBackgroundImage();

    // Player performance state (only aggregated stats are needed)
    const [isLoadingPerf, setIsLoadingPerf] = useState(false);
    const [stats, setStats] = useState<any | null>(null);
    const [perfError, setPerfError] = useState<string | null>(null);

    useEffect(() => {
        if (!player) return;
        const playerId = (player as any)?.id ?? (player as any)?.player_id;
        if (!playerId) {
            console.warn("PlayerInfoModal: missing player id for performance fetch", player);
            return;
        }

        let cancelled = false;
        const fetchPerf = async () => {
            setIsLoadingPerf(true);
            setPerfError(null);
            try {
                const url = `/players/${playerId}/performances`;
                console.log("PlayerInfoModal: fetching performances", { url, locale: i18n.language });
                const res = await axios.get(url, {
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                        Accept: "application/json",
                    },
                    params: { locale: i18n.language },
                });
                console.log("this is the player performance : ", res);
                if (!cancelled) {
                    setStats(res.data?.data?.stats ?? null);
                }
            } catch (e: any) {
                if (!cancelled) setPerfError(e?.response?.data?.error || "Failed to load performances");
            } finally {
                if (!cancelled) setIsLoadingPerf(false);
            }
        };
        fetchPerf();
        return () => {
            cancelled = true;
        };
    }, [(player as any)?.id ?? (player as any)?.player_id, i18n.language]);

    // Determine if player is a Goalkeeper (supports several shapes of position)
    const isGoalkeeper = useMemo(() => {
        const pos: any = (player as any)?.position;
        return pos === "GK" || pos?.value === "GK" || pos?.toString?.() === "GK";
    }, [player]);

    // After hooks: if no player, render nothing
    if (!player) return null;

    const isMyTeamPlayer = (p: any): p is Player => context === "myTeam";

    // Prefer Arabic name when current language is Arabic and name_ar is available
    const displayName =
        i18n.language === "ar" && (player as any).name_ar
            ? (player as any).name_ar
            : player.name;

    // Build image URL as: `${appUrl}/storage/<player.image>` with fallback
    const rawImage = player.image as string | undefined;
    const appUrl = typeof window !== "undefined" ? window.location.origin : "";
    const displayImage = rawImage
        ? `${appUrl}/storage/${rawImage.replace(/^\/?(?:storage\/)?/, "")}`
        : "/images/base-football-player.png";

    // Tenant background image for header
    const tenantBgImage = tenantBgRaw
        ? `${appUrl}/storage/${tenantBgRaw.replace(/^\/?(?:storage\/)?/, "")}`
        : null
    // Compute age from birthday (expects formats like 'YYYY-MM-DD' or 'YYYY')
    const birthday: string | undefined = ("birthday" in player && (player as any).birthday)
        ? ((player as any).birthday as string)
        : undefined;
    const currentYear = new Date().getFullYear();
    const getAgeFromBirthday = (b?: string): number | null => {
        if (!b) return null;
        const match = b.match(/^(\d{4})/);
        if (!match) return null;
        const birthYear = parseInt(match[1], 10);
        if (!Number.isFinite(birthYear)) return null;
        return currentYear - birthYear;
    };
    const age = getAgeFromBirthday(birthday);


    return (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex justify-center items-end sm:items-center z-50 p-4">
            <div
                className={`w-full max-w-xl rounded-2xl shadow-2xl overflow-hidden bg-white ${isMyTeamPlayer(player) ? "text-gray-800" : ""
                    }`}
            >
                {/* Header */}
                <div className="relative">
                    <div
                        className="h-28 bg-cover bg-center relative"
                        style={{
                            backgroundImage: tenantBgImage
                                ? `url(${tenantBgImage})`
                                : `linear-gradient(to right, rgba(99,102,241,0.8), rgba(168,85,247,0.8), rgba(236,72,153,0.8))`,
                        }}
                    />
                    <div className="absolute -bottom-10 left-6 flex items-end gap-4">
                        <img
                            src={displayImage}
                            alt={displayName}
                            className="h-24 w-24 rounded-2xl object-cover ring-4 ring-white shadow-lg "
                        />
                        <div className="pb-2">
                            <div className="text-xs uppercase tracking-wide text-white/90">
                                {isMyTeamPlayer(player)
                                    ? t("myTeam.playerActions")
                                    : t(`${context}.playerInfo.position`) + " " + player.position}
                            </div>
                            <h2 className="text-2xl sm:text-3xl font-extrabold text-white/90 drop-shadow">
                                {displayName}
                            </h2>
                            {/* Team line: normalize to always render consistently */}
                            {!isMyTeamPlayer(player) && (
                                (() => {
                                    const teamName: string =
                                        ("team" in player && (player as any).team)
                                            ? String((player as any).team)
                                            : "-";
                                    const teamLogoRaw =
                                        ("team_data" in player && (player as any).team_data?.logo)
                                            ? (player as any).team_data.logo as string
                                            : undefined;
                                    const teamLogo = teamLogoRaw
                                        ? `${appUrl}/storage/${teamLogoRaw.replace(/^\/?(?:storage\/)?/, "")}`
                                        : "/images/base-team-logo.png";
                                    return (
                                        <div className="text-black/90 text-sm inline-flex items-center gap-2">
                                            <img
                                                src={teamLogo}
                                                alt={`${teamName} logo`}
                                                className="h-5 w-5 rounded object-contain bg-white/70 p-0.5"
                                                onError={(e) => {
                                                    const target = e.currentTarget as HTMLImageElement;
                                                    if (target.src.indexOf("/images/base-team-logo.png") === -1) {
                                                        target.src = "/images/base-team-logo.png";
                                                    }
                                                }}
                                            />
                                            <span>{teamName}</span>
                                        </div>
                                    );
                                })()
                            )}
                        </div>
                    </div>
                </div>

                {/* Body */}
                <div className="pt-14 px-6 pb-6">
                    {/* Quick stats grid */}
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-6">
                        <div className="rounded-xl border p-3 text-center">
                            <div className="text-xs text-gray-500">{t(`${context}.playerInfo.price`)}</div>
                            <div className="text-lg font-semibold">{player.price.toFixed(1)}</div>
                        </div>
                        <div className="rounded-xl border p-3 text-center">
                            <div className="text-xs text-gray-500">{t(`${context}.playerInfo.position`)}</div>
                            <div className="text-lg font-semibold">{player.position}</div>
                        </div>
                        <div className="rounded-xl border p-3 text-center">
                            <div className="text-xs text-gray-500">{t(`${context}.playerInfo.country`)}</div>
                            <div className="text-lg font-semibold">{("country" in player && (player as any).country) ? (player as any).country as string : "-"}</div>
                        </div>
                        <div className="rounded-xl border p-3 text-center">
                            <div className="text-xs text-gray-500">{t(`${context}.playerInfo.age`)}</div>
                            <div className="text-lg font-semibold">{age !== null ? age : "-"}</div>
                        </div>
                    </div>

                    {/* Enhanced Performance Section */}
                    <div className="mb-4">
                        <div className="flex items-center gap-2 mb-3">
                            <Activity className="w-5 h-5 text-blue-600" />
                            <div className="text-base font-semibold text-gray-800">
                                {t("myTeam.playerInfo.performance") || "Season Performance"}
                            </div>
                        </div>
                        
                        <div className="rounded-xl border border-gray-200 bg-gradient-to-br from-gray-50 to-white overflow-hidden">
                            {isLoadingPerf && (
                                <div className="p-6 flex items-center justify-center">
                                    <Spinner size={2} label={t("loading") || "Loading stats..."} />
                                </div>
                            )}
                            
                            {!isLoadingPerf && perfError && (
                                <div className="p-6 text-center">
                                    <div className="text-red-600 text-sm flex items-center justify-center gap-2">
                                        <AlertTriangle className="w-4 h-4" />
                                        {perfError}
                                    </div>
                                </div>
                            )}
                            
                            {!isLoadingPerf && !perfError && !stats && (
                                <div className="p-6 text-center">
                                    <div className="text-gray-600 text-sm flex items-center justify-center gap-2">
                                        <Frown className="w-5 h-5" />
                                        {t("myTeam.playerInfo.noPerformance") || "No performances yet this season."}
                                    </div>
                                </div>
                            )}
                            
                            {!isLoadingPerf && !perfError && stats && (
                                <div className="p-4">
                                    {/* Stats Grid - 4 columns */}
                                    <div className="grid grid-cols-4 gap-3">
                                        {/* Games Played - Always first */}
                                        <div className="text-center p-3 rounded-lg bg-white border border-gray-100 hover:border-blue-200 transition-colors">
                                            <div className="flex justify-center mb-2">
                                                <div className="p-2 rounded-lg bg-blue-100">
                                                    <CalendarDays className="w-4 h-4 text-blue-600" />
                                                </div>
                                            </div>
                                            <div className="text-lg font-bold text-gray-900 mb-1">{stats.games_played ?? 0}</div>
                                            <div className="text-xs font-medium text-gray-600">
                                                {t("stats.gamesPlayed") || "Games"}
                                            </div>
                                        </div>

                                        {/* Position-specific stats */}
                                        {!isGoalkeeper && (
                                            <>
                                                <div className="text-center p-3 rounded-lg bg-white border border-gray-100 hover:border-green-200 transition-colors">
                                                    <div className="flex justify-center mb-2">
                                                        <div className="p-2 rounded-lg bg-green-100">
                                                            <Trophy className="w-4 h-4 text-green-600" />
                                                        </div>
                                                    </div>
                                                    <div className="text-lg font-bold text-gray-900 mb-1">{stats.goals_scored ?? 0}</div>
                                                    <div className="text-xs font-medium text-gray-600">
                                                        {t("stats.goals") || "Goals"}
                                                    </div>
                                                </div>
                                                
                                                <div className="text-center p-3 rounded-lg bg-white border border-gray-100 hover:border-red-200 transition-colors">
                                                    <div className="flex justify-center mb-2">
                                                        <div className="p-2 rounded-lg bg-red-100">
                                                            <Target className="w-4 h-4 text-red-600" />
                                                        </div>
                                                    </div>
                                                    <div className="text-lg font-bold text-gray-900 mb-1">{stats.penalties_missed ?? 0}</div>
                                                    <div className="text-xs font-medium text-gray-600">
                                                        {t("stats.penaltiesMissed") || "Pens Missed"}
                                                    </div>
                                                </div>
                                            </>
                                        )}

                                        {/* Goalkeeper specific stats */}
                                        {isGoalkeeper && (
                                            <>
                                                <div className="text-center p-3 rounded-lg bg-white border border-gray-100 hover:border-red-200 transition-colors">
                                                    <div className="flex justify-center mb-2">
                                                        <div className="p-2 rounded-lg bg-red-100">
                                                            <ShieldX className="w-4 h-4 text-red-600" />
                                                        </div>
                                                    </div>
                                                    <div className="text-lg font-bold text-gray-900 mb-1">{stats.goals_conceded ?? 0}</div>
                                                    <div className="text-xs font-medium text-gray-600">
                                                        {t("stats.goalsConceded") || "Goals Against"}
                                                    </div>
                                                </div>
                                                
                                                <div className="text-center p-3 rounded-lg bg-white border border-gray-100 hover:border-emerald-200 transition-colors">
                                                    <div className="flex justify-center mb-2">
                                                        <div className="p-2 rounded-lg bg-emerald-100">
                                                            <ShieldCheck className="w-4 h-4 text-emerald-600" />
                                                        </div>
                                                    </div>
                                                    <div className="text-lg font-bold text-gray-900 mb-1">{stats.penalties_saved ?? 0}</div>
                                                    <div className="text-xs font-medium text-gray-600">
                                                        {t("stats.penaltiesSaved") || "Pens Saved"}
                                                    </div>
                                                </div>
                                                
                                                <div className="text-center p-3 rounded-lg bg-white border border-gray-100 hover:border-blue-200 transition-colors">
                                                    <div className="flex justify-center mb-2">
                                                        <div className="p-2 rounded-lg bg-blue-100">
                                                            <Shield className="w-4 h-4 text-blue-600" />
                                                        </div>
                                                    </div>
                                                    <div className="text-lg font-bold text-gray-900 mb-1">{stats.saves ?? 0}</div>
                                                    <div className="text-xs font-medium text-gray-600">
                                                        {t("stats.saves") || "Saves"}
                                                    </div>
                                                </div>
                                            </>
                                        )}

                                        {/* Common disciplinary stats */}
                                        <div className="text-center p-3 rounded-lg bg-white border border-gray-100 hover:border-yellow-200 transition-colors">
                                            <div className="flex justify-center mb-2">
                                                <div className="p-2 rounded-lg bg-yellow-100">
                                                    <BookOpen className="w-4 h-4 text-yellow-600" />
                                                </div>
                                            </div>
                                            <div className="text-lg font-bold text-gray-900 mb-1">{stats.yellow_cards ?? 0}</div>
                                            <div className="text-xs font-medium text-gray-600">
                                                {t("stats.yellowCards") || "Yellow Cards"}
                                            </div>
                                        </div>
                                        
                                        <div className="text-center p-3 rounded-lg bg-white border border-gray-100 hover:border-red-200 transition-colors">
                                            <div className="flex justify-center mb-2">
                                                <div className="p-2 rounded-lg bg-red-100">
                                                    <Square className="w-4 h-4 text-red-600" />
                                                </div>
                                            </div>
                                            <div className="text-lg font-bold text-gray-900 mb-1">{stats.red_cards ?? 0}</div>
                                            <div className="text-xs font-medium text-gray-600">
                                                {t("stats.redCards") || "Red Cards"}
                                            </div>
                                        </div>

                                        <div className="text-center p-3 rounded-lg bg-white border border-gray-100 hover:border-orange-200 transition-colors">
                                            <div className="flex justify-center mb-2">
                                                <div className="p-2 rounded-lg bg-orange-100">
                                                    <Zap className="w-4 h-4 text-orange-600" />
                                                </div>
                                            </div>
                                            <div className="text-lg font-bold text-gray-900 mb-1">{stats.own_goals ?? 0}</div>
                                            <div className="text-xs font-medium text-gray-600">
                                                {t("stats.ownGoals") || "Own Goals"}
                                            </div>
                                        </div>
                                        
                                        <div className="text-center p-3 rounded-lg bg-white border border-gray-100 hover:border-purple-200 transition-colors">
                                            <div className="flex justify-center mb-2">
                                                <div className="p-2 rounded-lg bg-purple-100">
                                                    <Hand className="w-4 h-4 text-purple-600" />
                                                </div>
                                            </div>
                                            <div className="text-lg font-bold text-gray-900 mb-1">{stats.penalties_committed ?? 0}</div>
                                            <div className="text-xs font-medium text-gray-600">
                                                {t("stats.penaltiesCommitted") || "Pens Given"}
                                            </div>
                                        </div>
                                        
                                        <div className="text-center p-3 rounded-lg bg-white border border-gray-100 hover:border-indigo-200 transition-colors">
                                            <div className="flex justify-center mb-2">
                                                <div className="p-2 rounded-lg bg-indigo-100">
                                                    <Flag className="w-4 h-4 text-indigo-600" />
                                                </div>
                                            </div>
                                            <div className="text-lg font-bold text-gray-900 mb-1">{stats.penalties_caused ?? 0}</div>
                                            <div className="text-xs font-medium text-gray-600">
                                                {t("stats.penaltiesCaused") || "Pens Won"}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                        
                        {/* Loading state moved inside the performance card above to avoid duplicate loaders */}

                        {/* Placeholder for extra stats/fixtures (only when no stats and not loading) */}
                        {!isLoadingPerf && !stats && (
                            <div className="rounded-xl bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-200 p-4 text-sm text-gray-600 mt-3 flex items-center gap-3">
                                <div className="p-2 rounded-lg bg-white">
                                    <Frown className="w-5 h-5 text-gray-500" />
                                </div>
                                <div>
                                    <div className="font-medium text-gray-700 mb-1">No Data Available</div>
                                    <div className="text-xs text-gray-500">
                                        {t(`${context}.playerInfo.statsPlaceholder`) || "Player statistics will appear here once they start playing matches this season."}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Footer actions */}
                    <div className="mt-6 flex gap-3">
                        <button
                            onClick={onClose}
                            className="w-full inline-flex justify-center items-center rounded-xl border border-gray-300 bg-white px-4 py-2 font-semibold text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                            {t(`${context}.actions.close`) || "Close"}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PlayerInfoModal;
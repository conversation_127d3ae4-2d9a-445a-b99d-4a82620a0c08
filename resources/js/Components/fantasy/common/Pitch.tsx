import { useTranslation } from "react-i18next";
import { Player } from "@/types/fantasy";
import React, { FC } from "react";
import PlayerComponent from "./PlayerComponent";

const Pitch: FC<{
    players: Player[];
    onPlayerClick?: (player: Player, element: HTMLElement) => void;
    onPlayerInfoClick?: (player: Player) => void;
    selectedPlayerId?: number | null;
    eligiblePlayerIds?: number[] | null;
}> = ({
    players,
    onPlayerClick,
    onPlayerInfoClick,
    selectedPlayerId,
    eligiblePlayerIds,
}) => {
    const { t } = useTranslation();
    const pitchPlayers = players.filter((p) => p.onPitch);

    return (
        <div className="fantasy-pitch">
            {pitchPlayers.map((player) => (
                <PlayerComponent
                    key={player.id}
                    player={player}
                    onPlayerClick={onPlayerClick}
                    onPlayerInfoClick={onPlayerInfoClick}
                    isSelected={selectedPlayerId === player.id}
                    isEligible={eligiblePlayerIds?.includes(player.id) ?? false}
                />
            ))}
        </div>
    );
};

export default Pitch;

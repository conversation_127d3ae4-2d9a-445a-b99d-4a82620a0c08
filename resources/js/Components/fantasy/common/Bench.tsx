import { useTranslation } from "react-i18next";
import { Player } from "@/types/fantasy";
import React, { FC } from "react";
import PlayerComponent from "./PlayerComponent";

const Bench: FC<{
    players: Player[];
    onPlayerClick?: (player: Player, element: HTMLElement) => void;
    onPlayerInfoClick?: (player: Player) => void;
    selectedPlayerId?: number | null;
    eligiblePlayerIds?: number[] | null;
}> = ({
    players,
    onPlayerClick,
    onPlayerInfoClick,
    selectedPlayerId,
    eligiblePlayerIds,
}) => {
    const { t } = useTranslation();
    const benchPlayers = players
        .filter((p) => !p.onPitch)
        .sort((a, b) => {
            if (a.position === "GK") return -1;
            if (b.position === "GK") return 1;
            return 0;
        });

    return (
        <div className="bg-gray-200/20 p-4 rounded-lg space-y-4 flex flex-col fantasy-bench">
            <h2 className="text-white text-center font-semibold">
                {t("myTeam.bench")}
            </h2>
            <div className="flex justify-center items-center space-x-4 mt-4">
                {benchPlayers.map((player) => (
                    <div
                        key={player.id + "bench"}
                        className={`flex flex-col items-center h-28 ${
                            player.position === "GK" ? "mr-12" : ""
                        }`}
                    >
                        <PlayerComponent
                            key={player.id}
                            player={player}
                            onPlayerClick={onPlayerClick}
                            onPlayerInfoClick={onPlayerInfoClick}
                            isSelected={selectedPlayerId === player.id}
                            isEligible={
                                eligiblePlayerIds?.includes(player.id) ?? false
                            }
                        />
                    </div>
                ))}
            </div>
        </div>
    );
};

export default Bench;

import React, { useEffect, useState } from "react";
import shirtSvgRaw from "@/Components/fantasy/common/shirt-01.svg?raw";

interface PlayerShirtProps {
    player: {
        team_data: {
            logo: string;
            shirt: {
                base_color: string;
                pattern_type: string;
                pattern_color: string;
            };
        };
    };
}

const PlayerShirtComponent: React.FC<PlayerShirtProps> = ({ player }) => {
    const [shirtSvg, setShirtSvg] = useState<string>("");

    useEffect(() => {
        setShirtSvg(shirtSvgRaw);
    }, []);

    useEffect(() => {
        if (shirtSvg && player?.team_data?.shirt) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(shirtSvg, "image/svg+xml");

            const { base_color, pattern_type, pattern_color } =
                player.team_data.shirt;
            const { logo } = player.team_data;

            // Update base color
            const base = doc.querySelector("#base-color");
            if (base && base_color) {
                base.setAttribute("fill", base_color);
                base.querySelectorAll("path").forEach((p) =>
                    p.setAttribute("style", `fill: ${base_color}`)
                );
            }

            // Update logo
            const logoElement = doc.querySelector("#club_logo image");
            if (logoElement && logo) {
                logoElement.setAttribute("xlink:href", `/storage/${logo}`); // Assuming logo path is relative to storage
            }

            // Hide all secondary patterns at once
            const allPatternParts = doc.querySelectorAll(
                `[id^="secondary_pattern_"]`
            );
            allPatternParts.forEach(
                (part) => ((part as HTMLElement).style.display = "none")
            );

            // Show and color the selected pattern
            if (pattern_type) {
                const patternParts = doc.querySelectorAll(
                    `[id^="${pattern_type}"]`
                );
                patternParts.forEach((part) => {
                    (part as HTMLElement).style.display = "block";
                    if (pattern_color) {
                        part.setAttribute("fill", pattern_color);
                        part.querySelectorAll("path").forEach((p) =>
                            p.setAttribute("style", `fill: ${pattern_color}`)
                        );
                    }
                });
            }

            setShirtSvg(
                new XMLSerializer().serializeToString(doc.documentElement)
            );
        }
    }, [shirtSvg, player]);

    return (
        <div
            className="player-shirt-container"
            dangerouslySetInnerHTML={{ __html: shirtSvg }}
        />
    );
};

export default PlayerShirtComponent;

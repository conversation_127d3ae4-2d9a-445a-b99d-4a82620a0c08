import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { PointsManagerProps, Player } from "@/types/fantasy";
import {
    initializeTeam,
    applyFormation,
    getFormationKey,
} from "@/lib/fantasy/team-management";

import Pitch from "../common/Pitch";
import Bench from "../common/Bench";
import FixturesList from "../FixturesList";

interface Fixture {
    id: number;
    gameweek_id: number;
    homeTeam: any;
    awayTeam: any;
    game_date: string;
    home_score?: number | null;
    away_score?: number | null;
    status: "scheduled" | "in_progress" | "finished" | "postponed" | "canceled";
    stadium?: {
        id: number;
        name: string;
    };
    referee?: {
        id: number;
        name: string;
    };
}

interface PointsManagerWithFixturesProps extends PointsManagerProps {
    fixtures?: Fixture[];
    onPlayerClick?: (playerId: number) => void;
}

export default function PointsManager({
    squadPlayers,
    startingPlayerIds,
    fantasyTeam,
    fixtures = [],
    onPlayerClick,
}: PointsManagerWithFixturesProps) {
    const { t } = useTranslation();

    const initialSquad = useMemo(
        () =>
            squadPlayers.map((player) => ({
                id: player.id,
                name: player.name,
                position: player.position,
                team: player.team,
                price: player.price, // price is not displayed but good to have
                points: player.points,
                is_captain: false, // not relevant for points view
                is_vice_captain: false, // not relevant for points view
                onPitch: false,
                pitchPosition: null,
                team_data: player.team_data,
            })),
        [squadPlayers]
    );

    const players = useMemo(
        () => initializeTeam(initialSquad, startingPlayerIds, t),
        [initialSquad, startingPlayerIds, t]
    );

    return (
        <div className="flex gap-4">
            <div className="transparent-card min-h-screen rounded-lg p-4 sm:p-8 font-sans pb-24 w-full lg:w-2/3">
                <div className="max-w-5xl mx-auto">
                    <Pitch
                        players={players}
                        onPlayerClick={onPlayerClick ? (player) => onPlayerClick(typeof player.id === 'string' ? parseInt(player.id) : player.id) : undefined}
                    />
                    <Bench
                        players={players}
                        onPlayerClick={onPlayerClick ? (player) => onPlayerClick(typeof player.id === 'string' ? parseInt(player.id) : player.id) : undefined}
                    />
                </div>
            </div>
            <div className="transparent-card min-h-screen rounded-lg p-4 sm:p-8 font-sans pb-24 w-full lg:w-1/3">
                <h3 className="text-center mb-4">GameWeek Fixtures</h3>
                <FixturesList fixtures={fixtures} />
            </div>
        </div>
    );
}

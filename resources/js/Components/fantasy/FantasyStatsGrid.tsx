
import { FantasyStats } from "@/types/fantasy";
import FantasyStatsCard from "./FantasyStatsCard";

interface FantasyStatsGridProps {
    stats: FantasyStats | null;
    isLoading?: boolean;
}

export default function FantasyStatsGrid({
    stats,
    isLoading = false,
}: FantasyStatsGridProps) {
    const formatRank = (rank: number | null): string => {
        if (rank === null) return "N/A";
        return `#${rank}`;
    };

    return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <FantasyStatsCard
                value={stats?.gameweek_points ?? 0}
                labelKey="pointsStats.yourPoints"
                className="col-span-full lg:col-span-1"
                isLoading={isLoading}
            />
            <FantasyStatsCard
                value={stats?.transfers_used ?? 0}
                labelKey="pointsStats.transfersUsed"
                isLoading={isLoading}
            />
            <FantasyStatsCard
                value={formatRank(stats?.gameweek_rank ?? null)}
                labelKey="pointsStats.gwRank"
                isLoading={isLoading}
            />
            <FantasyStatsCard
                value={stats?.highest_points ?? 0}
                labelKey="pointsStats.highestPoints"
                isLoading={isLoading}
            />
            <FantasyStatsCard
                value={stats?.average_points ?? 0}
                labelKey="pointsStats.averagePoints"
                isLoading={isLoading}
            />
        </div>
    );
}


import { PlayerPerformanceData, PointsBreakdownItem } from "@/types/fantasy";
import { X, Clock, Target, Shield, Zap, TrendingUp, Star, Trophy, Minus } from "lucide-react";
import FixtureItem from "./FixtureItem";
import { useTranslation } from "react-i18next";

interface PlayerPerformanceModalProps {
    playerId: number | null;
    isOpen: boolean;
    onClose: () => void;
    playerPerformances: Record<number, PlayerPerformanceData>;
}

export default function PlayerPerformanceModal({
    playerId,
    isOpen,
    onClose,
    playerPerformances,
}: PlayerPerformanceModalProps) {
    const { t } = useTranslation();

    // Get performance data from server-side props
    const performanceData = playerId ? playerPerformances[playerId] || null : null;
    const hasError = playerId && !performanceData;

    const getPointsColor = (points: number): string => {
        if (points > 0) return "text-green-600";
        if (points < 0) return "text-red-600";
        return "text-gray-600";
    };

    // Custom card components
    const YellowCard = () => (
        <div className="w-4 h-6 bg-yellow-400 border border-yellow-500 rounded-sm shadow-sm"></div>
    );

    const RedCard = () => (
        <div className="w-4 h-6 bg-red-500 border border-red-600 rounded-sm shadow-sm"></div>
    );

    const Football = () => (
        <div className="w-4 h-4 bg-white border-2 border-gray-800 rounded-full relative">
            <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-1 h-1 bg-gray-800 rounded-full"></div>
            </div>
        </div>
    );

    const getActionIcon = (action: string) => {
        const actionLower = action.toLowerCase();

        if (actionLower.includes("goal") && !actionLower.includes("own")) {
            return <Football />;
        }
        if (actionLower.includes("assist")) {
            return <Target className="w-4 h-4 text-blue-500" />;
        }
        if (actionLower.includes("yellow card")) {
            return <YellowCard />;
        }
        if (actionLower.includes("red card")) {
            return <RedCard />;
        }
        if (actionLower.includes("clean sheet")) {
            return <Shield className="w-4 h-4 text-green-500" />;
        }
        if (actionLower.includes("penalty saved")) {
            return <Zap className="w-4 h-4 text-purple-500" />;
        }
        if (actionLower.includes("penalty") && actionLower.includes("missed")) {
            return <Minus className="w-4 h-4 text-red-500" />;
        }
        if (actionLower.includes("hat-trick")) {
            return <Trophy className="w-4 h-4 text-yellow-500" />;
        }
        if (actionLower.includes("own goal")) {
            return <TrendingUp className="w-4 h-4 text-red-500 rotate-180" />;
        }
        if (actionLower.includes("minute")) {
            return <Clock className="w-4 h-4 text-gray-500" />;
        }
        return <Star className="w-4 h-4 text-gray-500" />;
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
            <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                {/* Header */}
                <div className="flex justify-between items-center p-6 border-b">
                    <h2 className="text-xl font-bold text-gray-800">
                        {performanceData?.player.name || t("playerPerformance.title")}
                    </h2>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700 transition-colors"
                    >
                        <X className="w-6 h-6" />
                    </button>
                </div>

                {/* Content */}
                <div className="p-6">
                    {hasError && (
                        <div className="text-center py-8">
                            <div className="text-gray-600 mb-4">
                                <div className="text-lg font-semibold mb-2">{t("playerPerformance.noData.title")}</div>
                                <div className="text-sm">
                                    {t("playerPerformance.noData.message")}
                                </div>
                            </div>
                        </div>
                    )}

                    {performanceData && (
                        <div className="space-y-6">
                            {/* Game Result using FixtureItem */}
                            <div>
                                <FixtureItem
                                    homeTeam={performanceData.game.home_team}
                                    awayTeam={performanceData.game.away_team}
                                    kickoff={performanceData.game.game_date || ""}
                                    homeScore={performanceData.game.home_score}
                                    awayScore={performanceData.game.away_score}
                                    status={performanceData.game.status as any}
                                />

                                {/* Player's team indicator */}
                                <div className="text-center mt-2 text-sm text-gray-600">
                                    {t("playerPerformance.playedFor")} <span className="font-semibold">{performanceData.team_played_for.name}</span>
                                </div>
                            </div>

                            {/* Fantasy Points Summary */}
                            <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white shadow-lg">
                                <div className="text-center">
                                    <div className="flex items-center justify-center mb-2">
                                        <Trophy className="w-6 h-6 text-yellow-300 mr-2" />
                                        <span className="text-lg font-semibold">{t("playerPerformance.pointsBreakdown.total")}</span>
                                    </div>
                                    <div className="text-4xl font-bold mb-1">
                                        {performanceData.fantasy_points.total}
                                    </div>
                                    <div className="text-blue-100 text-sm">Fantasy Points</div>
                                </div>
                            </div>

                            {/* Points Breakdown */}
                            <div>
                                <h3 className="text-lg font-semibold mb-4">{t("playerPerformance.pointsBreakdown.title")}</h3>
                                <div className="space-y-3">
                                    {performanceData.fantasy_points.breakdown.map((item: PointsBreakdownItem, index: number) => {
                                        const totalPoints = item.points * item.count;
                                        const isPositive = totalPoints > 0;
                                        const isNegative = totalPoints < 0;

                                        return (
                                            <div
                                                key={index}
                                                className={`flex items-center justify-between p-4 rounded-lg border-l-4 transition-all duration-200 hover:shadow-md ${isPositive
                                                    ? 'bg-green-50 border-l-green-500 hover:bg-green-100'
                                                    : isNegative
                                                        ? 'bg-red-50 border-l-red-500 hover:bg-red-100'
                                                        : 'bg-gray-50 border-l-gray-400 hover:bg-gray-100'
                                                    }`}
                                            >
                                                <div className="flex items-center space-x-3">
                                                    <div className="flex-shrink-0">
                                                        {getActionIcon(item.action)}
                                                    </div>
                                                    <div>
                                                        <span className="font-medium text-gray-900">{item.action}</span>
                                                        {item.count > 1 && (
                                                            <span className="ml-2 px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded-full">
                                                                ×{item.count}
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className={`font-bold text-lg ${getPointsColor(totalPoints)}`}>
                                                    {isPositive ? "+" : ""}{totalPoints}
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>

                            {/* Performance Stats */}
                            <div>
                                <h3 className="text-lg font-semibold mb-4">{t("playerPerformance.performance.title")}</h3>
                                <div className="grid grid-cols-2 gap-4">
                                    {/* Minutes Played - Always show */}
                                    <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-4 border border-gray-200">
                                        <div className="flex items-center justify-between mb-2">
                                            <div className="text-sm text-gray-600">{t("playerPerformance.performance.minutesPlayed")}</div>
                                            <Clock className="w-4 h-4 text-gray-500" />
                                        </div>
                                        <div className="text-2xl font-bold text-gray-800">{performanceData.performance.minutes_played}'</div>
                                    </div>

                                    {/* Goals Scored */}
                                    {performanceData.performance.goals_scored > 0 && (
                                        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
                                            <div className="flex items-center justify-between mb-2">
                                                <div className="text-sm text-gray-600">{t("playerPerformance.performance.goalsScored")}</div>
                                                <Football />
                                            </div>
                                            <div className="text-2xl font-bold text-green-600">
                                                {performanceData.performance.goals_scored}
                                            </div>
                                        </div>
                                    )}

                                    {/* Assists */}
                                    {performanceData.performance.assists > 0 && (
                                        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
                                            <div className="flex items-center justify-between mb-2">
                                                <div className="text-sm text-gray-600">{t("playerPerformance.performance.assists")}</div>
                                                <Target className="w-4 h-4 text-blue-500" />
                                            </div>
                                            <div className="text-2xl font-bold text-blue-600">
                                                {performanceData.performance.assists}
                                            </div>
                                        </div>
                                    )}

                                    {/* Yellow Cards */}
                                    {performanceData.performance.yellow_cards > 0 && (
                                        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-4 border border-yellow-200">
                                            <div className="flex items-center justify-between mb-2">
                                                <div className="text-sm text-gray-600">{t("playerPerformance.performance.yellowCards")}</div>
                                                <YellowCard />
                                            </div>
                                            <div className="text-2xl font-bold text-yellow-600">
                                                {performanceData.performance.yellow_cards}
                                            </div>
                                        </div>
                                    )}

                                    {/* Red Cards */}
                                    {performanceData.performance.red_cards > 0 && (
                                        <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-4 border border-red-200">
                                            <div className="flex items-center justify-between mb-2">
                                                <div className="text-sm text-gray-600">{t("playerPerformance.performance.redCards")}</div>
                                                <RedCard />
                                            </div>
                                            <div className="text-2xl font-bold text-red-600">
                                                {performanceData.performance.red_cards}
                                            </div>
                                        </div>
                                    )}

                                    {/* Clean Sheet */}
                                    {performanceData.performance.clean_sheet && (
                                        <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg p-4 border border-emerald-200">
                                            <div className="flex items-center justify-between mb-2">
                                                <div className="text-sm text-gray-600">{t("playerPerformance.performance.cleanSheet")}</div>
                                                <Shield className="w-4 h-4 text-emerald-500" />
                                            </div>
                                            <div className="text-2xl font-bold text-emerald-600">✓</div>
                                        </div>
                                    )}

                                    {/* Saves (for goalkeepers) */}
                                    {performanceData.performance.saves > 0 && (
                                        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200">
                                            <div className="flex items-center justify-between mb-2">
                                                <div className="text-sm text-gray-600">{t("playerPerformance.performance.saves")}</div>
                                                <Zap className="w-4 h-4 text-purple-500" />
                                            </div>
                                            <div className="text-2xl font-bold text-purple-600">
                                                {performanceData.performance.saves}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Footer */}
                <div className="border-t bg-gray-50 p-4">
                    <button
                        onClick={onClose}
                        className="w-full bg-gradient-to-r from-gray-600 to-gray-700 text-white font-semibold py-3 px-6 rounded-lg hover:from-gray-700 hover:to-gray-800 transition-all duration-200 transform hover:scale-105 shadow-md"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
}

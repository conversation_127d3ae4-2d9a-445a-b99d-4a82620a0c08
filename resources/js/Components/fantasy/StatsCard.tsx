import { Card, CardContent } from "@/Components/ui/card";

interface StatsCardProps {
    value: string | number;
    label: string;
    className?: string;
}

export default function StatsCard({
    value,
    label,
    className = "",
}: StatsCardProps) {
    return (
        <Card className={`transparent-card ${className}`}>
            <CardContent className="pt-6 text-center">
                <div className="text-3xl font-bold text-gray-200">{value}</div>
                <p className="text-sm text-gray-400 mt-1">{label}</p>
            </CardContent>
        </Card>
    );
}

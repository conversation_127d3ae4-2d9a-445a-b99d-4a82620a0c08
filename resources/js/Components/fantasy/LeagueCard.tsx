import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/Components/ui/card";
import { Badge } from "@/Components/ui/badge";
import { Button } from "@/Components/ui/button";
import { useTranslation } from "react-i18next";
import { LeagueCardProps } from "@/types/index";
import { useForm, Link } from "@inertiajs/react";
import { usePage } from "@inertiajs/react";
import { PageProps } from "@/types";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/Components/ui/alert-dialog";

export default function LeagueCard({
    id,
    name,
    type,
    members,
    rank,
    points,
    code,
    logo,
    isJoined = true,
    onJoin,
}: LeagueCardProps) {
    const { t } = useTranslation();
    const { delete: leaveLeague } = useForm();
    const { app } = usePage<PageProps>().props;

    const getLogoUrl = () => {
        if (!logo) return '';
        // Remove any leading slashes from the logo path
        const cleanLogoPath = logo.startsWith('/') ? logo.substring(1) : logo;
        return `${app.url}/storage/${cleanLogoPath}`;
    };

    const handleLeaveLeague = () => {
        leaveLeague(route("leagues.leave", id), {
            preserveScroll: true,
        });
    };

    const cardContent = (
        <Card className="hover:shadow-md transition-shadow h-full flex flex-col relative">
            {isJoined && (
                <Link
                    href={route("leagues.ranking", id)}
                    className="absolute inset-0 z-10"
                />
            )}
            <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                    {logo && (
                        <div className="w-12 h-12 flex-shrink-0">
                            <img
                                src={getLogoUrl()}
                                alt={`${name} logo`}
                                className="w-full h-full object-cover rounded-full border border-gray-200"
                                onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.src = `${app.url}/images/default-league-logo.png`;
                                    target.onerror = null;
                                }}
                            />
                        </div>
                    )}
                    <div className="flex-1 flex items-center justify-between gap-2">
                        <CardTitle className="text-lg">{name}</CardTitle>
                        <Badge variant={type === "Private" ? "private" : "default"}>
                            {t(`leagueCard.types.${type.toLowerCase()}`)}
                        </Badge>
                    </div>
                </div>
            </CardHeader>
            <CardContent className="space-y-3 flex-grow flex flex-col justify-between">
                <div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span className="text-slate-600">{t("leagueCard.members")}</span>
                            <span className="font-medium ml-1">
                                {members.toLocaleString()}
                            </span>
                        </div>
                        {/* Points Section */}
                        {isJoined && (
                            <div className="flex items-center">
                                <span className="text-slate-600">{t("leagueCard.points")}</span>
                                <span className="font-medium ml-1">{points}</span>
                            </div>
                        )}
                    </div>

                    {/* Rank Section */}
                    {isJoined && (
                        <div className="mt-3 -ml-1 p-2 bg-primary rounded-lg border border-primary-500">
                            <div className="flex items-center justify-between text-sm">
                                <div className="flex items-center">
                                    <span className="text-black ml-2">
                                        {t("leagueCard.rank")}
                                    </span>
                                </div>
                                <div className="flex items-center">
                                    <span className="font-bold text-black text-base">
                                        {rank}
                                    </span>
                                </div>
                            </div>
                        </div>
                    )}

                    {code && (
                        <div className="mt-3 p-2 bg-slate-100 rounded text-center">
                            <span className="text-xs text-slate-600">{t("leagueCard.code")} </span>
                            <span className="font-mono font-medium">{code}</span>
                        </div>
                    )}
                </div>

                <div className="pt-2 relative z-20">
                    {isJoined ? (
                        <AlertDialog>
                            <AlertDialogTrigger asChild>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="w-full"
                                >
                                    {t("leagueCard.leave")}
                                </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                                <AlertDialogHeader>
                                    <AlertDialogTitle>
                                        {t("leagueCard.alert.title")}
                                    </AlertDialogTitle>
                                    <AlertDialogDescription>
                                        {t("leagueCard.alert.description")}
                                    </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                    <AlertDialogCancel>
                                        {t("leagueCard.alert.cancel")}
                                    </AlertDialogCancel>
                                    <AlertDialogAction
                                        className="bg-red-600 hover:bg-red-700"
                                        onClick={handleLeaveLeague}
                                    >
                                        {t("leagueCard.alert.confirm")}
                                    </AlertDialogAction>
                                </AlertDialogFooter>
                            </AlertDialogContent>
                        </AlertDialog>
                    ) : (
                        <Button
                            size="sm"
                            onClick={() => onJoin?.(id)}
                            className="w-full"
                        >
                            {t("leagueCard.join")}
                        </Button>
                    )}
                </div>
            </CardContent>
        </Card>
    );

    return cardContent;
}

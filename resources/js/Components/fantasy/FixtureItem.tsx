import { Badge } from "@/Components/ui/badge";
import { Card, CardContent } from "@/Components/ui/card";
import { Clock, Play, CheckCircle, Pause, XCircle } from "lucide-react";

interface Team {
    id: number;
    name: string;
    short_name: string;
    logo?: string;
    shirt?: {
        base_color?: string;
    };
}

interface FixtureItemProps {
    homeTeam: Team | string;
    awayTeam: Team | string;
    kickoff: string;
    gameweek?: string;
    homeScore?: number | null;
    awayScore?: number | null;
    status?: "scheduled" | "in_progress" | "finished" | "postponed" | "canceled";
    stadium?: string;
    referee?: string;
}

export default function FixtureItem({
    homeTeam,
    awayTeam,
    kickoff,
    gameweek,
    homeScore,
    awayScore,
    status = "scheduled",
    stadium,
    referee
}: FixtureItemProps) {
    const formatKickoff = (kickoffTime: string) => {
        const date = new Date(kickoffTime);
        return date.toLocaleTimeString('en-GB', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getStatusIcon = () => {
        switch (status) {
            case "scheduled":
                return <Clock className="w-4 h-4" />;
            case "in_progress":
                return <Play className="w-4 h-4 text-orange-500" />;
            case "finished":
                return <CheckCircle className="w-4 h-4 text-green-500" />;
            case "postponed":
                return <Pause className="w-4 h-4 text-yellow-500" />;
            case "canceled":
                return <XCircle className="w-4 h-4 text-red-500" />;
            default:
                return <Clock className="w-4 h-4" />;
        }
    };

    const getStatusBadge = () => {
        const variants = {
            scheduled: "default",
            in_progress: "destructive",
            finished: "secondary",
            postponed: "outline",
            canceled: "destructive"
        } as const;

        const labels = {
            scheduled: "Scheduled",
            in_progress: "Live",
            finished: "FT",
            postponed: "Postponed",
            canceled: "Canceled"
        };

        return (
            <Badge variant={variants[status]} className="flex items-center gap-1">
                {getStatusIcon()}
                {labels[status]}
            </Badge>
        );
    };

    const getTeamName = (team: Team | string) => {
        return typeof team === 'string' ? team : team.name;
    };

    const getTeamShortName = (team: Team | string) => {
        return typeof team === 'string' ? team : (team.short_name || team.name);
    };

    const getTeamLogo = (team: Team | string): string | undefined => {
        if (typeof team === 'string') return undefined;
        return team.logo || undefined;
    };

    const getTeamColor = (team: Team | string, defaultColor = '#E5E7EB') => {
        if (typeof team === 'string' || !team.shirt?.base_color) {
            return defaultColor;
        }
        return team.shirt.base_color;
    };

    const showScore = status === "finished" || status === "in_progress";
    const hasScore = homeScore !== null && awayScore !== null;

    return (
        <Card
            className="hover:shadow-md transition-all duration-200 overflow-hidden"
            style={{
                borderLeft: `4px solid ${getTeamColor(homeTeam)}`,
                borderRight: `4px solid ${getTeamColor(awayTeam)}`,
            }}
        >
            <CardContent className="p-4">
                <div className="flex items-center justify-between">
                    {/* Teams and Score */}
                    <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                            {/* Home Team */}
                            <div className="flex items-center space-x-3 flex-1">
                                {getTeamLogo(homeTeam) && (
                                    <img
                                        src={getTeamLogo(homeTeam)}
                                        alt={getTeamName(homeTeam)}
                                        className="w-8 h-8 object-contain"
                                    />
                                )}
                                <div className="flex-1">
                                    <p className="font-semibold text-gray-900 text-sm md:text-base">
                                        {getTeamName(homeTeam)}
                                    </p>
                                    <p className="text-xs text-gray-500 md:hidden">
                                        {getTeamShortName(homeTeam)}
                                    </p>
                                </div>
                            </div>

                            {/* Score or Kickoff time */}
                            <div className="px-4 text-center">
                                {showScore && hasScore ? (
                                    <div className="text-xl font-bold text-gray-900">
                                        {homeScore} - {awayScore}
                                    </div>
                                ) : (
                                    <div className="text-sm font-medium text-gray-500">
                                        {formatKickoff(kickoff)}
                                    </div>
                                )}
                            </div>

                            {/* Away Team */}
                            <div className="flex items-center space-x-3 flex-1 justify-end">
                                <div className="flex-1 text-right">
                                    <p className="font-semibold text-gray-900 text-sm md:text-base">
                                        {getTeamName(awayTeam)}
                                    </p>
                                    <p className="text-xs text-gray-500 md:hidden">
                                        {getTeamShortName(awayTeam)}
                                    </p>
                                </div>
                                {getTeamLogo(awayTeam) && (
                                    <img
                                        src={getTeamLogo(awayTeam)}
                                        alt={getTeamName(awayTeam)}
                                        className="w-8 h-8 object-contain"
                                    />
                                )}
                            </div>
                        </div>

                        

                        {/* Additional Info */}
                        {referee && (
                            <div className="mt-2 text-xs text-gray-500">
                                Referee: {referee}
                            </div>
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}

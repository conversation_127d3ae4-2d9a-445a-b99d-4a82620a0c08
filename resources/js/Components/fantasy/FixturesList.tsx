import React from "react";
import FixtureItem from "./FixtureItem";

interface Team {
    id: number;
    name: string;
    short_name: string;
    logo?: string;
    shirt?: {
        base_color?: string;
    };
}

interface Fixture {
    id: number;
    gameweek_id: number;
    homeTeam: Team;
    awayTeam: Team;
    game_date: string;
    home_score?: number | null;
    away_score?: number | null;
    status: "scheduled" | "in_progress" | "finished" | "postponed" | "canceled";
    stadium?: {
        id: number;
        name: string;
    };
    referee?: {
        id: number;
        name: string;
    };
}

interface FixturesListProps {
    fixtures: Fixture[];
}

export default function FixturesList({ fixtures }: FixturesListProps) {
    if (fixtures.length === 0) {
        return (
            <div className="text-center py-8">
                <p className="text-gray-500">No fixtures available for this gameweek.</p>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {fixtures.map((fixture) => (
                <FixtureItem
                    key={fixture.id}
                    homeTeam={fixture.homeTeam}
                    awayTeam={fixture.awayTeam}
                    kickoff={fixture.game_date}
                    homeScore={fixture.home_score}
                    awayScore={fixture.away_score}
                    status={fixture.status}
                    stadium={fixture.stadium?.name}
                    referee={fixture.referee?.name}
                />
            ))}
        </div>
    );
}
import React from "react";
import { <PERSON> } from "@inertiajs/react";
import { Card, CardContent } from "@/Components/ui/card";

interface FantasyNavigationCardProps {
    title: string;
    description: string;
    icon: string;
    href: string;
    color?: string;
}

export default function FantasyNavigationCard({
    title,
    description,
    icon,
    href,
    color = "",
}: FantasyNavigationCardProps) {
    return (
        <Card className="group transparent-card hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
            <CardContent className="pt-6">
                <Link href={href} className="block text-center space-y-3">
                    <div className="text-4xl">{icon}</div>
                    <div className="space-y-1">
                        <h3 className="text-lg font-semibold text-gray-200">
                            {title}
                        </h3>
                        <p className="text-sm text-gray-400">{description}</p>
                    </div>
                </Link>
            </CardContent>
        </Card>
    );
}

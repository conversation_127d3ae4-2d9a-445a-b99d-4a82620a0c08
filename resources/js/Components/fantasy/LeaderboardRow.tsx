import React from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from "@/lib/utils";
import { ArrowUp, ArrowDown, Minus, Crown, Medal, Award } from "lucide-react";

interface LeaderboardRowProps {
    rank: number;
    name: string;
    teamName: string;
    points: number;
    previousRank?: number;
    isCurrentUser?: boolean;
    highlight?: boolean;
}

export default function LeaderboardRow({
    rank,
    name,
    teamName,
    points,
    previousRank,
    isCurrentUser = false,
    highlight = false
}: LeaderboardRowProps) {
    const { t } = useTranslation();
    
    const getRankClasses = (position: number) => {
        if (position === 1) return "bg-gradient-to-r from-amber-50 to-yellow-50 border-amber-200 shadow-sm";
        if (position === 2) return "bg-gradient-to-r from-slate-50 to-gray-50 border-slate-200 shadow-sm";
        if (position === 3) return "bg-gradient-to-r from-orange-50 to-amber-50 border-orange-200 shadow-sm";
        return "bg-white border-gray-100 hover:bg-gray-50";
    };

    const getRankIcon = (position: number) => {
        if (position === 1) return <Crown className="w-4 h-4 text-amber-600" />;
        if (position === 2) return <Medal className="w-4 h-4 text-slate-600" />;
        if (position === 3) return <Award className="w-4 h-4 text-orange-600" />;
        return null;
    };

    const getRankTextColor = (position: number) => {
        if (position === 1) return "text-amber-700";
        if (position === 2) return "text-slate-700";
        if (position === 3) return "text-orange-700";
        return "text-gray-700";
    };

    const renderRankComparison = () => {
        let movement: 'up' | 'down' | 'same' = 'same';
        let diff = 0;

        if (previousRank !== undefined && previousRank !== null) {
            if (rank < previousRank) {
                movement = 'up';
                diff = previousRank - rank;
            } else if (rank > previousRank) {
                movement = 'down';
                diff = rank - previousRank;
            }
        }

        const movementIcon = {
            up: <ArrowUp className="w-2.5 h-2.5 text-green-600 -ml-1" />,
            down: <ArrowDown className="w-2.5 h-2.5 text-red-600 -ml-1" />,
            same: <Minus className="w-2.5 h-2.5 text-gray-400 -ml-1" />
        }[movement];

        const movementText = {
            up: <span className="text-green-600 font-semibold text-xs">+{diff}</span>,
            down: <span className="text-red-600 font-semibold text-xs">-{diff}</span>,
            same: null
        }[movement];

        return (
            <div className="flex flex-col items-center justify-center space-y-0.5">
                <div className="flex items-center space-x-1">
                    {getRankIcon(rank)}
                    <span className={cn("font-bold text-lg", getRankTextColor(rank))}>
                        {rank}
                    </span>
                </div>
                {previousRank !== undefined && previousRank !== null && (
                    <div className="flex items-center space-x-1 text-xs">
                        {movementIcon}
                        {movementText}
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className={cn(
            "grid grid-cols-12 gap-4 items-center p-4 rounded-lg border transition-all duration-200",
            "hover:shadow-md hover:scale-[1.01]",
            isCurrentUser ? "bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 ring-2 ring-blue-200/50" : getRankClasses(rank),
            highlight && "ring-1 ring-amber-200/50"
        )}>
            {/* Rank Column */}
            <div className="col-span-1 flex justify-center">
                {renderRankComparison()}
            </div>
            
            {/* Team Info Column */}
            <div className="col-span-7 min-w-0">
                <div className="flex flex-col">
                    <p className={cn(
                        "font-semibold text-base truncate",
                        isCurrentUser ? "text-blue-900" : "text-gray-800"
                    )}>
                        {teamName}
                    </p>
                    <p className={cn(
                        "text-sm truncate",
                        isCurrentUser ? "text-blue-600" : "text-gray-500"
                    )}>
                        {name}
                    </p>
                </div>
            </div>
            
            {/* Empty Column for spacing */}
            <div className="col-span-2"></div>
            
            {/* Points Column */}
            <div className="col-span-2 text-right">
                <div className="flex flex-col items-end">
                    <span className={cn(
                        "font-bold text-lg",
                        isCurrentUser ? "text-blue-900" : rank <= 3 ? getRankTextColor(rank) : "text-gray-900"
                    )}>
                        {points.toLocaleString()}
                    </span>
                </div>
            </div>
        </div>
    );
}
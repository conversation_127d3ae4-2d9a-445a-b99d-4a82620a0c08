import React from "react";
import { Badge } from "@/Components/ui/badge";

interface ActivityItemProps {
    title: string;
    description: string;
    badge: string;
    variant: "green" | "blue" | "amber";
}

export default function ActivityItem({ title, description, badge, variant }: ActivityItemProps) {
    const variantClasses = {
        green: {
            container: "bg-green-50 border-green-100",
            title: "text-green-900",
            description: "text-green-600",
            badge: "bg-green-100 text-green-700 hover:bg-green-200"
        },
        blue: {
            container: "bg-blue-50 border-blue-100",
            title: "text-blue-900",
            description: "text-blue-600",
            badge: "bg-blue-100 text-blue-700 hover:bg-blue-200"
        },
        amber: {
            container: "bg-amber-50 border-amber-100",
            title: "text-amber-900",
            description: "text-amber-600",
            badge: "bg-amber-100 text-amber-700 hover:bg-amber-200"
        }
    };

    const classes = variantClasses[variant];

    return (
        <div className={`flex items-center justify-between p-4 rounded-lg border ${classes.container}`}>
            <div className="space-y-1">
                <p className={`font-medium ${classes.title}`}>{title}</p>
                <p className={`text-sm ${classes.description}`}>{description}</p>
            </div>
            <Badge variant="secondary" className={classes.badge}>
                {badge}
            </Badge>
        </div>
    );
}

import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/Components/ui/card";
import { Badge } from "@/Components/ui/badge";
import * as Tabs from "@radix-ui/react-tabs";
import FixtureItem from "./FixtureItem";
import { Calendar, ChevronLeft, ChevronRight, Clock } from "lucide-react";
import { useTranslation } from "react-i18next";

interface Team {
    id: number;
    name: string;
    short_name: string;
    logo?: string;
    shirt?: {
        base_color?: string;
    };
}

interface Fixture {
    id: number;
    gameweek_id: number;
    homeTeam: Team;
    awayTeam: Team;
    game_date: string;
    home_score?: number | null;
    away_score?: number | null;
    status: "scheduled" | "in_progress" | "finished" | "postponed" | "canceled";
    stadium?: {
        id: number;
        name: string;
    };
    referee?: {
        id: number;
        name: string;
    };
}

interface Gameweek {
    id: number;
    name: string;
    startDate: string;
    endDate: string;
    status: string;
}

interface PaginationInfo {
    currentIndex: number;
    totalGameweeks: number;
    hasNext: boolean;
    hasPrevious: boolean;
}

interface FixturesWidgetProps {
    fixtures: Record<number, Record<string, Fixture[]>>;
    gameweeks: Gameweek[];
    allGameweeks: Gameweek[];
    currentGameweek: Gameweek | null;
    pagination?: PaginationInfo;
    showNavigation?: boolean;
    showCompetitionInfo?: boolean;
    maxFixtures?: number;
    title?: string;
    className?: string;
    transparent?: boolean;
}

export default function FixturesWidget({
    fixtures,
    gameweeks,
    allGameweeks,
    currentGameweek,
    pagination,
    showNavigation = true,
    showCompetitionInfo = false,
    maxFixtures,
    title,
    className = "",
    transparent = false
}: FixturesWidgetProps) {
    const { t, i18n } = useTranslation();
    const [selectedGameweek, setSelectedGameweek] = useState(
        currentGameweek?.id || (gameweeks.length > 0 ? gameweeks[0].id : 1)
    );

    // Get current gameweek fixtures
    const currentFixtures = useMemo(() => {
        const fixturesForGameweek = fixtures[selectedGameweek] || {};

        // If maxFixtures is specified, limit the number of fixtures
        if (maxFixtures) {
            const allFixtures: Fixture[] = [];
            Object.values(fixturesForGameweek).forEach(fixturesOnDate => {
                allFixtures.push(...fixturesOnDate);
            });

            const limitedFixtures = allFixtures.slice(0, maxFixtures);

            // Group limited fixtures back by date
            const groupedLimited: Record<string, Fixture[]> = {};
            limitedFixtures.forEach(fixture => {
                const date = new Date(fixture.game_date).toISOString().split('T')[0];
                if (!groupedLimited[date]) {
                    groupedLimited[date] = [];
                }
                groupedLimited[date].push(fixture);
            });

            return groupedLimited;
        }

        return fixturesForGameweek;
    }, [fixtures, selectedGameweek, maxFixtures]);

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        // Use current locale from i18n, fallback to 'en-GB'
        const locale = i18n.language || 'en-GB';
        return date.toLocaleDateString(locale, {
            weekday: 'short',
            day: 'numeric',
            month: 'short'
        });
    };

    // Helper function to get gameweek status color
    const getGameweekStatusColor = (status: string) => {
        switch (status) {
            case 'ongoing':
                return 'bg-green-100 text-green-800';
            case 'completed':
                return 'bg-gray-100 text-gray-800';
            case 'upcoming':
                return 'bg-blue-100 text-blue-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    // Helper function to get translated gameweek status
    const getTranslatedStatus = (status: string) => {
        switch (status) {
            case 'ongoing':
                return t('fixtures.ongoing');
            case 'completed':
                return t('fixtures.completed');
            case 'upcoming':
                return t('fixtures.upcoming');
            default:
                return t('fixtures.unknown');
        }
    };

    // Helper function to navigate gameweeks
    const navigateGameweek = (direction: 'previous' | 'next') => {
        const currentIndex = allGameweeks.findIndex(gw => gw.id === currentGameweek?.id);
        if (currentIndex === -1) return;

        let targetIndex;
        if (direction === 'previous') {
            targetIndex = Math.max(0, currentIndex - 1);
        } else {
            targetIndex = Math.min(allGameweeks.length - 1, currentIndex + 1);
        }

        const targetGameweek = allGameweeks[targetIndex];
        if (targetGameweek) {
            setSelectedGameweek(targetGameweek.id);
        }
    };

    // Count total fixtures for display
    const totalFixtures = Object.values(currentFixtures).reduce(
        (total, fixturesOnDate) => total + fixturesOnDate.length,
        0
    );

    // Get card class based on transparent prop
    const cardClass = transparent ? "transparent-card" : "";
    const titleClass = transparent ? "text-gray-200" : "";
    const dateTextClass = transparent ? "text-gray-200" : "text-gray-800";
    const badgeVariant = transparent ? "secondary" : "outline" as const;
    const emptyStateHeadingClass = transparent ? "text-gray-200" : "text-gray-900";
    const emptyStateTextClass = transparent ? "text-gray-300" : "text-gray-600";
    const emptyStateIconClass = transparent ? "text-gray-300" : "text-gray-400";
    const paginationTextClass = transparent ? "text-gray-300" : "text-gray-500";

    return (
        <div className={className}>
            {/* Enhanced Gameweek Navigation */}
            {showNavigation && gameweeks.length > 1 && (
                <Card className={`mb-6 ${cardClass}`}>
                    <CardHeader>
                        <CardTitle className={`flex items-center justify-between ${titleClass}`}>
                            <div className="flex items-center space-x-2">
                                <Calendar className="w-5 h-5" />
                                <span>{t('fixtures.gameweekNavigation')}</span>
                            </div>
                            {pagination && (
                                <div className={`text-sm ${paginationTextClass}`}>
                                    {t('fixtures.paginationText', {
                                        current: pagination.currentIndex + 1,
                                        total: pagination.totalGameweeks
                                    })}
                                </div>
                            )}
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center space-x-2">
                            {/* Previous Button */}
                            <button
                                onClick={() => navigateGameweek('previous')}
                                disabled={!pagination?.hasPrevious}
                                className={`
                                    p-2 rounded-lg transition-all duration-200
                                    ${pagination?.hasPrevious
                                        ? 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                                        : 'bg-gray-50 text-gray-300 cursor-not-allowed'
                                    }
                                `}
                                title={t('fixtures.previousGameweeks')}
                            >
                                <ChevronLeft className="w-4 h-4" />
                            </button>

                            {/* Gameweek Tabs */}
                            <div className="flex-1">
                                <Tabs.Root
                                    value={selectedGameweek.toString()}
                                    onValueChange={(value) => setSelectedGameweek(parseInt(value))}
                                    className="w-full"
                                >
                                    <Tabs.List className="flex gap-2 p-1 bg-gray-100 rounded-lg overflow-x-auto">
                                        {gameweeks.map((gameweek) => {
                                            const isActive = gameweek.id === selectedGameweek;
                                            const isCurrent = gameweek.id === currentGameweek?.id;
                                            const statusColor = getGameweekStatusColor(gameweek.status);

                                            return (
                                                <Tabs.Trigger
                                                    key={gameweek.id}
                                                    value={gameweek.id.toString()}
                                                    className={`
                                                        px-4 py-3 text-sm font-medium rounded-md transition-all duration-200 min-w-0 flex-shrink-0
                                                        ${isActive
                                                            ? 'bg-blue-600 text-white shadow-md'
                                                            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                                                        }
                                                        ${isCurrent && !isActive ? 'ring-2 ring-blue-300' : ''}
                                                    `}
                                                >
                                                    <div className="flex flex-col items-center space-y-1">
                                                        <span className="font-semibold">{gameweek.name}</span>
                                                        <div className="flex items-center space-x-1">
                                                            {isCurrent && (
                                                                <span className="text-xs opacity-75 bg-white/20 px-1 rounded">
                                                                    {t('fixtures.current')}
                                                                </span>
                                                            )}
                                                            <span className={`text-xs px-1 rounded ${statusColor}`}>
                                                                {getTranslatedStatus(gameweek.status)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </Tabs.Trigger>
                                            );
                                        })}
                                    </Tabs.List>
                                </Tabs.Root>
                            </div>

                            {/* Next Button */}
                            <button
                                onClick={() => navigateGameweek('next')}
                                disabled={!pagination?.hasNext}
                                className={`
                                    p-2 rounded-lg transition-all duration-200
                                    ${pagination?.hasNext
                                        ? 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                                        : 'bg-gray-50 text-gray-300 cursor-not-allowed'
                                    }
                                `}
                                title={t('fixtures.nextGameweeks')}
                            >
                                <ChevronRight className="w-4 h-4" />
                            </button>
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Fixtures List */}
            <Card className={cardClass}>
                <CardHeader>
                    <CardTitle className={`flex items-center justify-between ${titleClass}`}>
                        <div className="flex items-center space-x-2">
                            <Clock className="w-5 h-5" />
                            <span>
                                {title ||
                                    t('fixtures.gameweekFixtures', {
                                        gameweek: gameweeks.find(gw => gw.id === selectedGameweek)?.name ||
                                            t('fixtures.gameweekNumber', { number: selectedGameweek })
                                    })
                                }
                            </span>
                        </div>
                        <Badge variant={badgeVariant}>
                            {totalFixtures} {totalFixtures === 1 ? t('fixtures.match') : t('fixtures.matches')}
                        </Badge>
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    {Object.keys(currentFixtures).length === 0 ? (
                        <div className="text-center py-12">
                            <Clock className={`w-12 h-12 ${emptyStateIconClass} mx-auto mb-4`} />
                            <h3 className={`text-lg font-medium ${emptyStateHeadingClass} mb-2`}>
                                {t('fixtures.noFixturesFound')}
                            </h3>
                            <p className={emptyStateTextClass}>
                                {t('fixtures.noFixturesScheduled')}
                            </p>
                        </div>
                    ) : (
                        Object.entries(currentFixtures).map(([date, fixturesOnDate]) => (
                            <div key={date}>
                                <h3 className={`text-lg font-semibold ${dateTextClass} mb-2`}>{formatDate(date)}</h3>
                                <div className="space-y-4">
                                    {fixturesOnDate.map((fixture: Fixture) => (
                                        <FixtureItem
                                            key={fixture.id}
                                            homeTeam={fixture.homeTeam}
                                            awayTeam={fixture.awayTeam}
                                            kickoff={fixture.game_date}
                                            homeScore={fixture.home_score}
                                            awayScore={fixture.away_score}
                                            status={fixture.status}
                                            stadium={fixture.stadium?.name}
                                            referee={fixture.referee?.name}
                                        />
                                    ))}
                                </div>
                            </div>
                        ))
                    )}
                </CardContent>
            </Card>
        </div>
    );
}

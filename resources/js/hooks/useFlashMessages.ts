import { useEffect } from 'react'
import { usePage } from '@inertiajs/react'
import { toast } from '@/hooks/use-toast'
import { PageProps } from '@/types/inertia'

export function useFlashMessages() {
  const { props } = usePage<PageProps>()
  const flash = props.flash

  useEffect(() => {
    // Display success message
    if (flash?.success) {
      toast({
        variant: "success",
        title: "Success",
        description: flash.success,
      })
    }

    // Display error message
    if (flash?.error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: flash.error,
      })
    }

    // Display general message
    if (flash?.message) {
      toast({
        variant: "default",
        title: "Notice",
        description: flash.message,
      })
    }
  }, [flash?.success, flash?.error, flash?.message])
}

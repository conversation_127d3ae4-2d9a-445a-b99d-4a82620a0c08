import { usePage } from "@inertiajs/react";
import { PageProps, Tenant } from "@/types/inertia";

/**
 * Custom hook to access current tenant data throughout the application
 *
 * @returns {Tenant | null} Current tenant data or null if no tenant is active
 */
export function useTenant(): Tenant | null {
    const { tenant } = usePage<PageProps>().props;
    return tenant;
}

/**
 * Custom hook to check if a tenant is currently active
 *
 * @returns {boolean} True if a tenant is active, false otherwise
 */
export function useHasTenant(): boolean {
    const tenant = useTenant();
    return tenant !== null;
}

/**
 * Custom hook to get tenant name with fallback
 *
 * @param fallback - Fallback name if no tenant is active
 * @returns {string} Tenant name or fallback
 */
export function useTenantName(fallback: string = "Fantasy Football"): string {
    const tenant = useTenant();
    return tenant?.name || fallback;
}

/**
 * Custom hook to get tenant logo with fallback
 *
 * @returns {string | null} Tenant logo or null if no logo is set
 */

export function useTenantLogo(): string | null {
    const tenant = useTenant();
    return tenant?.logo || null;
}

/**
 * Custom hook to get tenant favicon with fallback
 *
 * @returns {string | null} Tenant favicon or null if no favicon is set
 */
export function useTenantFavicon(): string | null {
    const tenant = useTenant();
    return tenant?.favicon || null;
}

/** Custom hook to get tenant background image with fallback
 *
 * @returns {string | null} Tenant background image or null if no image is set
 */

export function useBackgroundImage(): string | null {
    const tenant = useTenant();
    return tenant?.background_image || null;
}

/** Custom hook to get tenant primary color with fallback
 *
 * @returns {string} Tenant primary color or fallback
 */
export function usePrimaryColor(): string | null {
    const tenant = useTenant();
    return tenant?.primary_color || null;
}

/** Custom hook to get tenant secondary color with fallback
 *
 * @returns {string} Tenant secondary color or fallback
 */
export function useSecondaryColor(): string | null {
    const tenant = useTenant();
    return tenant?.secondary_color || null;
}

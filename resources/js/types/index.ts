import { PageProps as InertiaPageProps } from '@inertiajs/core';

export interface User {
    id: number;
    name: string;
    first_name?: string;
    last_name?: string;
    email: string;
    avatar?: string;
    email_verified_at?: string;
    wallet_balance?: number;
    created_at: string;
    updated_at: string;
}

export interface Tenant {
    id: number;
    name: string;
    domain: string;
    database: string;
    created_at: string;
    updated_at: string;
}

export interface LeagueCardProps {
    id: number;
    name: string;
    type: "Private" | "Public";
    members: number;
    rank?: number;
    points?: number;
    prize?: string;
    entryFee?: string;
    code?: string;
    logo?: string;
    isJoined?: boolean;
    onJoin?: (id: number) => void;
    onLeave?: (id: number) => void;
}

export interface Competition {
    id: number;
    name: string;
    slug: string;
}

export interface Auth {
    user: User | null;
}

export interface Flash {
    message?: string;
    error?: string;
    success?: string;
}

export interface PageProps extends InertiaPageProps {
    auth: Auth;
    flash: Flash;
    app: {
        available_languages: string[];
        locale: string;
        translations: Record<string, string>;
        url: string;
    };
    tenant: Tenant | null;
    competitions: Competition[];
    currentCompetition: Competition | null;
}

export interface ErrorProps extends PageProps {
    status: number;
    message: string;
}

export interface Season {
    id: number;
    name: string;
    start_date: string;
    end_date: string;
}

export interface Team {
    id: number;
    name: string;
    code_name: string;
    logo: string;
}

export interface ShirtTypeOption {
    value: string;
    label: string;
}

export interface LeaderboardRowProps {
    rank: number;
    peviousRank: number;
    name: string;
    leagueType: string;
    teamName: string;
    points: number;
    lastWeek: number;
    isCurrentUser?: boolean;
}
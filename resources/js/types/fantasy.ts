import { Paginated } from "./pagination";

export interface FantasyStats {
    gameweek_points: number;
    transfers_used: number;
    gameweek_rank: number | null;
    highest_points: number;
    average_points: number;
    total_points: number;
}

export interface PlayerPerformanceData {
    player: {
        id: number;
        name: string;
        position: string;
    };
    game: {
        id: number;
        home_team: {
            id: number;
            name: string;
            short_name: string;
            logo: string;
        };
        away_team: {
            id: number;
            name: string;
            short_name: string;
            logo: string;
        };
        home_score: number | null;
        away_score: number | null;
        status: string;
        game_date: string | null;
    };
    performance: {
        minutes_played: number;
        goals_scored: number;
        assists: number;
        clean_sheet: boolean;
        goals_conceded: number;
        own_goals: number;
        penalties_saved: number;
        penalties_missed: number;
        penalties_caused: number;
        penalties_committed: number;
        saves: number;
        yellow_cards: number;
        red_cards: number;
    };
    team_played_for: {
        id: number;
        name: string;
        short_name: string;
        logo: string;
    };
    fantasy_points: {
        total: number;
        breakdown: PointsBreakdownItem[];
        calculated_total: number;
    };
}

export interface PointsBreakdownItem {
    action: string;
    points: number;
    count: number;
}

export interface TeamData {
    budget: number;
    squadValue: number;
    balance: number;
    freeTransfers: number;
}
export interface Transfer {
    id: number;
    fantasy_team: {
        id: number;
        name: string;
        user: {
            id: number;
            name: string;
        };
    };
    player_in: {
        id: number;
        player: {
            id: number;
            name: string;
        };
    };
    player_out: {
        id: number;
        player: {
            id: number;
            name: string;
        };
    };
    gameweek_id: number;
    created_at: string;
    updated_at: string;
}
export interface SquadPlayer {
    id: number;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    price: number;
    team: string;
    is_captain: boolean;
    is_vice_captain: boolean;
    fantasy_player_id: number;
    team_data: {
        logo: string;
        shirt: {
            base_color: string;
            pattern_type: string;
            pattern_color: string;
        };
    };
    points: number;
}

export interface League {
    id: number;
    name: string;
    season_id: number;
    owner_id: number;
    type: string;
    invite_code: string | null;
    logo?: string;
    created_at: string;
    updated_at: string;
    tenant_id: number;
    season: object;
    fantasy_teams: FantasyTeam[];
}

export interface Ranking {
    rank: number;
    name: string;
    teamName: string;
    points: number;
    lastWeek: number;
}

export interface FantasyTeam {
    id: number;
    name: string;
    kit_type: string;
    kit_primary_color: string;
    kit_secondary_color: string;
}

export interface Gameweek {
    id: number;
    name: string;
    deadline: string;
}

export interface CurrentGameweek {
    id: number;
    name: string;
    deadline: string;
}

export interface MyTeamManagerProps {
    teamData: TeamData;
    squadPlayers: SquadPlayer[];
    startingPlayerIds: number[];
    fantasyTeam: FantasyTeam;
    currentGameweek: CurrentGameweek;
}

export interface PointsManagerProps {
    squadPlayers: SquadPlayer[];
    startingPlayerIds: number[];
    fantasyTeam: FantasyTeam;
}

export interface BasePlayer {
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    price: number;
    is_captain?: boolean;
    is_vice_captain?: boolean;
    // Optional extended fields for richer player info
    name_ar?: string;
    birthday?: string; // ISO date string
    country?: string;
    image?: string; // URL to player image/avatar
}

export interface TeamData {
    budget: number;
    squadValue: number;
    balance: number;
    freeTransfers: number;
}

export interface Player {
    id: number | string;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    team: string;
    price: number;
    is_captain?: boolean;
    is_vice_captain?: boolean;
}

export interface SquadPlayer {
    id: number;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    price: number;
    team: string;
    is_captain: boolean;
    is_vice_captain: boolean;
    fantasy_player_id: number;
}

export interface FantasyTeam {
    id: number;
    name: string;
    kit_type: string;
    kit_primary_color: string;
    kit_secondary_color: string;
}

export interface CurrentGameweek {
    id: number;
    name: string;
    deadline: string;
}

export interface Player extends BasePlayer {
    id: number | string;
    onPitch: boolean;
    pitchPosition: { x: number; y: number } | null;
    team_data: {
        logo: string;
        shirt: {
            base_color: string;
            pattern_type: string;
            pattern_color: string;
        };
    };
    points: number;
}

export interface PositionLayout {
    x: number;
    y: number;
}

export interface FormationLayout {
    GK: PositionLayout[];
    DEF: PositionLayout[];
    MID: PositionLayout[];
    FWD: PositionLayout[];
}

export interface Formations {
    [key: string]: FormationLayout;
}

export interface ConfirmationModalProps {
    changes: {
        playersIn: Player[];
        playersOut: Player[];
        captain: Player | undefined;
        viceCaptain: Player | undefined;
    };
    onConfirm: () => void;
    onCancel: () => void;
}

export interface TransferPlayer extends BasePlayer {
    id: number | string; // Can be number for real players, string for placeholders
    team: string;
    team_data: {
        logo: string;
        shirt: {
            base_color: string;
            pattern_type: string;
            pattern_color: string;
        };
    };
}

export interface TeamOption {
    name: string;
    logo?: string | null;
}

export interface AvailablePlayersResponse extends Paginated<TransferPlayer> {
    // Prefer objects with logo; some older responses may still be string[]
    teams: TeamOption[] | string[]; // All teams from current season for filter dropdown
}

export interface TransferManagerProps {
    isCreationMode?: boolean;
    initialSquadData?: TransferPlayer[];
    availablePlayers: AvailablePlayersResponse;
    initialBudget?: number;
    onSquadSubmit?: (squadData: {
        players: TransferPlayer[];
        captain: TransferPlayer | null;
        viceCaptain: TransferPlayer | null;
        budget: number;
    }) => void;
    onFetchPlayers?: (filters?: any) => Promise<void>;
    isLoadingPlayers?: boolean;
}

export interface FantasyTransfer {
    id: number;
    fantasy_team_id: number;
    player_in_id: number | null;
    player_out_id: number | null;
    gameweek_id: number;
    transfer_cost: number;
    created_at: string;
    updated_at: string;
    fantasy_team: {
        id: number;
        name: string;
        user: {
            id: number;
            first_name: string;
            last_name: string;
        };
    };
    playerIn?: {
        id: number;
        player: {
            id: number;
            name: string;
        };
    };
    playerOut?: {
        id: number;
        player: {
            id: number;
            name: string;
        };
    };
    gameweek: {
        id: number;
        name: string;
        number: number;
    };
}

export const formations: Formations = {
    "4-4-2": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 20, y: 35 },
            { x: 40, y: 35 },
            { x: 60, y: 35 },
            { x: 80, y: 35 },
        ],
        MID: [
            { x: 20, y: 60 },
            { x: 40, y: 60 },
            { x: 60, y: 60 },
            { x: 80, y: 60 },
        ],
        FWD: [
            { x: 40, y: 85 },
            { x: 60, y: 85 },
        ],
    },
    "4-3-3": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 20, y: 35 },
            { x: 40, y: 35 },
            { x: 60, y: 35 },
            { x: 80, y: 35 },
        ],
        MID: [
            { x: 30, y: 60 },
            { x: 50, y: 60 },
            { x: 70, y: 60 },
        ],
        FWD: [
            { x: 25, y: 85 },
            { x: 50, y: 85 },
            { x: 75, y: 85 },
        ],
    },
    "3-5-2": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 30, y: 35 },
            { x: 50, y: 35 },
            { x: 70, y: 35 },
        ],
        MID: [
            { x: 15, y: 60 },
            { x: 35, y: 60 },
            { x: 50, y: 60 },
            { x: 65, y: 60 },
            { x: 85, y: 60 },
        ],
        FWD: [
            { x: 40, y: 85 },
            { x: 60, y: 85 },
        ],
    },
    "5-3-2": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 15, y: 35 },
            { x: 30, y: 35 },
            { x: 50, y: 35 },
            { x: 70, y: 35 },
            { x: 85, y: 35 },
        ],
        MID: [
            { x: 30, y: 60 },
            { x: 50, y: 60 },
            { x: 70, y: 60 },
        ],
        FWD: [
            { x: 40, y: 85 },
            { x: 60, y: 85 },
        ],
    },
    "4-5-1": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 20, y: 35 },
            { x: 40, y: 35 },
            { x: 60, y: 35 },
            { x: 80, y: 35 },
        ],
        MID: [
            { x: 15, y: 60 },
            { x: 35, y: 60 },
            { x: 50, y: 60 },
            { x: 65, y: 60 },
            { x: 85, y: 60 },
        ],
        FWD: [{ x: 50, y: 85 }],
    },
    "3-4-3": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 30, y: 35 },
            { x: 50, y: 35 },
            { x: 70, y: 35 },
        ],
        MID: [
            { x: 20, y: 60 },
            { x: 40, y: 60 },
            { x: 60, y: 60 },
            { x: 80, y: 60 },
        ],
        FWD: [
            { x: 25, y: 85 },
            { x: 50, y: 85 },
            { x: 75, y: 85 },
        ],
    },
    "5-4-1": {
        GK: [{ x: 50, y: 10 }],
        DEF: [
            { x: 15, y: 35 },
            { x: 30, y: 35 },
            { x: 50, y: 35 },
            { x: 70, y: 35 },
            { x: 85, y: 35 },
        ],
        MID: [
            { x: 20, y: 60 },
            { x: 40, y: 60 },
            { x: 60, y: 60 },
            { x: 80, y: 60 },
        ],
        FWD: [{ x: 50, y: 85 }],
    },
};

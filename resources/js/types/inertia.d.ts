import { PageProps as InertiaPageProps } from "@inertiajs/core";

export interface User {
    id: number;
    name: string;
    first_name: string;
    last_name: string;
    email: string;
    avatar?: string;
    email_verified_at: string;
    created_at: string;
    updated_at: string;
}

export interface Tenant {
    id: number;
    name: string;
    domain: string;
    database: string;
    logo: string;
    favicon: string;
    background_image: string;
    primary_color: string;
    secondary_color: string;
    created_at: string;
    updated_at: string;
}

export interface Competition {
    id: number;
    name: string;
    slug: string;
}

export interface Flash {
    success?: string;
    error?: string;
    message?: string;
}

export type PageProps<
    T extends Record<string, unknown> = Record<string, unknown>
> = T & {
    app: {
        available_languages: string[];
        locale: string;
        translations: Record<string, string>;
        url: string;
    };
    auth: {
        user: User;
    };
    tenant: Tenant | null;
    competitions: Competition[];
    currentCompetition: Competition | null;
    flash: Flash;
} & InertiaPageProps;

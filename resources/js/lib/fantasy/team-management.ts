import { Player, formations } from "@/types/fantasy";
import { TFunction } from "i18next";

export const calculateSquadValue = (
    squad: Omit<Player, "onPitch" | "pitchPosition" | "initialOnPitch">[]
): number => {
    return squad.reduce((total, player) => total + player.price, 0);
};

export const getFormationKey = (counts: { [key: string]: number }): string =>
    `${counts.DEF}-${counts.MID}-${counts.FWD}`;

export const applyFormation = (
    players: Player[],
    formationKey: string,
    t: TFunction
): Player[] => {
    let formationLayout = formations[formationKey];

    if (!formationLayout) {
        console.warn(t("myTeam.formationNotFound", { formationKey }));
        formationLayout = formations["4-4-2"];
    }

    const positionCounters = { GK: 0, DEF: 0, MID: 0, FWD: 0 };
    const playersWithPositions = [...players];

    playersWithPositions
        .filter((p) => p.onPitch)
        .forEach((player) => {
            const posType = player.position;
            const posIndex = positionCounters[posType];

            if (
                formationLayout[posType] &&
                posIndex < formationLayout[posType].length
            ) {
                player.pitchPosition = formationLayout[posType][posIndex];
                positionCounters[posType]++;
            } else {
                console.warn(
                    t("myTeam.noFormationPosition", {
                        playerName: player.name,
                        position: posType,
                    })
                );
                player.pitchPosition = {
                    x: 50 + posIndex * 10 - 20,
                    y:
                        posType === "GK"
                            ? 90
                            : posType === "DEF"
                            ? 70
                            : posType === "MID"
                            ? 50
                            : 30,
                };
            }
        });

    return playersWithPositions;
};

export const initializeTeam = (
    squad: Omit<Player, "onPitch" | "pitchPosition">[],
    startingIds: number[],
    t: TFunction
): Player[] => {
    let players: Player[] = squad.map((player) => ({
        ...player,
        onPitch: startingIds.includes(player.id),
        pitchPosition: null,
    }));
    const pitchPlayers = players.filter((p) => p.onPitch);
    const counts = { GK: 0, DEF: 0, MID: 0, FWD: 0 };
    pitchPlayers.forEach((p) => {
        counts[p.position]++;
    });

    const formationKey = getFormationKey(counts);
    return applyFormation(players, formationKey, t);
};

export const validateSwap = (
    playerOut: Player,
    playerIn: Player,
    currentPitchPlayers: Player[],
    balance: number,
    t: TFunction
): { valid: boolean; message: string } => {
    const priceDifference = playerIn.price - playerOut.price;
    if (priceDifference > balance)
        return {
            valid: false,
            message: t("myTeam.notEnoughFunds", {
                amount: priceDifference.toFixed(1),
            }),
        };
    const newPitchLineup = currentPitchPlayers.filter(
        (p) => p.id !== playerOut.id
    );
    newPitchLineup.push(playerIn);
    const counts = { GK: 0, DEF: 0, MID: 0, FWD: 0 };
    newPitchLineup.forEach((p) => {
        counts[p.position]++;
    });
    if (counts.GK !== 1)
        return {
            valid: false,
            message: t("myTeam.oneGoalkeeper"),
        };
    if (counts.DEF < 3 || counts.DEF > 5)
        return { valid: false, message: t("myTeam.defendersRule") };
    if (counts.MID < 3 || counts.MID > 5)
        return { valid: false, message: t("myTeam.midfieldersRule") };
    if (counts.FWD < 1 || counts.FWD > 3)
        return { valid: false, message: t("myTeam.forwardsRule") };
    const formationKey = getFormationKey(counts);
    if (!formations[formationKey])
        return {
            valid: false,
            message: t("myTeam.invalidFormation", { formationKey }),
        };
    return { valid: true, message: "" };
};

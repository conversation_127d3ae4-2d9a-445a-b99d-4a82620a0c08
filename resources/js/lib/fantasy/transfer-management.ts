import { TransferPlayer } from "@/types/fantasy";
import { TFunction } from "i18next";

export const MAX_PLAYERS_PER_TEAM = 3;
export const POINTS_PER_TRANSFER = 4;

export const squadLayout = {
    GK: [
        { x: 40, y: 12 },
        { x: 60, y: 12 },
    ],
    DEF: [
        { x: 15, y: 35 },
        { x: 32.5, y: 35 },
        { x: 50, y: 35 },
        { x: 67.5, y: 35 },
        { x: 85, y: 35 },
    ],
    MID: [
        { x: 10, y: 61 },
        { x: 30, y: 61 },
        { x: 50, y: 61 },
        { x: 70, y: 61 },
        { x: 90, y: 61 },
    ],
    FWD: [
        { x: 30, y: 87 },
        { x: 50, y: 87 },
        { x: 70, y: 87 },
    ],
};

export const createPlaceholderSquad = (t: TFunction): TransferPlayer[] => {
    const placeholders: TransferPlayer[] = [];
    let idCounter = 0;
    (Object.keys(squadLayout) as Array<keyof typeof squadLayout>).forEach(
        (pos) => {
            squadLayout[pos].forEach(() => {
                placeholders.push({
                    id: `placeholder-${idCounter++}`,
                    name: t("transferManager.placeholders.addPlayer", {
                        position: pos,
                    }),
                    position: pos,
                    team: "",
                    price: 0,
                });
            });
        }
    );
    return placeholders;
};

export const calculateSquadValue = (squad: TransferPlayer[]): number =>
    squad.reduce((total, player) => total + player.price, 0);

export const validateTransfer = (
    playerOut: TransferPlayer,
    playerIn: TransferPlayer,
    currentSquad: TransferPlayer[],
    balance: number,
    t: TFunction
): { valid: boolean; message: string } => {
    if (playerIn.position !== playerOut.position) {
        return {
            valid: false,
            message: t("transferManager.validation.samePosition"),
        };
    }
    const priceDifference = playerIn.price - playerOut.price;
    if (priceDifference > balance) {
        return {
            valid: false,
            message: t("transferManager.validation.notEnoughFunds", {
                amount: priceDifference.toFixed(1),
            }),
        };
    }
    const newSquad = currentSquad.filter((p) => p.id !== playerOut.id);
    newSquad.push(playerIn);
    const teamCounts = newSquad.reduce((acc, p) => {
        if (p.team) acc[p.team] = (acc[p.team] || 0) + 1;
        return acc;
    }, {} as { [key: string]: number });

    if (teamCounts[playerIn.team] > MAX_PLAYERS_PER_TEAM) {
        return {
            valid: false,
            message: t("transferManager.validation.maxPlayers", {
                max: MAX_PLAYERS_PER_TEAM,
                team: playerIn.team,
            }),
        };
    }
    return { valid: true, message: "" };
};

import { Head } from "@inertiajs/react";
import Layout from "../../Components/Layout";
import { PageProps } from "../../types/inertia";
import { useTenantName } from "../../hooks/useTenant";
import HeroSection from "@/Components/ui/HeroSection";
import MyTeamManager from "@/Components/fantasy/MyTeamManager/index";
import { useTranslation } from "react-i18next";
import {
    TeamData,
    SquadPlayer,
    FantasyTeam,
    CurrentGameweek,
    Booster,
} from "../../types/fantasy";

interface GamePageProps extends PageProps {
    teamData: TeamData;
    squadPlayers: SquadPlayer[];
    startingPlayerIds: number[];
    fantasyTeam: FantasyTeam;
    currentGameweek: CurrentGameweek;
    boosters: Booster[];
}

/**
 * Page component for the "My Team" page.
 *
 * This component displays the MyTeamManager component, which allows the user to manage their fantasy team.
 *
 * @param {GamePageProps} props - The props for the Game component. This includes the team data, squad players, starting player IDs, fantasy team, and the current gameweek.
 * @returns {JSX.Element} - The JSX element for the Game component.
 */
export default function Game(props: GamePageProps) {
    const {
        teamData,
        squadPlayers,
        startingPlayerIds,
        fantasyTeam,
        currentGameweek,
        boosters,
    } = props;
    const tenantName = useTenantName();
    const { t } = useTranslation();

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - ${t("My Team")}`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Header */}
                <HeroSection
                    title={t("My Team")}
                    subtitle={t("Manage your squad and formation")}
                />
                {/* My Team Manager */}
                <MyTeamManager
                    teamData={teamData}
                    squadPlayers={squadPlayers}
                    startingPlayerIds={startingPlayerIds}
                    fantasyTeam={fantasyTeam}
                    currentGameweek={currentGameweek}
                    boosters={boosters}
                />
            </div>
        </Layout>
    );
}

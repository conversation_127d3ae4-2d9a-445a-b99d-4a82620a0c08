import { Head, router } from "@inertiajs/react";
import { useState } from "react";
import Layout from "../../Components/Layout";
import { PageProps } from "../../types/inertia";
import { useTenantName } from "../../hooks/useTenant";
import HeroSection from "@/Components/ui/HeroSection";
import PointsManager from "@/Components/fantasy/PointsManager";
import PlayerPerformanceModal from "@/Components/fantasy/PlayerPerformanceModal";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/Components/ui/dropdown-menu";
import { Button } from "@/Components/ui/button";
import FantasyStatsGrid from "@/Components/fantasy/FantasyStatsGrid";

import { SquadPlayer, FantasyTeam, Gameweek, FantasyStats, PlayerPerformanceData } from "@/types/fantasy";
import { t } from "i18next";

interface Team {
    id: number;
    name: string;
    short_name: string;
    logo?: string;
    shirt?: {
        base_color?: string;
    };
}

interface Fixture {
    id: number;
    gameweek_id: number;
    homeTeam: Team;
    awayTeam: Team;
    game_date: string;
    home_score?: number | null;
    away_score?: number | null;
    status: "scheduled" | "in_progress" | "finished" | "postponed" | "canceled";
    stadium?: {
        id: number;
        name: string;
    };
    referee?: {
        id: number;
        name: string;
    };
}

interface PointsPageProps extends PageProps {
    squadPlayers: SquadPlayer[];
    startingPlayerIds: number[];
    fantasyTeam: FantasyTeam;
    gameweeks: Gameweek[];
    selectedGameweek: Gameweek;
    fixtures: Fixture[];
    fantasyStats: FantasyStats;
    playerPerformances: Record<number, PlayerPerformanceData>;
}

export default function Points(props: PointsPageProps) {
    const {
        squadPlayers,
        startingPlayerIds,
        fantasyTeam,
        gameweeks,
        selectedGameweek,
        fixtures,
        fantasyStats,
        playerPerformances,
    } = props;
    const tenantName = useTenantName();
    const [modalState, setModalState] = useState<{
        playerId: number | null;
        isOpen: boolean;
    }>({
        playerId: null,
        isOpen: false,
    });

    const handleGameweekChange = (gameweekId: number) => {
        router.get(route("game.points", { gameweek_id: gameweekId }));
    };

    const handlePlayerClick = (playerId: number) => {
        setModalState({
            playerId,
            isOpen: true,
        });
    };

    const handleCloseModal = () => {
        setModalState({
            playerId: null,
            isOpen: false,
        });
    };

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - Points`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                <HeroSection
                    title={t("points")}
                    subtitle={`View your team's performance for ${selectedGameweek.name}`}
                />

                <FantasyStatsGrid stats={fantasyStats} />

                <div className="my-4">
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline">
                                {selectedGameweek.name}
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            {gameweeks.map((gw) => (
                                <DropdownMenuItem
                                    key={gw.id}
                                    onSelect={() => handleGameweekChange(gw.id)}
                                >
                                    {gw.name}
                                </DropdownMenuItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>

                <PointsManager
                    squadPlayers={squadPlayers}
                    startingPlayerIds={startingPlayerIds}
                    fantasyTeam={fantasyTeam}
                    fixtures={fixtures}
                    onPlayerClick={handlePlayerClick}
                />

                <PlayerPerformanceModal
                    playerId={modalState.playerId}
                    isOpen={modalState.isOpen}
                    onClose={handleCloseModal}
                    playerPerformances={playerPerformances}
                />
            </div>
        </Layout>
    );
}

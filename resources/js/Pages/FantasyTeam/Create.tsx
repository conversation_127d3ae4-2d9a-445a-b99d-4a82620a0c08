import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useState } from "react";
import { Head, useForm } from "@inertiajs/react";
import { useTranslation } from "react-i18next";
import Layout from "../../Components/Layout";
import FootballShirtPreview from "../../Components/fantasy/FootballShirtPreview";
import { Button } from "../../Components/ui/button";
import { Input } from "../../Components/ui/input";
import { Label } from "../../Components/ui/label";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "../../Components/ui/card";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "../../Components/ui/select";
import { PageProps, Competition, Season, Team, ShirtTypeOption } from "../../types";
import HeroSection from "@/Components/ui/HeroSection";
import * as Form from "@radix-ui/react-form";
import * as Toast from "@radix-ui/react-toast";
import ColorPicker from "../../Components/ui/ColorPicker";

interface CreateFantasyTeamProps extends PageProps {
    competition: Competition & {
        currentSeason: Season & {
            teams: Team[];
        };
    };
    season: Season;
    shirtTypes: ShirtTypeOption[];
}

export default function Create({
    competition,
    teams,
    season,
    shirtTypes,
    ...pageProps
}: CreateFantasyTeamProps) {
    const { t, i18n } = useTranslation();

    // Force re-render when language changes
    const [, forceUpdate] = useState({});

    React.useEffect(() => {
        const handleLanguageChange = () => {
            forceUpdate({});
        };

        i18n.on('languageChanged', handleLanguageChange);

        return () => {
            i18n.off('languageChanged', handleLanguageChange);
        };
    }, [i18n]);

    const { data, setData, post, processing, errors } = useForm({
        name: "",
        shirt_type: shirtTypes[0]?.value || "striped",
        shirt_color: "#FF0000",
        strip_color: "#FFFFFF",
        favorite_team: "",
    });

    const [selectedShirtType, setSelectedShirtType] = useState(
        shirtTypes[0]?.value || "striped"
    );
    const [toastMessage, setToastMessage] = useState('');
    const [toastType, setToastType] = useState<'success' | 'error'>('error');
    const [showToast, setShowToast] = useState(false);

    const showToastMessage = (message: string, type: 'success' | 'error' = 'error') => {
        setToastMessage(message);
        setToastType(type);
        setShowToast(true);
    };

    const validateField = (name: string, value: string) => {
        switch (name) {
            case 'name':
                if (value.length < 2) {
                    showToastMessage(t('fantasyTeam.validation.teamNameTooShort'));
                    return false;
                }
                if (value.length > 50) {
                    showToastMessage(t('fantasyTeam.validation.teamNameTooLong'));
                    return false;
                }
                break;
            case 'shirt_color':
            case 'strip_color':
                const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
                if (!hexPattern.test(value)) {
                    showToastMessage(t('fantasyTeam.validation.invalidHexColor'));
                    return false;
                }
                break;
        }
        return true;
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        // Validate all fields
        const isValid = Object.entries(data).every(([key, value]) =>
            validateField(key, value)
        );
        if (isValid) {
            post(route("fantasy-team.store"), {
                onSuccess: () => {
                    showToastMessage(t('fantasyTeam.messages.teamCreatedSuccess'), 'success');
                },
                onError: (errors) => {
                    const firstError = Object.values(errors)[0];
                    if (firstError) {
                        showToastMessage(firstError as string, 'error');
                    }
                }
            });
        }
    };

    return (
        <Layout {...pageProps}>
            <Head title={t('fantasyTeam.title')} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                <HeroSection
                    title={t('fantasyTeam.heroTitle')}
                    subtitle={`${t('fantasyTeam.heroSubtitle')} ${competition.name} - ${season.name}`}
                />
            </div>
            <div className="max-w-2xl mx-auto px-4">
                <Card className="shadow-xl">
                    <CardHeader>
                        <CardTitle className="text-2xl text-center">
                            {t('fantasyTeam.teamSetup')}
                        </CardTitle>
                        <CardDescription className="text-center">
                            {t('fantasyTeam.teamSetupDescription')}
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Form.Root onSubmit={submit} className="space-y-6">
                            <div className="space-y-4">
                                {/* Team Name */}
                                <Form.Field name="name" className="space-y-2">
                                    <Form.Label asChild>
                                        <Label className="mb-2">{t('fantasyTeam.teamName')}</Label>
                                    </Form.Label>
                                    <Form.Control asChild>
                                        <Input
                                            placeholder={t('fantasyTeam.teamNamePlaceholder')}
                                            value={data.name}
                                            onChange={(e) => setData("name", e.target.value)}
                                            className={errors.name ? 'border-red-500' : ''}
                                            required
                                        />
                                    </Form.Control>
                                    <Form.Message match="valueMissing" className="text-sm text-red-600">
                                        {t('fantasyTeam.validation.teamNameRequired')}
                                    </Form.Message>
                                    {errors.name && (
                                        <p className="text-sm text-red-600">{errors.name}</p>
                                    )}
                                </Form.Field>

                                {/* Favorite Team */}
                                <div className="space-y-2">
                                    <Label className="mb-2">{t('fantasyTeam.favoriteTeam')}</Label>
                                    <Select
                                        value={data.favorite_team}
                                        onValueChange={(value) => setData("favorite_team", value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder={t('fantasyTeam.favoriteTeamPlaceholder')} />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {teams?.map((team) => (
                                                <SelectItem
                                                    key={team.id}
                                                    value={team.name}
                                                >
                                                    {team.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Kit Customization */}
                                <div className="space-y-6">
                                    <div className="text-center">
                                        <h3 className="text-lg font-semibold mb-4">
                                            {t('fantasyTeam.kitCustomization')}
                                        </h3>
                                    </div>

                                    {/* Kit Type */}
                                    <div className="space-y-2">
                                        <Label className="mb-2">{t('fantasyTeam.kitType')}</Label>
                                        <Select
                                            value={selectedShirtType}
                                            onValueChange={(value) => {
                                                setSelectedShirtType(value);
                                                setData("shirt_type", value);
                                            }}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder={t('fantasyTeam.kitTypePlaceholder')} />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {shirtTypes.map((type) => (
                                                    <SelectItem
                                                        key={type.value}
                                                        value={type.value}
                                                    >
                                                        {t(type.label)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <ColorPicker
                                            name="shirt_color"
                                            label={t('fantasyTeam.primaryColor')}
                                            value={data.shirt_color}
                                            onChange={(e) =>
                                                validateField(
                                                    "shirt_color",
                                                    e.target.value
                                                ) &&
                                                setData(
                                                    "shirt_color",
                                                    e.target.value
                                                )
                                            }
                                            error={errors.shirt_color}
                                            placeholder="#FF0000"
                                        />
                                        <ColorPicker
                                            name="strip_color"
                                            label={t('fantasyTeam.secondaryColor')}
                                            value={data.strip_color}
                                            onChange={(e) =>
                                                validateField(
                                                    "strip_color",
                                                    e.target.value
                                                ) &&
                                                setData(
                                                    "strip_color",
                                                    e.target.value
                                                )
                                            }
                                            error={errors.strip_color}
                                            placeholder="#FFFFFF"
                                        />
                                    </div>

                                    {/* Kit Preview */}
                                    <div className="space-y-3">
                                        <Label className="mb-2">{t('fantasyTeam.kitPreview')}</Label>
                                        <div className="flex justify-center">
                                            <FootballShirtPreview
                                                shirtType={
                                                    selectedShirtType as
                                                    | "striped"
                                                    | "filled"
                                                    | "halved"
                                                    | "sash"
                                                    | "hoops"
                                                    | "checkered"
                                                }
                                                primaryColor={data.shirt_color}
                                                secondaryColor={data.strip_color}
                                                size="medium"
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Submit Button */}
                                <div className="pt-6">
                                    <Form.Submit asChild>
                                        <Button
                                            type="submit"
                                            disabled={processing}
                                            className="w-full text-lg py-3"
                                        >
                                            {processing
                                                ? t('fantasyTeam.creatingTeam')
                                                : t('fantasyTeam.createTeam')}
                                        </Button>
                                    </Form.Submit>
                                </div>
                            </div>
                        </Form.Root>
                    </CardContent>
                </Card>

                {/* Info Card */}
                <Card className="mt-5 mb-5">
                    <CardContent className="pt-6">
                        <div className="text-center text-sm text-gray-600">
                            <p className="mb-2">
                                <strong>{t('fantasyTeam.startingBudget')}</strong> 100
                            </p>
                            <p>
                                {t('fantasyTeam.changeSettingsLater')}
                            </p>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Toast */}
            <Toast.Provider swipeDirection="right">
                <Toast.Root
                    className={`fixed top-4 right-4 p-4 rounded-lg shadow-lg ${toastType === 'success'
                        ? 'bg-green-600 text-white'
                        : 'bg-red-600 text-white'
                        }`}
                    open={showToast}
                    onOpenChange={setShowToast}
                >
                    <Toast.Title>{toastMessage}</Toast.Title>
                </Toast.Root>
                <Toast.Viewport />
            </Toast.Provider>
        </Layout>
    );
}
import { Head, router } from "@inertiajs/react";
import Layout from "../../Components/Layout";
import { PageProps } from "../../types/inertia";
import HeroSection from "../../Components/ui/HeroSection";
import { useTenantName } from "@/hooks/useTenant";
import TransferManager from "@/Components/fantasy/TransferManager";
import { TransferPlayer, AvailablePlayersResponse } from "@/types/fantasy";
import { useTranslation } from "react-i18next";
import { useState, useCallback, useEffect } from 'react';
import axios from "axios";
import { Paginated } from "@/types/pagination";

interface Player {
    id: number | string;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    team: string;
    price: number;
    is_captain?: boolean;
    is_vice_captain?: boolean;
    // Optional enriched fields
    name_ar?: string;
    birthday?: string;
    country?: string;
    image?: string;
}

interface SquadPageProps extends PageProps {
    competition: any;
    season: any;
    gameweek: any;
    fantasyTeam: any;
    availablePlayers: Paginated<any>;
    currentSquad: any[];
    budget: number;
}

export default function Squad(props: SquadPageProps) {
    const tenantName = useTenantName();
    const { t, i18n } = useTranslation();
    const { currentSquad, budget } = props;

    console.log('Squad component mounted with props:', props);

    // AJAX state for available players
    const [availablePlayers, setAvailablePlayers] = useState<Paginated<any>>({
        data: [],
        current_page: 1,
        first_page_url: '',
        from: 0,
        last_page: 1,
        last_page_url: '',
        links: [],
        next_page_url: null,
        path: '',
        per_page: 20,
        prev_page_url: null,
        to: 0,
        total: 0,
    });
    const [isLoadingPlayers, setIsLoadingPlayers] = useState(false);

    // Function to fetch available players via AJAX
    const fetchAvailablePlayers = useCallback(async (filters: any = {}) => {
        console.log('fetchAvailablePlayers called with filters:', filters);
        setIsLoadingPlayers(true);
        try {
            console.log('Making AJAX request to /fantasy-team/available-players');
            const response = await axios.get('/fantasy-team/available-players', {
                params: {
                    ...filters,
                    locale: i18n.language,
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
            });
            console.log('AJAX response received:', response.data);
            setAvailablePlayers(response.data);
        } catch (error: any) {
            console.error('Failed to fetch available players:', error);
            console.error('Error details:', error.response?.data);
        } finally {
            setIsLoadingPlayers(false);
        }
    }, [i18n.language]);

    // Fetch initial page on mount to populate market with enriched TransferPlayers
    useEffect(() => {
        fetchAvailablePlayers();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fetchAvailablePlayers]);

    console.log('available  ', availablePlayers);
    // Helper to build safe team_data
    const buildTeamData = (src: any) => ({
        logo: src?.logo || src?.team_data?.logo || "",
        shirt: {
            base_color: src?.shirt?.base_color || src?.team_data?.shirt?.base_color || "#2d3748",
            pattern_type: src?.shirt?.pattern_type || src?.team_data?.shirt?.pattern_type || "none",
            pattern_color: src?.shirt?.pattern_color || src?.team_data?.shirt?.pattern_color || "#1a202c",
        },
    });

    // Transform backend data to match TransferPlayer interface
    const transformedAvailablePlayers: TransferPlayer[] = (availablePlayers.data || []).map(
        (p: any): TransferPlayer => ({
            id: p.id,
            name: p.name,
            position: p.position as "GK" | "DEF" | "MID" | "FWD",
            team: p.team || t("fantasyTeam.squad.unknown"),
            price: p.price || 5,
            team_data: buildTeamData(p.team_data || p),
            // Optional enriched fields
            name_ar: p.name_ar,
            birthday: p.birthday,
            country: p.country,
            image: p.image,
        })
    );

    console.log('transformed available players ', transformedAvailablePlayers);
    // Keep pagination metadata but replace data array with transformed players and ensure teams list exists
    const availablePlayersForTM: AvailablePlayersResponse = {
        ...(availablePlayers as any),
        data: transformedAvailablePlayers,
        teams: (availablePlayers as any).teams || [],
    };
    // Transform current squad data if exists to TransferPlayer[]
    const transformedCurrentSquad: TransferPlayer[] = currentSquad.map((fp: any): TransferPlayer => {
        const team0 = fp.player?.teams?.[0] || {};
        return {
            id: fp.player.id,
            name: fp.player.name,
            position: fp.player.position as "GK" | "DEF" | "MID" | "FWD",
            team: team0.name || t("fantasyTeam.squad.unknown"),
            price: (fp.player.market_value || 5000000) / 1000000,
            team_data: buildTeamData({
                logo: team0.logo,
                shirt: team0.shirt,
            }),
            // Optional enriched fields
            is_captain: fp.fantasyTeamLineups?.some((l: any) => l.is_captain) || false,
            is_vice_captain: fp.fantasyTeamLineups?.some((l: any) => l.is_vice_captain) || false,
            name_ar: fp.player.name_ar,
            birthday: fp.player.birthday,
            country: fp.player.country,
            image: fp.player.image,
        };
    });

    const handleSquadSubmit = (squadData: {
        players: Player[];
        captain: Player | null;
        viceCaptain: Player | null;
        budget: number;
    }) => {
        // Transform data for backend submission to match controller expectations
        const selectedPlayers = squadData.players.map((player) => player.id);

        // For now, assume first 11 players are in lineup (this should be enhanced later)
        const lineupPlayers = squadData.players
            .slice(0, 11)
            .map((player) => player.id);

        const submissionData = {
            selected_players: selectedPlayers,
            lineup_players: lineupPlayers,
            captain_id: squadData.captain?.id || null,
            vice_captain_id: squadData.viceCaptain?.id || null,
        };
        // Submit to backend
        router.post(route("fantasy-team.squad.store"), submissionData, {
            onSuccess: () => {
                // Redirect to home or next step after successful squad creation
                router.visit(route("home"));
            },
            onError: (errors) => {
                console.error("Squad submission failed:", errors);
            },
        });
    };

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - ${t("fantasyTeam.squad.squad")}`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Header */}
                <HeroSection
                    title={t("fantasyTeam.squad.createYourSquad")}
                    subtitle={t(
                        "fantasyTeam.squad.buildYourDreamTeam"
                    )}
                />
            </div>
            <TransferManager
                isCreationMode={true}
                initialSquadData={transformedCurrentSquad}
                availablePlayers={availablePlayersForTM}
                initialBudget={budget}
                onSquadSubmit={handleSquadSubmit}
                onFetchPlayers={fetchAvailablePlayers}
                isLoadingPlayers={isLoadingPlayers}
            />
        </Layout>
    );
}

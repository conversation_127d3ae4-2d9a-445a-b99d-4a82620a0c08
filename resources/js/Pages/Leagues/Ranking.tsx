import { Head, useForm, usePage } from '@inertiajs/react';
import Layout from '@/Components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import LeaderboardRow from '@/Components/fantasy/LeaderboardRow';
import LeagueNews from '@/Pages/Leagues/LeagueNews';
import { PageProps } from '@/types';
import { League, Ranking } from '@/types/fantasy';
import { useTranslation } from 'react-i18next';
import { Badge } from '@/Components/ui/badge';
import { Calendar, Crown, Award, Medal, Trophy } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Paginated } from '@/types/pagination';
import { Button } from "@/Components/ui/button";
import { useState } from 'react';
import axios from 'axios';

interface FantasyTransfer {
    id: number;
    fantasy_team_id: number;
    gameweek_id: number;
    player_in_id: number | null;
    player_out_id: number | null;
    transfer_cost: string;
    is_free_transfer: boolean;
    transfer_type: 'in' | 'out' | 'swap';
    tenant_id: number;
    created_at: string;
    updated_at: string;
    // Relations that might be loaded
    player_in?: {
        name: string;
        position: string;
        team: string;
        image: string;
    };
    player_out?: {
        name: string;
        position: string;
        team: string;
        image: string;
    };
    fantasy_team?: {
        name: string;
        user?: {
            name: string;
            avatar: string;
        };
    };
}

interface RankingPageProps extends PageProps {
    league: League;
    standings: Paginated<Ranking>;
    topThree: Ranking[];
    transfers: Paginated<FantasyTransfer>;
    seasons: { id: string; name: string }[];
    phases: { id: string; name: string }[];
    gameweeks: { id: string; name: string }[];
    selectedSeasonId: string;
    selectedPhaseId: string;
    selectedGameweekId: string;
}

export default function RankingPage({
    league,
    standings: initialStandings,
    topThree,
    transfers,
    seasons,
    phases,
    gameweeks,
    selectedSeasonId,
    selectedPhaseId,
    selectedGameweekId,
    ...props
}: RankingPageProps) {
    const { t, i18n } = useTranslation();
    const { app } = usePage<PageProps>().props;
    const [standings, setStandings] = useState<Paginated<Ranking>>(initialStandings);
    const [standingsLoading, setStandingsLoading] = useState(false);
    const { get, processing } = useForm();

    const getLogoUrl = () => {
        if (!league.logo) return '';
        // Remove any leading slashes from the logo path
        const cleanLogoPath = league.logo.startsWith('/') ? league.logo.substring(1) : league.logo;
        return `${app.url}/storage/${cleanLogoPath}`;
    };

    const handleSelectChange = (type: 'season' | 'phase' | 'gameweek', value: string) => {
        const params: any = {
            season_id: type === 'season' ? value : selectedSeasonId,
        };

        if (type === 'phase') {
            params.phase_id = value;
        } else if (type === 'gameweek') {
            params.phase_id = selectedPhaseId;
            params.gameweek_id = value;
        }

        get(route('leagues.ranking', { league: league.id, ...params, locale: i18n.language }));
    };

    const handleStandingsPagination = async (page: number) => {
        try {
            setStandingsLoading(true);
            
            // Extract league ID from current URL path
            const pathSegments = window.location.pathname.split('/');
            const leagueIndex = pathSegments.indexOf('leagues');
            const leagueId = pathSegments[leagueIndex + 1];
            
            
            // Make AJAX request for standings only
            const response = await axios.get(`/leagues/${leagueId}/standings`, {
                params: { 
                    page,
                    season_id: selectedSeasonId,
                    phase_id: selectedPhaseId,
                    gameweek_id: selectedGameweekId,
                    locale: i18n.language
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });
            
            
            setStandings(response.data);
        } catch (error) {

        } finally {
            setStandingsLoading(false);
        }
    };

    return (
        <Layout {...props}>
            <Head title={`${t('ranking.title')} ${league.name}`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Header */}
                <Card>
                    <CardHeader>
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                            <div>
                                <CardTitle className="flex items-center gap-2">
                                    {league.logo && (
                                        <img 
                                            src={getLogoUrl()} 
                                            alt={`${league.name} logo`} 
                                            className="w-8 h-8 rounded-full object-cover"
                                            onError={(e) => {
                                                const target = e.target as HTMLImageElement;
                                                target.src = `${app.url}/images/default-league-logo.png`;
                                                target.onerror = null;
                                            }}
                                        />
                                    )}
                                    {t('ranking.leagueRanking')}: {league.name}
                                </CardTitle>
                                {league.invite_code && (
                                    <p className="text-sm text-gray-500 dark:text-gray-400 font-mono mt-2">
                                        {t('leaderboard.create.inviteCode')} {league.invite_code}
                                    </p>
                                )}
                            </div>
                            <div className="flex items-center gap-2 bg-primary/10 px-4 py-2 rounded-lg border border-primary/20">
                                <Calendar className="w-5 h-5 text-primary" />
                                <div>
                                    <p className="text-xs font-medium text-muted-foreground">{league.season.current_season_phase?.name}</p>
                                    <p className="font-bold text-primary">
                                        {league.season.current_gameweek?.[0]?.name || 'N/A'}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </CardHeader>
                </Card>

                {/* Top 3 Teams - Full Width */}
                {topThree.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>{t('ranking.topThreeTeams')}</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {/* 2nd Place */}
                                {topThree[1] && (
                                    <div className="flex flex-col items-center">
                                        <div className="relative w-full">
                                            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                                <div className="bg-slate-600 text-white text-xs font-bold px-3 py-1 rounded-full flex items-center gap-1">
                                                    <Medal className="w-3 h-3" /> {t('ranking.second')}
                                                </div>
                                            </div>
                                            <div className="border-slate-200 bg-slate-50 pt-6 shadow-sm rounded-lg p-4">
                                                <div className="text-center">
                                                    <div className="w-16 h-16 rounded-full bg-slate-100 border-2 border-slate-300 flex items-center justify-center mx-auto mb-3">
                                                        <span className="text-2xl font-bold text-slate-700">2</span>
                                                    </div>
                                                    <h3 className="font-bold text-slate-800">{topThree[1].teamName}</h3>
                                                    <p className="text-sm text-slate-600">{topThree[1].name}</p>
                                                    <div className="mt-3 text-lg font-bold text-slate-900">
                                                        {topThree[1].points} {t('ranking.pts')}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* 1st Place */}
                                {topThree[0] && (
                                    <div className="flex flex-col items-center">
                                        <div className="relative w-full">
                                            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                                <div className="bg-amber-600 text-white text-xs font-bold px-3 py-1 rounded-full flex items-center gap-1">
                                                    <Crown className="w-3 h-3" /> {t('ranking.first')}
                                                </div>
                                            </div>
                                            <div className="border-amber-100 bg-amber-50 pt-6 shadow-sm rounded-lg p-4">
                                                <div className="text-center">
                                                    <div className="w-16 h-16 rounded-full bg-amber-100 border-2 border-amber-300 flex items-center justify-center mx-auto mb-3">
                                                        <span className="text-2xl font-bold text-amber-700">1</span>
                                                    </div>
                                                    <h3 className="font-bold text-amber-800">{topThree[0].teamName}</h3>
                                                    <p className="text-sm text-amber-600">{topThree[0].name}</p>
                                                    <div className="mt-3 text-lg font-bold text-amber-900">
                                                        {topThree[0].points} {t('ranking.pts')}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* 3rd Place */}
                                {topThree[2] && (
                                    <div className="flex flex-col items-center">
                                        <div className="relative w-full">
                                            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                                <div className="bg-stone-600 text-white text-xs font-bold px-3 py-1 rounded-full flex items-center gap-1">
                                                    <Award className="w-3 h-3" /> {t('ranking.third')}
                                                </div>
                                            </div>
                                            <div className="border-stone-200 bg-stone-50 pt-6 shadow-sm rounded-lg p-4">
                                                <div className="text-center">
                                                    <div className="w-16 h-16 rounded-full bg-stone-100 border-2 border-stone-300 flex items-center justify-center mx-auto mb-3">
                                                        <span className="text-2xl font-bold text-stone-700">3</span>
                                                    </div>
                                                    <h3 className="font-bold text-stone-800">{topThree[2].teamName}</h3>
                                                    <p className="text-sm text-stone-600">{topThree[2].name}</p>
                                                    <div className="mt-3 text-lg font-bold text-stone-900">
                                                        {topThree[2].points} {t('ranking.pts')}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Main Content - League Standings and Transfer Content side by side */}
                <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
                    {/* League Standings - Takes 3/5 of the width */}
                    <div className="lg:col-span-3">
                        <Card>
                            <CardHeader className="bg-muted/50">
                                <CardTitle className="flex items-center gap-2">
                                    {t('ranking.leagueStandings')}
                                    <Badge variant="outline" className="ml-auto">
                                        {standings.total} {t('ranking.teams')}
                                    </Badge>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-0">
                                <div className="space-y-3 p-6">
                                    <div className="flex items-center gap-4 mb-6">
                                        {/* Season Dropdown */}
                                        <Select
                                            value={selectedSeasonId}
                                            onValueChange={value => handleSelectChange('season', value)}
                                            disabled={processing || seasons.length === 0}
                                        >
                                            <SelectTrigger className="w-[180px]">
                                                <SelectValue placeholder={t('ranking.selectSeason')} />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {seasons.map(season => (
                                                    <SelectItem key={season.id} value={season.id}>
                                                        {season.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>

                                        {/* Phase Dropdown */}
                                        <Select
                                            value={selectedPhaseId}
                                            onValueChange={value => handleSelectChange('phase', value)}
                                            disabled={processing || !selectedSeasonId || phases.length === 0}
                                        >
                                            <SelectTrigger className="w-[180px]">
                                                <SelectValue placeholder={t('ranking.selectPhase')} />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {phases.map(phase => (
                                                    <SelectItem key={phase.id} value={phase.id}>
                                                        {phase.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>

                                        {/* Gameweek Dropdown */}
                                        <Select
                                            value={selectedGameweekId}
                                            onValueChange={value => handleSelectChange('gameweek', value)}
                                            disabled={processing || !selectedPhaseId || gameweeks.length === 0}
                                        >
                                            <SelectTrigger className="w-[180px]">
                                                <SelectValue placeholder={t('ranking.selectGameweek')} />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {gameweeks.map(gw => (
                                                    <SelectItem key={gw.id} value={gw.id}>
                                                        {gw.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Table Header */}
                                    <div className="grid grid-cols-12 gap-4 px-4 py-3 text-sm font-medium text-muted-foreground border-b">
                                        <div className="col-span-1">{t('ranking.rank')}</div>
                                        <div className="col-span-7">{t('ranking.team')}</div>
                                        <div className="col-span-2 text-center"></div>
                                        <div className="col-span-2 text-right">{t('ranking.points')}</div>
                                    </div>

                                    {/* Rows */}
                                    {standings.data.length === 0 ? (
                                        <div className="text-center py-8">
                                            <Trophy className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                                            <p className="text-sm text-muted-foreground">
                                                {t(
                                                    "ranking.noRankingsMessage",
                                                    "No rankings available for this period. Be the first to make your mark!"
                                                )}
                                            </p>
                                        </div>
                                    ) : (
                                        standings.data.map((ranking, index) => (
                                            <div
                                                key={index}
                                                className={`transition-all hover:bg-muted/50 rounded-lg ${index < 3 ? "bg-muted/30" : ""}`}
                                            >
                                                <LeaderboardRow
                                                    rank={ranking.rank}
                                                    name={ranking.name}
                                                    teamName={ranking.teamName}
                                                    points={ranking.points}
                                                    previousRank={ranking.previous_rank}
                                                    highlight={index < 3}
                                                />
                                            </div>
                                        ))
                                    )}
                                </div>

                                {/* Pagination */}
                                {standings.last_page > 1 && (
                                    <div className="flex items-center justify-between mt-6 mb-4 pt-4 border-t px-4">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleStandingsPagination(standings.current_page - 1)}
                                            disabled={standings.current_page <= 1 || standingsLoading}
                                            className="flex items-center gap-2"
                                        >
                                            {standingsLoading && standings.current_page > 1 ? '...' : t('ranking.previous')}
                                        </Button>
                                        
                                        <div className="flex items-center gap-2">
                                            <span className="text-sm text-muted-foreground">
                                                {t('ranking.pageXOfY', { current: standings.current_page, total: standings.last_page })}
                                            </span>
                                        </div>
                                        
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleStandingsPagination(standings.current_page + 1)}
                                            disabled={standings.current_page >= standings.last_page || standingsLoading}
                                            className="flex items-center gap-2"
                                        >
                                            {standingsLoading && standings.current_page < standings.last_page ? '...' : t('ranking.next')}
                                        </Button>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        </div>

                    {/* League News */}
                    <div className="lg:col-span-2">
                        <LeagueNews 
                            transfers={transfers} 
                            title={t('leagueNews.leagueTransferNews')} 
                            selectedPhaseId={selectedPhaseId}
                            selectedGameweekId={selectedGameweekId}
                        />
                    </div>

                </div>
            </div>
        </Layout>
    );
}

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import LeaderboardRow from '@/Components/fantasy/LeaderboardRow';
import { useTranslation } from 'react-i18next';
import { Calendar, Trophy, Globe } from 'lucide-react';
import axios from 'axios';
import { Paginated } from '@/types/pagination';

interface GlobalRanking {
    rank: number;
    previous_rank: number | undefined;
    name: string;
    teamName: string;
    points: number;
}

interface GlobalRankingProps {
    league: {
        id: number;
        name: string;
    };
    phases: { id: string; name: string }[];
    gameweeks: { id: string; name: string }[];
    selectedPhaseId: string;
    selectedGameweekId: string;
}

export default function GlobalRanking({
    league,
    phases,
    gameweeks,
    selectedPhaseId,
    selectedGameweekId,
}: GlobalRankingProps) {
    const { t, i18n } = useTranslation();
    const [globalRankings, setGlobalRankings] = useState<Paginated<GlobalRanking>>({
        data: [],
        current_page: 1,
        last_page: 1,
        per_page: 20,
        total: 0,
        from: 0,
        to: 0,
        links: [],
        first_page_url: '',
        last_page_url: '',
        next_page_url: null,
        prev_page_url: null,
        path: ''
    });
    const [loading, setLoading] = useState(false);
    const [loadingGameweeks, setLoadingGameweeks] = useState(false);
    const [currentPhaseId, setCurrentPhaseId] = useState(selectedPhaseId);
    const [currentGameweekId, setCurrentGameweekId] = useState(selectedGameweekId);
    const [currentGameweeks, setCurrentGameweeks] = useState(gameweeks);

    // Fetch global rankings data
    const fetchGlobalRankings = async (page: number = 1, phaseId?: string, gameweekId?: string) => {
        try {
            setLoading(true);

            const url = `/leagues/${league.id}/global-rankings`;
            const params = {
                page,
                phase_id: phaseId || currentPhaseId,
                gameweek_id: gameweekId || currentGameweekId,
                locale: i18n.language
            };

          
            const response = await axios.get(url, {
                params,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });

            setGlobalRankings(response.data);
        } catch (error: any) {
            console.error('❌ Error fetching global rankings:', error);
            console.error('  Error message:', error.message);
            console.error('  Error response:', error.response);
            console.error('  Error status:', error.response?.status);
            console.error('  Error data:', error.response?.data);
            console.error('  Request URL:', error.config?.url);
            console.error('  Request params:', error.config?.params);
        } finally {
            setLoading(false);
        }
    };

    // Initial load
    useEffect(() => {
        fetchGlobalRankings();
    }, []);

    // Fetch gameweeks for a specific phase
    const fetchGameweeks = async (phaseId: string) => {
        console.log('Fetching gameweeks for phase:', phaseId);
        try {
            setLoadingGameweeks(true);
            const response = await axios.get('/leagues/gameweeks', {
                params: {
                    phase_id: phaseId,
                    locale: i18n.language
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });

            console.log('Gameweeks response:', response.data);
            if (response.data.gameweeks && Array.isArray(response.data.gameweeks)) {
                setCurrentGameweeks(response.data.gameweeks);
                console.log('Updated gameweeks:', response.data.gameweeks);
                return response.data.selectedGameweekId;
            } else {
                console.error('Invalid gameweeks data received:', response.data);
                return null;
            }
        } catch (error: any) {
            console.error('Error fetching gameweeks:', error);
            if (error.response) {
                console.error('Error response:', error.response.data);
                console.error('Error status:', error.response.status);
            }
            return null;
        } finally {
            setLoadingGameweeks(false);
        }
    };

    // Handle phase change
    const handlePhaseChange = async (phaseId: string) => {
        setCurrentPhaseId(phaseId);

        // Fetch gameweeks for the selected phase
        const defaultGameweekId = await fetchGameweeks(phaseId);
        if (defaultGameweekId) {
            setCurrentGameweekId(defaultGameweekId);
            fetchGlobalRankings(1, phaseId, defaultGameweekId);
        }
    };

    // Handle gameweek change
    const handleGameweekChange = (gameweekId: string) => {
        setCurrentGameweekId(gameweekId);
        fetchGlobalRankings(1, currentPhaseId, gameweekId);
    };

    // Handle pagination
    const handlePagination = (page: number) => {
        fetchGlobalRankings(page);
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                    <Globe className="w-6 h-6 text-primary" />
                </div>
                <div>
                    <h2 className="text-2xl font-bold text-primary">{t('globalRanking.title')}</h2>
                    <p className="text-muted-foreground text-white">{t('globalRanking.description')}</p>
                </div>
            </div>

            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Calendar className="w-5 h-5" />
                        {t('globalRanking.filters')}
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Phase Selector */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium">{t('globalRanking.phase')}</label>
                            <Select value={currentPhaseId} onValueChange={handlePhaseChange}>
                                <SelectTrigger>
                                    <SelectValue placeholder={t('globalRanking.selectPhase')} />
                                </SelectTrigger>
                                <SelectContent>
                                    {phases.map((phase) => (
                                        <SelectItem key={phase.id} value={phase.id}>
                                            {phase.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {/* Gameweek Selector */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium">{t('globalRanking.gameweek')}</label>
                            <Select value={currentGameweekId} onValueChange={handleGameweekChange} disabled={loadingGameweeks}>
                                <SelectTrigger>
                                    <SelectValue placeholder={loadingGameweeks ? t('globalRanking.loading') : t('globalRanking.selectGameweek')} />
                                </SelectTrigger>
                                <SelectContent>
                                    {loadingGameweeks ? (
                                        <SelectItem value="loading" disabled>
                                            <div className="flex items-center gap-2">
                                                <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                {t('globalRanking.loading')}
                                            </div>
                                        </SelectItem>
                                    ) : (
                                        currentGameweeks.map((gameweek) => (
                                            <SelectItem key={gameweek.id} value={gameweek.id}>
                                                {gameweek.name}
                                            </SelectItem>
                                        ))
                                    )}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Global Rankings Table */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Trophy className="w-5 h-5" />
                        {t('globalRanking.rankings')}
                        {globalRankings.total > 0 && (
                            <span className="text-sm font-normal text-muted-foreground">
                                ({globalRankings.total} {t('globalRanking.teams')})
                            </span>
                        )}
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {loading ? (
                        <div className="flex items-center justify-center py-12">
                            <div className="flex items-center gap-2">
                                <svg className="animate-spin h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span>{t('globalRanking.loading')}</span>
                            </div>
                        </div>
                    ) : globalRankings.data.length === 0 ? (
                        <div className="text-center py-12">
                            <Trophy className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                            <p className="text-muted-foreground">{t('globalRanking.noData')}</p>
                        </div>
                    ) : (
                        <>
                            {/* Table Header */}
                            <div className="grid grid-cols-12 gap-4 px-4 py-3 border-b font-medium text-sm text-muted-foreground">
                                <div className="col-span-1">{t('globalRanking.rank')}</div>
                                <div className="col-span-1">{t('globalRanking.change')}</div>
                                <div className="col-span-4">{''}</div>
                                <div className="col-span-4">{t('globalRanking.team')}</div>
                                <div className="col-span-2 text-right">{t('globalRanking.points')}</div>
                            </div>

                            {/* Rows */}
                            {globalRankings.data.map((ranking, index) => {
                                console.log(`🔍 Frontend Debug - Ranking #${index}:`, {
                                    rank: ranking.rank,
                                    name: ranking.name,
                                    teamName: ranking.teamName,
                                    points: ranking.points,
                                    previous_rank: ranking.previous_rank
                                });
                                
                                return (
                                    <div
                                        key={index}
                                        className={`transition-all hover:bg-muted/50 rounded-lg ${index < 3 ? "bg-muted/30" : ""}`}
                                    >
                                        <LeaderboardRow
                                            rank={ranking.rank}
                                            name={ranking.name}
                                            teamName={ranking.teamName}
                                            points={ranking.points}
                                            previousRank={ranking.previous_rank}
                                            highlight={index < 3}
                                        />
                                    </div>
                                );
                            })}
                        </>
                    )}

                    {/* Pagination */}
                    {globalRankings.last_page > 1 && !loading && (
                        <div className="flex items-center justify-between mt-6 mb-4 pt-4 border-t px-4">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePagination(globalRankings.current_page - 1)}
                                disabled={globalRankings.current_page <= 1 || loading}
                                className="flex items-center gap-2"
                            >
                                {loading && globalRankings.current_page > 1 ? '...' : t('globalRanking.previous')}
                            </Button>

                            <div className="flex items-center gap-2">
                                <span className="text-sm text-muted-foreground">
                                    {t('globalRanking.pageXOfY', { current: globalRankings.current_page, total: globalRankings.last_page })}
                                </span>
                            </div>

                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePagination(globalRankings.current_page + 1)}
                                disabled={globalRankings.current_page >= globalRankings.last_page || loading}
                                className="flex items-center gap-2"
                            >
                                {loading && globalRankings.current_page < globalRankings.last_page ? '...' : t('globalRanking.next')}
                            </Button>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
import React, { useState } from "react";
import { Head, useForm } from "@inertiajs/react";
import Layout from "../Components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/Components/ui/card";
import { <PERSON><PERSON> } from "@/Components/ui/button";
import { Input } from "@/Components/ui/input";
import { Label } from "@/Components/ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/Components/ui/select";
import * as Tabs from "@radix-ui/react-tabs";
import { PageProps } from "../types/inertia";
import HeroSection from "../Components/ui/HeroSection";
import LeagueCard from "../Components/fantasy/LeagueCard";
import LeaderboardRow from "../Components/fantasy/LeaderboardRow";
import { useTenantName } from "@/hooks/useTenant";
import { useTranslation } from "react-i18next";

export default function Leagues({
    myLeagues,
    availableLeagues,
    ...props
}: PageProps & { myLeagues: any[]; availableLeagues: any[] }) {
    const tenantName = useTenantName();
    const { t } = useTranslation();
    const [activeTab, setActiveTab] = useState("my-leagues");

    // Form for creating new league
    const {
        data: createData,
        setData: setCreateData,
        post: createPost,
        processing: createProcessing,
        errors: createErrors,
    } = useForm({
        name: "",
        type: "public",
    });

    // Form for joining by invite code
    const {
        data: joinData,
        setData: setJoinData,
        post: joinPost,
        processing: joinProcessing,
        errors: joinErrors,
    } = useForm({
        invite_code: "",
    });

    const handleCreateLeague = (e: React.FormEvent) => {
        e.preventDefault();
        createPost(route("leagues.store"));
    };

    const handleJoinByCode = (e: React.FormEvent) => {
        e.preventDefault();
        joinPost(route("leagues.join-by-code"));
    };

    const handleJoinLeague = (leagueId: number) => {
        createPost(route("leagues.join", leagueId));
    };

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - ${t("leaderboard.heroTitle")} `} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Header */}
                <HeroSection
                    title={t("leaderboard.heroTitle")}
                    subtitle={t("leaderboard.heroSubtitle")}
                />

                {/* Tabs */}
                <Tabs.Root value={activeTab} onValueChange={setActiveTab}>
                    <div className="flex justify-center">
                        <Tabs.List className="grid w-full grid-cols-3 bg-slate-100 rounded-lg p-1 max-w-md">
                            <Tabs.Trigger
                                value="my-leagues"
                                className={`px-4 py-2 text-sm font-medium rounded-md ${
                                    activeTab === "my-leagues"
                                        ? "bg-background text-foreground shadow-sm"
                                        : "text-gray-500"
                                }`}
                            >
                                {t("leaderboard.tabs.myLeagues")}
                            </Tabs.Trigger>
                            <Tabs.Trigger
                                value="join-league"
                                className={`px-4 py-2 text-sm font-medium rounded-md ${
                                    activeTab === "join-league"
                                        ? "bg-background text-foreground shadow-sm"
                                        : "text-gray-500"
                                }`}
                            >
                                {t("leaderboard.tabs.joinLeague")}
                            </Tabs.Trigger>
                            <Tabs.Trigger
                                value="create-league"
                                className={`px-4 py-2 text-sm font-medium rounded-md ${
                                    activeTab === "create-league"
                                        ? "bg-background text-foreground shadow-sm"
                                        : "text-gray-500"
                                }`}
                            >
                                {t("leaderboard.tabs.createLeague")}
                            </Tabs.Trigger>
                        </Tabs.List>
                    </div>

                    {/* My Leagues Content */}
                    <Tabs.Content value="my-leagues" className="mt-8 space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {myLeagues.map((league) => (
                                <LeagueCard
                                    key={league.id}
                                    {...league}
                                    type={league.type as "Private" | "Public"}
                                    isJoined={true}
                                />
                            ))}
                        </div>
                        <Card>
                            <CardHeader>
                                <CardTitle>
                                    {t("leaderboard.standings.title")}
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    {t(
                                                        "leaderboard.standings.rank"
                                                    )}
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    {t(
                                                        "leaderboard.standings.name"
                                                    )}
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    {t(
                                                        "leaderboard.standings.team"
                                                    )}
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    {t(
                                                        "leaderboard.standings.totalPoints"
                                                    )}
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    {t(
                                                        "leaderboard.standings.lastWeek"
                                                    )}
                                                </th>
                                            </tr>
                                        </thead>
                                        {/* <tbody className="bg-white divide-y divide-gray-200">
                                            {leagueStandings.map((row) => (
                                                <LeaderboardRow
                                                    key={row.rank}
                                                    {...row}
                                                    isCurrentUser={
                                                        row.name === "You"
                                                    }
                                                />
                                            ))}
                                        </tbody> */}
                                    </table>
                                </div>
                            </CardContent>
                        </Card>
                    </Tabs.Content>

                    {/* Join League Content */}
                    <Tabs.Content value="join-league" className="mt-8 space-y-6">
                        <Card className="max-w-md mx-auto">
                            <CardHeader>
                                <CardTitle>
                                    {t("leaderboard.join.privateTitle")}
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <form
                                    onSubmit={handleJoinByCode}
                                    className="space-y-4"
                                >
                                    <div>
                                        <Label htmlFor="invite_code">
                                            {t("leaderboard.join.privateTitle")}
                                        </Label>
                                        <Input
                                            id="invite_code"
                                            type="text"
                                            value={joinData.invite_code}
                                            onChange={(e) =>
                                                setJoinData(
                                                    "invite_code",
                                                    e.target.value
                                                )
                                            }
                                            placeholder={t(
                                                "leaderboard.join.placeholder"
                                            )}
                                            className="mt-1"
                                        />
                                        {joinErrors.invite_code && (
                                            <p className="text-red-500 text-xs mt-1">
                                                {joinErrors.invite_code}
                                            </p>
                                        )}
                                    </div>
                                    <Button
                                        type="submit"
                                        className="w-full"
                                        disabled={joinProcessing}
                                    >
                                        {joinProcessing
                                            ? t("leaderboard.join.joining")
                                            : t("leaderboard.join.joinButton")}
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {availableLeagues.map((league) => (
                                <LeagueCard
                                    key={league.id}
                                    {...league}
                                    type={league.type as "Private" | "Public"}
                                    isJoined={false}
                                    onJoin={handleJoinLeague}
                                />
                            ))}
                        </div>
                    </Tabs.Content>

                    {/* Create League Content */}
                    <Tabs.Content value="create-league" className="mt-8">
                        <Card className="max-w-3xl mx-auto">
                            <CardHeader>
                                <CardTitle>
                                    {t("leaderboard.create.title")}
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <form
                                    onSubmit={handleCreateLeague}
                                    className="space-y-6"
                                >
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        {/* Left Side: Form */}
                                        <div className="space-y-4">
                                            <div>
                                                <Label htmlFor="name">
                                                    {t(
                                                        "leaderboard.create.leagueName"
                                                    )}
                                                </Label>
                                                <Input
                                                    id="name"
                                                    type="text"
                                                    value={createData.name}
                                                    onChange={(e) =>
                                                        setCreateData(
                                                            "name",
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder={t(
                                                        "leaderboard.create.leagueNamePlaceholder"
                                                    )}
                                                    className="mt-1"
                                                />
                                                {createErrors.name && (
                                                    <p className="text-red-500 text-xs mt-1">
                                                        {createErrors.name}
                                                    </p>
                                                )}
                                            </div>
                                            <div>
                                                <Label htmlFor="type">
                                                    {t(
                                                        "leaderboard.create.leagueType"
                                                    )}
                                                </Label>
                                                <Select
                                                    value={createData.type}
                                                    onValueChange={(val) =>
                                                        setCreateData(
                                                            "type",
                                                            val
                                                        )
                                                    }
                                                >
                                                    <SelectTrigger className="mt-1">
                                                        <SelectValue
                                                            placeholder={t(
                                                                "leaderboard.create.leagueTypePlaceholder"
                                                            )}
                                                        />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="private">
                                                            {t(
                                                                "leaderboard.create.privateLabel"
                                                            )}
                                                        </SelectItem>
                                                        <SelectItem value="public">
                                                            {t(
                                                                "leaderboard.create.publicLabel"
                                                            )}
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>

                                        {/* Right Side: Information */}
                                        <div className="bg-blue-50 p-4 rounded-lg space-y-2">
                                            <h3 className="font-semibold text-blue-800">
                                                {t(
                                                    "leaderboard.create.leagueInformation"
                                                )}
                                            </h3>
                                            <div className="text-sm space-y-1">
                                                <div className="flex justify-between">
                                                    <span className="text-blue-700 font-medium">
                                                        {t(
                                                            "leaderboard.create.organization"
                                                        )}
                                                    </span>
                                                    <span className="text-blue-800">
                                                        {tenantName}
                                                    </span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-blue-700 font-medium">
                                                        {t(
                                                            "leaderboard.create.season"
                                                        )}
                                                    </span>
                                                    <span className="text-blue-800">
                                                        {t(
                                                            "leaderboard.create.currentCompetition"
                                                        )}
                                                    </span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-blue-700 font-medium">
                                                        {t(
                                                            "leaderboard.create.owner"
                                                        )}
                                                    </span>
                                                    <span className="text-blue-800">
                                                        {t(
                                                            "leaderboard.create.you"
                                                        )}{" "}
                                                        (
                                                        {props.auth?.user
                                                            ?.name ||
                                                            t(
                                                                "leaderboard.create.currentUser"
                                                            )}
                                                        )
                                                    </span>
                                                </div>
                                                {createData.type ===
                                                    "private" && (
                                                    <div className="flex justify-between">
                                                        <span className="text-blue-700 font-medium">
                                                            {t(
                                                                "leaderboard.create.inviteCode"
                                                            )}
                                                        </span>
                                                        <span className="text-blue-800">
                                                            {t(
                                                                "leaderboard.create.autoGenerated"
                                                            )}
                                                        </span>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    <Button
                                        type="submit"
                                        className="w-full"
                                        size="lg"
                                        disabled={createProcessing}
                                    >
                                        {createProcessing
                                            ? t("leaderboard.create.creating")
                                            : t(
                                                  "leaderboard.create.createButton"
                                              )}
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>
                    </Tabs.Content>
                </Tabs.Root>
            </div>
        </Layout>
    );
}

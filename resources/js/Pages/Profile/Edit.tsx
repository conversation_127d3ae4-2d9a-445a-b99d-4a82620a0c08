import React from 'react';
import { Head, useForm } from '@inertiajs/react';
import Layout from '@/Components/Layout';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { User, Save, Camera } from 'lucide-react';

interface User {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    phone?: string;
    date_of_birth?: string;
    favorite_team?: string;
    avatar?: string;
}

interface EditProfileProps {
    user: User;
    teams: Array<{ id: string; name: string; }>;
}

export default function EditProfile({ user, teams }: EditProfileProps) {
    const { data, setData, put, processing, errors } = useForm({
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || '',
        phone: user.phone || '',
        date_of_birth: user.date_of_birth || '',
        favorite_team: user.favorite_team || '',
        avatar: null as File | null,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put('/profile/update');
    };

    const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('avatar', file);
        }
    };

    const getInitials = () => {
        const firstInitial = user.first_name?.charAt(0) || '';
        const lastInitial = user.last_name?.charAt(0) || '';
        return (firstInitial + lastInitial).toUpperCase();
    };

    return (
        <Layout user={user}>
            <Head title="Edit Profile" />
            
            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex items-center space-x-4 mb-6">
                                <User className="h-8 w-8 text-green-600" />
                                <div>
                                    <h1 className="text-2xl font-bold text-gray-900">Edit Profile</h1>
                                    <p className="text-gray-600">Update your personal information and preferences</p>
                                </div>
                            </div>

                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Avatar Section */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Profile Picture</CardTitle>
                                        <CardDescription>
                                            Upload a new profile picture or keep your current one
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="flex items-center space-x-6">
                                            <div className="relative">
                                                {user.avatar ? (
                                                    <img
                                                        src={user.avatar}
                                                        alt="Profile"
                                                        className="h-20 w-20 rounded-full object-cover"
                                                    />
                                                ) : (
                                                    <div className="h-20 w-20 rounded-full bg-green-600 flex items-center justify-center text-white text-xl font-medium">
                                                        {getInitials()}
                                                    </div>
                                                )}
                                            </div>
                                            <div>
                                                <Label htmlFor="avatar" className="cursor-pointer">
                                                    <div className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                                                        <Camera className="h-4 w-4" />
                                                        <span>Change Picture</span>
                                                    </div>
                                                </Label>
                                                <Input
                                                    id="avatar"
                                                    type="file"
                                                    accept="image/*"
                                                    onChange={handleAvatarChange}
                                                    className="hidden"
                                                />
                                                {errors.avatar && (
                                                    <p className="text-red-500 text-sm mt-1">{errors.avatar}</p>
                                                )}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Personal Information */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Personal Information</CardTitle>
                                        <CardDescription>
                                            Your basic personal details
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <Label htmlFor="first_name">First Name</Label>
                                                <Input
                                                    id="first_name"
                                                    type="text"
                                                    value={data.first_name}
                                                    onChange={(e) => setData('first_name', e.target.value)}
                                                    className="mt-1"
                                                />
                                                {errors.first_name && (
                                                    <p className="text-red-500 text-sm mt-1">{errors.first_name}</p>
                                                )}
                                            </div>
                                            <div>
                                                <Label htmlFor="last_name">Last Name</Label>
                                                <Input
                                                    id="last_name"
                                                    type="text"
                                                    value={data.last_name}
                                                    onChange={(e) => setData('last_name', e.target.value)}
                                                    className="mt-1"
                                                />
                                                {errors.last_name && (
                                                    <p className="text-red-500 text-sm mt-1">{errors.last_name}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div>
                                            <Label htmlFor="email">Email Address</Label>
                                            <Input
                                                id="email"
                                                type="email"
                                                value={data.email}
                                                onChange={(e) => setData('email', e.target.value)}
                                                className="mt-1"
                                            />
                                            {errors.email && (
                                                <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                                            )}
                                        </div>

                                        <div>
                                            <Label htmlFor="phone">Phone Number</Label>
                                            <Input
                                                id="phone"
                                                type="tel"
                                                value={data.phone}
                                                onChange={(e) => setData('phone', e.target.value)}
                                                className="mt-1"
                                                placeholder="Enter your phone number"
                                            />
                                            {errors.phone && (
                                                <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                                            )}
                                        </div>

                                        <div>
                                            <Label htmlFor="date_of_birth">Date of Birth</Label>
                                            <Input
                                                id="date_of_birth"
                                                type="date"
                                                value={data.date_of_birth}
                                                onChange={(e) => setData('date_of_birth', e.target.value)}
                                                className="mt-1"
                                            />
                                            {errors.date_of_birth && (
                                                <p className="text-red-500 text-sm mt-1">{errors.date_of_birth}</p>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Fantasy Preferences */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Fantasy Preferences</CardTitle>
                                        <CardDescription>
                                            Your football fantasy game preferences
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div>
                                            <Label htmlFor="favorite_team">Favorite Team</Label>
                                            <Select
                                                value={data.favorite_team}
                                                onValueChange={(value) => setData('favorite_team', value)}
                                            >
                                                <SelectTrigger className="mt-1">
                                                    <SelectValue placeholder="Select your favorite team" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {teams.map((team) => (
                                                        <SelectItem key={team.id} value={team.id}>
                                                            {team.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.favorite_team && (
                                                <p className="text-red-500 text-sm mt-1">{errors.favorite_team}</p>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Submit Button */}
                                <div className="flex justify-end">
                                    <Button
                                        type="submit"
                                        disabled={processing}
                                        className="flex items-center space-x-2"
                                    >
                                        <Save className="h-4 w-4" />
                                        <span>{processing ? 'Saving...' : 'Save Changes'}</span>
                                    </Button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    );
}

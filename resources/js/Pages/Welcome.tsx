import { Head } from "@inertiajs/react";
import Layout from "../Components/Layout";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/Components/ui/card";
import { PageProps } from "@/types/inertia";
import HeroSection from "../Components/ui/HeroSection";
import StatsCard from "../Components/fantasy/StatsCard";
import FantasyNavigationCard from "../Components/fantasy/FantasyNavigationCard";
import ActivityItem from "../Components/fantasy/ActivityItem";
import FixturesWidget from "../Components/fantasy/FixturesWidget";
import { useTenantName } from "@/hooks/useTenant";
import { useTranslation } from "react-i18next";

interface Team {
    id: number;
    name: string;
    short_name: string;
    logo?: string;
    shirt?: {
        base_color?: string;
    };
}

interface Fixture {
    id: number;
    gameweek_id: number;
    homeTeam: Team;
    awayTeam: Team;
    game_date: string;
    home_score?: number | null;
    away_score?: number | null;
    status: "scheduled" | "in_progress" | "finished" | "postponed" | "canceled";
    stadium?: {
        id: number;
        name: string;
    };
    referee?: {
        id: number;
        name: string;
    };
}

interface Gameweek {
    id: number;
    name: string;
    startDate: string;
    endDate: string;
    status: string;
}

interface PaginationInfo {
    currentIndex: number;
    totalGameweeks: number;
    hasNext: boolean;
    hasPrevious: boolean;
}

interface DashboardData {
    teamValue: string;
    totalPoints: number;
    globalRank: string;
    currentWeek: string;
    recentActivity: Array<{
        id: number;
        type: string;
        date: string;
        inPlayer?: {
            id: number;
            name: string;
            price: number;
        } | null;
        outPlayer?: {
            id: number;
            name: string;
            price: number;
        } | null;
    }>;
}

interface WelcomeProps extends PageProps {
    dashboardData: DashboardData;
    fixtures: Record<number, Record<string, Fixture[]>>;
    gameweeks: Gameweek[];
    allGameweeks: Gameweek[];
    currentGameweek: Gameweek | null;
    pagination: PaginationInfo;
    competition: {
        id: number;
        name: string;
    } | null;
    season: {
        id: number;
        name: string;
    } | null;
}

export default function Welcome(props: WelcomeProps) {
    const { t } = useTranslation();
    const { auth, dashboardData, fixtures, gameweeks, allGameweeks, currentGameweek, pagination } = props;
    console.log(dashboardData.recentActivity);
    const tenantName = useTenantName();
    const fantasyCards = [
        {
            title: t("My Team"),
            description: t("Manage your squad and formation"),
            icon: "👥",
            href: route("game.team"),
            color: "bg-green-500 hover:bg-green-600",
        },
        {
            title: t("Transfers"),
            description: t("Buy and sell players"),
            icon: "🔄",
            href: route("game.transfers"),
            color: "bg-blue-500 hover:bg-blue-600",
        },
        {
            title: t("Leagues"),
            description: t("Join leagues and compete"),
            icon: "🏆",
            href: route("game.leagues"),
            color: "bg-yellow-500 hover:bg-yellow-600",
        },
        {
            title: t("Fixtures"),
            description: t("View upcoming matches"),
            icon: "📅",
            href: route("game.fixtures"),
            color: "bg-purple-500 hover:bg-purple-600",
        },
    ];

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - ${t("Dashboard")}`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Hero Section */}
                {/* Hero Section */}
                <HeroSection
                    title={`${t("Welcome to")} ${tenantName}`}
                    subtitle={t(
                        "Build your dream team and compete with friends!"
                    )}
                    userWelcome={
                        auth?.user?.first_name
                            ? {
                                firstName: auth.user.first_name,
                                message: t("Ready to manage your team?"),
                            }
                            : undefined
                    }
                />

                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <StatsCard
                        value={dashboardData.teamValue}
                        label={t("Team Value")}
                    />
                    <StatsCard
                        value={dashboardData.totalPoints.toLocaleString()}
                        label={t("Total Points")}
                    />
                    <StatsCard
                        value={dashboardData.globalRank}
                        label={t("Global League Rank")}
                    />
                    <StatsCard
                        value={dashboardData.currentWeek}
                        label={t("Current Week")}
                    />
                </div>

                {/* Fantasy Navigation Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {fantasyCards.map((card, index) => (
                        <FantasyNavigationCard
                            key={index}
                            title={card.title}
                            description={card.description}
                            icon={card.icon}
                            href={card.href}
                            color={card.color}
                        />
                    ))}
                </div>

                {/* Recent Activity & Upcoming Fixtures */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card className="transparent-card">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-gray-200">
                                <span>📊</span>
                                {t("Recent Activity")}
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {dashboardData.recentActivity.length > 0 ? (
                                dashboardData.recentActivity.map((activity) => {
                                    let title = "";
                                    let description = "";
                                    let badge = "";
                                    let variant: "green" | "blue" | "amber" =
                                        "blue";

                                    if (
                                        activity.inPlayer &&
                                        activity.outPlayer
                                    ) {
                                        title = `${activity.inPlayer.name} ${t(
                                            "transferred in"
                                        )}`;
                                        description = `${activity.outPlayer.name
                                            } ${t("transferred out")}`;
                                        badge = t("IN");
                                        variant = "blue";
                                    } else if (activity.inPlayer) {
                                        title = `${activity.inPlayer.name} ${t(
                                            "transferred in"
                                        )}`;
                                        description = `${t(
                                            "Cost: £"
                                        )}${activity.inPlayer.price.toFixed(
                                            1
                                        )}${t("m")}`;
                                        badge = t("IN");
                                        variant = "blue";
                                    } else if (activity.outPlayer) {
                                        title = `${activity.outPlayer.name} ${t(
                                            "transferred out"
                                        )}`;
                                        description = `${t(
                                            "Transfer date:"
                                        )} ${new Date(
                                            activity.date
                                        ).toLocaleDateString()}`;
                                        badge = t("OUT");
                                        variant = "amber";
                                    }

                                    return (
                                        <ActivityItem
                                            key={activity.id}
                                            title={title}
                                            description={description}
                                            badge={badge}
                                            variant={variant}
                                        />
                                    );
                                })
                            ) : (
                                <div className="text-center py-4 text-gray-400">
                                    {t("No recent activity")}
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <FixturesWidget
                        fixtures={fixtures}
                        gameweeks={gameweeks}
                        allGameweeks={allGameweeks}
                        currentGameweek={currentGameweek}
                        pagination={pagination}
                        showNavigation={false}
                        showCompetitionInfo={false}
                        maxFixtures={5}
                        title={`📅 ${t("Upcoming Fixtures")}`}
                        className=""
                        transparent={true}
                    />
                </div>
            </div>
        </Layout>
    );
}

import React from 'react';
import { Head, useForm } from '@inertiajs/react';
import { Button } from '../../Components/ui/button';
import { Input } from '../../Components/ui/input';
import { Label } from '../../Components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../Components/ui/card';
import { Toaster } from '../../Components/ui/toaster';
import { useToast } from '../../hooks/use-toast';
import * as Form from '@radix-ui/react-form';

interface Props {
    phone: string;
    is_new_user: boolean;
}

export default function ProfileCompletion({ phone, is_new_user }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        first_name: '',
        last_name: '',
        date_of_birth: '',
    });

    const { toast } = useToast();

    const validateField = (name: string, value: string) => {
        switch (name) {
            case 'first_name':
            case 'last_name':
                // Only validate if there's content
                if (value.length > 0) {
                    // Check minimum length
                    if (value.length < 4) {
                        return {
                            isValid: false,
                            message: `${name.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} must be at least 4 characters long`
                        };
                    }
                    // Check if contains only letters (including accented characters and spaces)
                    if (!/^[\p{L}\s']+$/u.test(value)) {
                        return {
                            isValid: false,
                            message: `${name.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} can only contain letters and spaces`
                        };
                    }
                }
                return { isValid: true };
            case 'date_of_birth':
                const today = new Date();
                const birthDate = new Date(value);
                const age = today.getFullYear() - birthDate.getFullYear();
                if (age < 13) {
                    return {
                        isValid: false,
                        message: "You must be at least 13 years old"
                    };
                }
                break;
        }
        return { isValid: true };
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        let hasErrors = false;
        
        // Validate all fields
        Object.entries(data).forEach(([key, value]) => {
            const validation = validateField(key, value);
            if (!validation.isValid) {
                hasErrors = true;
                toast({
                    variant: "destructive",
                    title: "Validation Error",
                    description: validation.message,
                });
            }
        });

        if (!hasErrors) {
            post(route('auth.profile.store'));
        }
    };

    return (
        <>
            <Head title="Complete Your Profile" />

            <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
                <div className="w-full max-w-md">
                    <div className="text-center mb-8">
                        <div className="mx-auto w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mb-4">
                            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">Complete Your Profile</h1>
                        <p className="text-gray-600">
                            {is_new_user ? 'Welcome! Let\'s set up your profile' : 'Please complete your profile to continue'}
                        </p>
                        <p className="text-sm text-gray-500 mt-2">Phone: {phone}</p>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>Personal Information</CardTitle>
                            <CardDescription>
                                Tell us about yourself to personalize your fantasy experience
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Form.Root onSubmit={handleSubmit} className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <Form.Field name="first_name" className="space-y-2">
                                        <Form.Label asChild>
                                            <Label htmlFor="first_name" className="mb-2">First Name</Label>
                                        </Form.Label>
                                        <Form.Control asChild>
                                            <Input
                                                id="first_name"
                                                type="text"
                                                placeholder="Ahmed"
                                                value={data.first_name}
                                                onChange={(e) => setData('first_name', e.target.value)}
                                                className={`mt-2 ${
                                                    data.first_name && !validateField('first_name', data.first_name).isValid 
                                                        ? 'border-red-500' 
                                                        : ''
                                                }`}
                                                required
                                            />
                                        </Form.Control>
                                        <Form.Message match="valueMissing" className="text-sm text-red-600">
                                            Please enter your first name
                                        </Form.Message>
                                        {errors.first_name && (
                                            <Form.Message className="text-sm text-red-600">
                                                {errors.first_name}
                                            </Form.Message>
                                        )}
                                    </Form.Field>

                                    <Form.Field name="last_name" className="space-y-2">
                                        <Form.Label asChild>
                                            <Label htmlFor="last_name" className="mb-2">Last Name</Label>
                                        </Form.Label>
                                        <Form.Control asChild>
                                            <Input
                                                id="last_name"
                                                type="text"
                                                placeholder="Benali"
                                                value={data.last_name}
                                                onChange={(e) => setData('last_name', e.target.value)}
                                                className={`mt-2 ${
                                                    data.last_name && !validateField('last_name', data.last_name).isValid 
                                                        ? 'border-red-500' 
                                                        : ''
                                                }`}
                                                required
                                            />
                                        </Form.Control>
                                        <Form.Message match="valueMissing" className="text-sm text-red-600">
                                            Please enter your last name
                                        </Form.Message>
                                        {errors.last_name && (
                                            <Form.Message className="text-sm text-red-600">
                                                {errors.last_name}
                                            </Form.Message>
                                        )}
                                    </Form.Field>
                                </div>

                                <Form.Field name="date_of_birth" className="space-y-2">
                                    <Form.Label asChild>
                                        <Label htmlFor="date_of_birth" className="mb-2">Date of Birth</Label>
                                    </Form.Label>
                                    <Form.Control asChild>
                                        <Input
                                            id="date_of_birth"
                                            type="date"
                                            value={data.date_of_birth}
                                            onChange={(e) => setData('date_of_birth', e.target.value)}
                                            className={`mt-2 ${
                                                errors.date_of_birth ? 'border-red-500' : ''
                                            }`}
                                            max={new Date().toISOString().split('T')[0]}
                                            required
                                        />
                                    </Form.Control>
                                    <Form.Message match="valueMissing" className="text-sm text-red-600">
                                        Please enter your date of birth
                                    </Form.Message>
                                    {errors.date_of_birth && (
                                        <Form.Message className="text-sm text-red-600">
                                            {errors.date_of_birth}
                                        </Form.Message>
                                    )}
                                </Form.Field>

                                <Form.Submit asChild>
                                    <Button
                                        type="submit"
                                        className="w-full bg-green-600 hover:bg-green-700"
                                        disabled={processing}
                                    >
                                        {processing ? 'Saving...' : 'Complete Profile'}
                                    </Button>
                                </Form.Submit>
                            </Form.Root>
                        </CardContent>
                    </Card>
                </div>
            </div>
            <Toaster />
        </>
    );
}

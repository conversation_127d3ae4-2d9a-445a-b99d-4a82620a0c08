import React, { useState, useEffect } from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import { Button } from '../../Components/ui/button';
import { Input } from '../../Components/ui/input';
import { Label } from '../../Components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../Components/ui/card';
import { Toaster } from '../../Components/ui/toaster';
import { useToast } from '../../hooks/use-toast';
import * as Form from '@radix-ui/react-form';

interface Props {
    phone: string;
    message?: string;
    errors?: any;
}

export default function OtpVerification({ phone, message, errors: propErrors }: Props) {
    const [countdown, setCountdown] = useState(60);
    const [canResend, setCanResend] = useState(false);

    const { data, setData, post, processing, errors } = useForm({
        otp: '',
    });

    const { post: resendPost, processing: resendProcessing } = useForm();
    const { toast } = useToast();

    useEffect(() => {
        const timer = setInterval(() => {
            setCountdown((prev) => {
                if (prev <= 1) {
                    setCanResend(true);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    useEffect(() => {
        if (errors.otp) {
            toast({
                variant: "destructive",
                title: "Invalid OTP",
                description: errors.otp,
            });
        }
    }, [errors.otp]);

    useEffect(() => {
        if (propErrors?.otp) {
            toast({
                variant: "destructive",
                title: "Invalid OTP",
                description: propErrors.otp,
            });
        }
    }, [propErrors]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/auth/otp');
    };

    const handleResend = () => {
        resendPost('/auth/otp/resend', {
            preserveState: true,
            preserveScroll: true,
            onSuccess: () => {
                setCountdown(60);
                setCanResend(false);
                toast({
                    variant: "success",
                    title: "Code Sent",
                    description: "New verification code sent!",
                });
            },
            onError: () => {
                toast({
                    variant: "destructive",
                    title: "Failed to Send",
                    description: "Failed to resend code. Please try again.",
                });
            }
        });
    };

    const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.replace(/\D/g, '').slice(0, 6);
        setData('otp', value);
    };

    const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    return (
        <>
            <Head title="Verify Your Phone" />

            <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
                <div className="w-full max-w-md">
                    <div className="text-center mb-8">
                        <div className="mx-auto w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mb-4">
                            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">Verify Your Phone</h1>
                        <p className="text-gray-600">
                            We've sent a 6-digit code to
                        </p>
                        <p className="font-semibold text-gray-900">{phone}</p>
                    </div>

                    <Card>
                        <CardHeader>
                            <div className="flex items-center mb-4">
                                <Link href="/auth/onboarding">
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        className="text-gray-600 hover:text-gray-900 p-2 -ml-2"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                        </svg>
                                        Change Phone Number
                                    </Button>
                                </Link>
                            </div>
                            <CardTitle>Enter Verification Code</CardTitle>
                            <CardDescription>
                                {message || 'Please enter the 6-digit code sent to your phone'}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Form.Root onSubmit={handleSubmit} className="space-y-4">
                                <Form.Field name="otp">
                                    <Form.Label asChild className='mb-2'>
                                        <Label htmlFor="otp" className='mb-2'>Verification Code</Label>
                                    </Form.Label>
                                    <Form.Control asChild>
                                        <Input
                                            id="otp"
                                            type="text"
                                            placeholder="123456"
                                            value={data.otp}
                                            onChange={handleOtpChange}
                                            className={`mt-2 text-center text-2xl font-mono tracking-widest ${errors.otp ? 'border-red-500' : ''}`}
                                            maxLength={6}
                                            required
                                        />
                                    </Form.Control>
                                    <Form.Message match="valueMissing" className="text-sm text-red-600">
                                        Please enter the verification code
                                    </Form.Message>
                                </Form.Field>

                                <Form.Submit asChild>
                                    <Button
                                        type="submit"
                                        className="w-full bg-green-600 hover:bg-green-700"
                                        disabled={processing || data.otp.length !== 6}
                                    >
                                        {processing ? 'Verifying...' : 'Verify Code'}
                                    </Button>
                                </Form.Submit>
                            </Form.Root>

                            <div className="mt-6 text-center space-y-3">
                                <p className="text-sm text-gray-600 mb-2">
                                    Didn't receive the code?
                                </p>

                                {canResend ? (
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handleResend}
                                        disabled={resendProcessing}
                                        className="text-green-600 border-green-600 hover:bg-green-50"
                                    >
                                        {resendProcessing ? 'Sending...' : 'Resend Code'}
                                    </Button>
                                ) : (
                                    <p className="text-sm text-gray-500">
                                        Resend code in {formatTime(countdown)}
                                    </p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    <div className="mt-6 text-center">
                        <p className="text-xs text-gray-500">
                            By continuing, you agree to our Terms of Service and Privacy Policy
                        </p>
                    </div>
                </div>
            </div>

            <Toaster />
        </>
    );
}

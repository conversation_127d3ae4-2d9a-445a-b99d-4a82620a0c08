import React, { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import { Button } from '../../Components/ui/button';
import { Input } from '../../Components/ui/input';
import { Label } from '../../Components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../Components/ui/card';
import { Toaster } from '../../Components/ui/toaster';
import { useToast } from '../../hooks/use-toast';
import * as Form from '@radix-ui/react-form';
import * as Avatar from '@radix-ui/react-avatar';

export default function Onboarding() {
    const { data, setData, post, processing, errors } = useForm({
        phone: '',
    });

    const { toast } = useToast();

    const validatePhoneNumber = (phone: string) => {
        const cleaned = phone.replace(/\D/g, '');

        if (cleaned.length === 0) {
            toast({
                variant: "destructive",
                title: "Invalid Phone Number",
                description: "Please enter your phone number",
            });
            return false;
        }

        if (cleaned.length !== 8) {
            toast({
                variant: "destructive",
                title: "Invalid Phone Number",
                description: "Phone number must be exactly 8 digits",
            });
            return false;
        }

        return true;
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!validatePhoneNumber(data.phone)) {
            return;
        }

        post('/auth/phone');
    };

    // Add phone number formatting with better UX
    const formatPhoneNumber = (value: string) => {
        const cleaned = value.replace(/\D/g, '').slice(0, 8); // Limit to 8 digits
        const match = cleaned.match(/^(\d{2})(\d{3})(\d{3})$/);
        if (match) {
            return `${match[1]} ${match[2]} ${match[3]}`;
        }
        return cleaned;
    };

    return (
        <>
            <Head title="Welcome to Fantasy Football" />

            <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
                <div className="w-full max-w-md">
                    <div className="text-center mb-8">
                        <Avatar.Root className="mx-auto w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mb-4">
                            <Avatar.Fallback className="w-8 h-8 text-white">
                                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </Avatar.Fallback>
                        </Avatar.Root>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">Fantasy Football</h1>
                        <p className="text-gray-600">Build your dream team and compete with friends!</p>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>Get Started</CardTitle>
                            <CardDescription>
                                Enter your phone number to join the fantasy football experience
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Form.Root onSubmit={handleSubmit} className="space-y-4">
                                <Form.Field name="phone" className="space-y-2">
                                    <Form.Label asChild className='mb-2'>
                                        <Label htmlFor="phone">Phone Number</Label>
                                    </Form.Label>
                                    <Form.Control asChild>
                                        <Input
                                            id="phone"
                                            type="tel"
                                            placeholder="XX XXX XXX"
                                            value={formatPhoneNumber(data.phone)}
                                            onChange={(e) => setData('phone', e.target.value.replace(/\s/g, ''))}
                                            className={`mt-2 ${errors.phone ? 'border-red-500' : ''}`}
                                            required
                                        />
                                    </Form.Control>
                                    <Form.Message match="valueMissing" className="text-sm text-red-600">
                                        Please enter your phone number
                                    </Form.Message>
                                    {errors.phone && (
                                        <Form.Message className="text-sm text-red-600">
                                            {errors.phone}
                                        </Form.Message>
                                    )}
                                </Form.Field>

                                <Form.Submit asChild>
                                    <Button
                                        type="submit"
                                        className="w-full bg-green-600 hover:bg-green-700"
                                        disabled={processing}
                                    >
                                        Continue
                                    </Button>
                                </Form.Submit>
                            </Form.Root>
                        </CardContent>
                    </Card>

                    <div className="mt-8 text-center">
                        <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
                            <div className="flex items-center">
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Secure
                            </div>
                            <div className="flex items-center">
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                Fast
                            </div>
                            <div className="flex items-center">
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                                Private
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <Toaster />
        </>
    );
}

import { Head } from "@inertiajs/react";
import Layout from "../Components/Layout";
import { Badge } from "@/Components/ui/badge";
import { PageProps } from "../types/inertia";
import HeroSection from "../Components/ui/HeroSection";
import FixturesWidget from "../Components/fantasy/FixturesWidget";
import { useTenantName } from "@/hooks/useTenant";
import { useTranslation } from "react-i18next";
import { Calendar, Trophy } from "lucide-react";

interface Team {
    id: number;
    name: string;
    short_name: string;
    logo?: string;
}

interface Fixture {
    id: number;
    gameweek_id: number;
    homeTeam: Team;
    awayTeam: Team;
    game_date: string;
    home_score?: number | null;
    away_score?: number | null;
    status: "scheduled" | "in_progress" | "finished" | "postponed" | "canceled";
    stadium?: {
        id: number;
        name: string;
    };
    referee?: {
        id: number;
        name: string;
    };
}

interface Gameweek {
    id: number;
    name: string;
    startDate: string;
    endDate: string;
    status: string;
}

interface PaginationInfo {
    currentIndex: number;
    totalGameweeks: number;
    hasNext: boolean;
    hasPrevious: boolean;
}

interface FixturesProps extends PageProps {
    fixtures: Record<number, Record<string, Fixture[]>>;
    gameweeks: Gameweek[];
    allGameweeks: Gameweek[];
    currentGameweek: Gameweek | null;
    pagination: PaginationInfo;
    competition: {
        id: number;
        name: string;
    };
    season: {
        id: number;
        name: string;
    };
    error?: string;
}

export default function Fixtures(props: FixturesProps) {
    const tenantName = useTenantName();
    const { t } = useTranslation();
    const { fixtures, gameweeks, allGameweeks, currentGameweek, pagination, competition, season, error } =
        props;

    // Handle error state
    if (error) {
        return (
            <Layout {...props}>
                <Head title={t('fixtures.title')} />
                <div className="container mx-auto px-4 py-8">
                    <div className="text-center">
                        <h1 className="text-2xl font-bold text-red-600 mb-4">
                            {t('fixtures.errorLoading')}
                        </h1>
                        <p className="text-gray-600">{error}</p>
                    </div>
                </div>
            </Layout>
        );
    }



    return (
        <Layout {...props}>
            <Head title={`${tenantName} - ${t('fixtures.title')}`} />
            <HeroSection
                title={t('fixtures.title')}
                subtitle={`${competition?.name} - ${season?.name}`}
            />

            <div className="container mx-auto px-4 py-8 space-y-6">
                {/* Competition Info */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Trophy className="w-6 h-6 text-yellow-500" />
                        <div>
                            <h2 className="text-xl font-bold text-gray-900">{competition?.name}</h2>
                            <p className="text-sm text-gray-600">{season?.name}</p>
                        </div>
                    </div>
                    <Badge variant="outline" className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3" />
                        <span>{gameweeks.length} Gameweeks</span>
                    </Badge>
                </div>

                {/* Fixtures Widget */}
                <FixturesWidget
                    fixtures={fixtures}
                    gameweeks={gameweeks}
                    allGameweeks={allGameweeks}
                    currentGameweek={currentGameweek}
                    pagination={pagination}
                    showNavigation={true}
                    showCompetitionInfo={false}
                />


            </div>
        </Layout>
    );
}

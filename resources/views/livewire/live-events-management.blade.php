<div>
    @if($selectedLiveGame)
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Match Info -->
            <div class="lg:col-span-3 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        {{ $selectedLiveGame->game->game_display_name }}
                    </h3>
                    <div class="flex items-center space-x-4">
                        <span class="text-2xl font-bold text-gray-900 dark:text-white">
                            {{ $selectedLiveGame->game->home_score ?? 0 }} - {{ $selectedLiveGame->game->away_score ?? 0 }}
                        </span>
                        <span class="px-3 py-1 rounded-full text-sm font-medium
                            @if($selectedLiveGame->status->isActive()) bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                            @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                            {{ $selectedLiveGame->status->getLabel() }}
                        </span>
                        <span class="text-lg font-semibold text-gray-700 dark:text-gray-300">
                            {{ $selectedLiveGame->current_minute }}'
                        </span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h4>
                <div class="space-y-3">
                    @if($selectedLiveGame->status === \App\Enums\LiveGameStatus::LIVE)
                        <button wire:click="startHalfTime" 
                                class="w-full px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                            Start Half Time
                        </button>
                    @endif
                    
                    @if($selectedLiveGame->status === \App\Enums\LiveGameStatus::HALF_TIME)
                        <button wire:click="startSecondHalf" 
                                class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                            Start Second Half
                        </button>
                    @endif

                    <!-- Update Minute -->
                    <div class="flex items-center space-x-2">
                        <input type="number" 
                               wire:model.defer="newMinute" 
                               placeholder="Minute" 
                               min="0" 
                               max="120"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                        <button wire:click="updateMinute($wire.newMinute)" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            Update
                        </button>
                    </div>
                </div>
            </div>

            <!-- Record Goal -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">Record Goal</h4>
                <form wire:submit.prevent="recordGoal({
                    minute: $wire.goalMinute,
                    lineup_player_id: $wire.goalPlayerId,
                    goal_type: $wire.goalType,
                    assist_player_id: $wire.assistPlayerId
                })">
                    <div class="space-y-3">
                        <input type="number" 
                               wire:model.defer="goalMinute" 
                               placeholder="Minute" 
                               min="1" 
                               max="120"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                               required>
                        
                        <select wire:model.defer="goalTeamId" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="">Select Team</option>
                            <option value="{{ $selectedLiveGame->game->home_team_id }}">{{ $selectedLiveGame->game->homeTeam->name }}</option>
                            <option value="{{ $selectedLiveGame->game->away_team_id }}">{{ $selectedLiveGame->game->awayTeam->name }}</option>
                        </select>

                        @if($goalTeamId)
                            <select wire:model.defer="goalPlayerId" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                                    required>
                                <option value="">Select Player</option>
                                @foreach($this->getTeamPlayers($goalTeamId) as $id => $name)
                                    <option value="{{ $id }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        @endif

                        <select wire:model.defer="goalType" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="goal">Regular Goal</option>
                            <option value="penalty">Penalty</option>
                            <option value="own_goal">Own Goal</option>
                            <option value="free_kick">Free Kick</option>
                        </select>

                        <button type="submit" 
                                class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                            Record Goal
                        </button>
                    </div>
                </form>
            </div>

            <!-- Record Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">Record Card</h4>
                <form wire:submit.prevent="recordCard({
                    minute: $wire.cardMinute,
                    lineup_player_id: $wire.cardPlayerId,
                    card_type: $wire.cardType,
                    reason: $wire.cardReason
                })">
                    <div class="space-y-3">
                        <input type="number" 
                               wire:model.defer="cardMinute" 
                               placeholder="Minute" 
                               min="1" 
                               max="120"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                               required>
                        
                        <select wire:model.defer="cardTeamId" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="">Select Team</option>
                            <option value="{{ $selectedLiveGame->game->home_team_id }}">{{ $selectedLiveGame->game->homeTeam->name }}</option>
                            <option value="{{ $selectedLiveGame->game->away_team_id }}">{{ $selectedLiveGame->game->awayTeam->name }}</option>
                        </select>

                        @if($cardTeamId)
                            <select wire:model.defer="cardPlayerId" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                                    required>
                                <option value="">Select Player</option>
                                @foreach($this->getTeamPlayers($cardTeamId) as $id => $name)
                                    <option value="{{ $id }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        @endif

                        <select wire:model.defer="cardType" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="yellow">Yellow Card</option>
                            <option value="red">Red Card</option>
                        </select>

                        <input type="text" 
                               wire:model.defer="cardReason" 
                               placeholder="Reason (optional)" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white">

                        <button type="submit" 
                                class="w-full px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                            Record Card
                        </button>
                    </div>
                </form>
            </div>

            <!-- Events Timeline -->
            <div class="lg:col-span-3 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">Match Events</h4>
                <div class="space-y-3 max-h-96 overflow-y-auto">
                    @forelse($events as $event)
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <span class="text-sm font-semibold text-gray-600 dark:text-gray-400 min-w-[3rem]">
                                {{ $event['minute'] ?? 0 }}'
                            </span>
                            <div class="flex-1">
                                <span class="text-sm text-gray-900 dark:text-white">
                                    {{ $event['description'] }}
                                </span>
                            </div>
                            <span class="px-2 py-1 text-xs rounded-full
                                @if($event['type'] === 'goal') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                @elseif($event['type'] === 'card') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                @elseif($event['type'] === 'substitution') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                                {{ ucfirst($event['type']) }}
                            </span>
                        </div>
                    @empty
                        <p class="text-gray-500 dark:text-gray-400 text-center py-8">No events recorded yet.</p>
                    @endforelse
                </div>
            </div>
        </div>
    @else
        <div class="text-center py-12">
            <p class="text-gray-500 dark:text-gray-400">No live game selected.</p>
        </div>
    @endif
</div>

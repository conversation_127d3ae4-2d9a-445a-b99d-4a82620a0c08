<div>
    @if($selectedLiveGame)
        <div class="space-y-6">
            <!-- Match Header -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Team Lineups: {{ $selectedLiveGame->game->game_display_name }}
                </h3>
                <p class="text-gray-600 dark:text-gray-400">
                    Set up starting XI and substitutes for both teams before going live.
                </p>
            </div>

            <!-- Teams Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Home Team -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                    <!-- Team Header -->
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ $homeTeam->name }} (Home)
                                </h4>
                                @if($homeLineup)
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        Formation: {{ $homeLineup->formation }}
                                    </p>
                                @endif
                            </div>
                            <div class="flex items-center space-x-2">
                                @if($homeLineup && !$homeLineup->is_confirmed)
                                    <button 
                                        wire:click="autoFillLineup({{ $homeTeam->id }})"
                                        class="px-3 py-1 text-xs bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                        Auto Fill
                                    </button>
                                @endif
                                
                                @if($homeLineup)
                                    @php
                                        $homeStartingCount = $homeLineup->players()->where('status', \App\Enums\PlayerStatus::STARTING)->count();
                                        $canConfirm = $homeStartingCount === 11 && !$homeLineup->is_confirmed;
                                    @endphp
                                    
                                    @if($homeLineup->is_confirmed)
                                        <x-filament::badge color="success">Confirmed</x-filament::badge>
                                    @elseif($canConfirm)
                                        <button 
                                            wire:click="confirmLineup({{ $homeLineup->id }})"
                                            class="px-3 py-1 text-xs bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                            Confirm Lineup
                                        </button>
                                    @else
                                        <x-filament::badge color="warning">{{ $homeStartingCount }}/11 Players</x-filament::badge>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Starting XI -->
                    <div class="p-6">
                        <h5 class="font-medium text-gray-900 dark:text-white mb-4">Starting XI</h5>
                        @if($homeLineup && $homeLineup->players->where('status', \App\Enums\PlayerStatus::STARTING)->count() > 0)
                            <div class="space-y-2">
                                @foreach($homeLineup->players->where('status', \App\Enums\PlayerStatus::STARTING)->sortBy('jersey_number') as $lineupPlayer)
                                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <span class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                                                {{ $lineupPlayer->jersey_number }}
                                            </span>
                                            <div>
                                                <p class="font-medium text-gray-900 dark:text-white">
                                                    {{ $lineupPlayer->player->name }}
                                                    @if($lineupPlayer->is_captain)
                                                        <span class="text-yellow-500">👑</span>
                                                    @endif
                                                </p>
                                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                                    {{ $lineupPlayer->position->getLabel() }}
                                                </p>
                                            </div>
                                        </div>
                                        @if(!$homeLineup->is_confirmed)
                                            <div class="flex items-center space-x-2">
                                                @if(!$lineupPlayer->is_captain)
                                                    <button 
                                                        wire:click="setCaptain({{ $lineupPlayer->id }})"
                                                        class="text-xs text-yellow-600 hover:text-yellow-700">
                                                        Make Captain
                                                    </button>
                                                @endif
                                                <button 
                                                    wire:click="removePlayerFromLineup({{ $lineupPlayer->id }})"
                                                    class="text-xs text-red-600 hover:text-red-700">
                                                    Remove
                                                </button>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                                <p>No starting players selected</p>
                                <p class="text-sm">Add players to the starting XI</p>
                            </div>
                        @endif

                        <!-- Add Player to Starting XI -->
                        @if($homeLineup && !$homeLineup->is_confirmed && $homeAvailablePlayers->count() > 0)
                            <div class="mt-4 p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                                <h6 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Add to Starting XI</h6>
                                <div class="grid grid-cols-1 gap-2">
                                    @foreach($homeAvailablePlayers->take(5) as $player)
                                        <button 
                                            wire:click="addPlayerToLineup({{ $homeTeam->id }}, {{ $player->id }}, '{{ $player->position->value }}', true)"
                                            class="flex items-center justify-between p-2 text-left bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                            <span class="text-sm text-gray-900 dark:text-white">{{ $player->name }}</span>
                                            <span class="text-xs text-gray-500 dark:text-gray-400">{{ $player->position->getLabel() }}</span>
                                        </button>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Substitutes -->
                    <div class="p-6 border-t border-gray-200 dark:border-gray-700">
                        <h5 class="font-medium text-gray-900 dark:text-white mb-4">Substitutes</h5>
                        @if($homeLineup && $homeLineup->players->where('status', \App\Enums\PlayerStatus::SUBSTITUTE)->count() > 0)
                            <div class="space-y-2">
                                @foreach($homeLineup->players->where('status', \App\Enums\PlayerStatus::SUBSTITUTE)->sortBy('jersey_number') as $lineupPlayer)
                                    <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                                        <div class="flex items-center space-x-3">
                                            <span class="w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs">
                                                {{ $lineupPlayer->jersey_number }}
                                            </span>
                                            <div>
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $lineupPlayer->player->name }}</p>
                                                <p class="text-xs text-gray-600 dark:text-gray-400">{{ $lineupPlayer->position->getLabel() }}</p>
                                            </div>
                                        </div>
                                        @if(!$homeLineup->is_confirmed)
                                            <button 
                                                wire:click="removePlayerFromLineup({{ $lineupPlayer->id }})"
                                                class="text-xs text-red-600 hover:text-red-700">
                                                Remove
                                            </button>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <p class="text-center py-4 text-gray-500 dark:text-gray-400 text-sm">No substitutes selected</p>
                        @endif

                        <!-- Add Substitutes -->
                        @if($homeLineup && !$homeLineup->is_confirmed && $homeAvailablePlayers->count() > 0)
                            <div class="mt-4">
                                <h6 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Add Substitutes</h6>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($homeAvailablePlayers->take(7) as $player)
                                        <button 
                                            wire:click="addPlayerToLineup({{ $homeTeam->id }}, {{ $player->id }}, '{{ $player->position->value }}', false)"
                                            class="px-2 py-1 text-xs bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors">
                                            {{ $player->name }}
                                        </button>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Away Team (Similar structure) -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                    <!-- Team Header -->
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ $awayTeam->name }} (Away)
                                </h4>
                                @if($awayLineup)
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        Formation: {{ $awayLineup->formation }}
                                    </p>
                                @endif
                            </div>
                            <div class="flex items-center space-x-2">
                                @if($awayLineup && !$awayLineup->is_confirmed)
                                    <button 
                                        wire:click="autoFillLineup({{ $awayTeam->id }})"
                                        class="px-3 py-1 text-xs bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                        Auto Fill
                                    </button>
                                @endif
                                
                                @if($awayLineup)
                                    @php
                                        $awayStartingCount = $awayLineup->players()->where('status', \App\Enums\PlayerStatus::STARTING)->count();
                                        $canConfirm = $awayStartingCount === 11 && !$awayLineup->is_confirmed;
                                    @endphp
                                    
                                    @if($awayLineup->is_confirmed)
                                        <x-filament::badge color="success">Confirmed</x-filament::badge>
                                    @elseif($canConfirm)
                                        <button 
                                            wire:click="confirmLineup({{ $awayLineup->id }})"
                                            class="px-3 py-1 text-xs bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                            Confirm Lineup
                                        </button>
                                    @else
                                        <x-filament::badge color="warning">{{ $awayStartingCount }}/11 Players</x-filament::badge>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Starting XI and Substitutes for Away Team (similar to Home Team) -->
                    <!-- ... (Similar structure as home team) ... -->
                </div>
            </div>

            <!-- Instructions -->
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">Lineup Setup Instructions</h4>
                <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                    <li>• Each team must have exactly 11 starting players</li>
                    <li>• Formation must include: 1 GK, min 3 DEF, min 2 MID, min 1 FWD</li>
                    <li>• Select one captain per team</li>
                    <li>• Add up to 7 substitutes per team</li>
                    <li>• Confirm both lineups before going live</li>
                </ul>
            </div>
        </div>
    @else
        <div class="text-center py-12">
            <p class="text-gray-500 dark:text-gray-400">No live game selected for lineup management.</p>
        </div>
    @endif
</div>

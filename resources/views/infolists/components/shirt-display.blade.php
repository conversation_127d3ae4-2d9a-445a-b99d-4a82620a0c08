<x-dynamic-component :component="$getEntryWrapperView()" :entry="$entry">
    @php
        $state = $getState();
        $logoUrl = $entry->getLogoUrl();
        $svgContent = file_get_contents(resource_path('js/Components/fantasy/common/shirt-01.svg'));

        // Default values if state is empty
        $shirtData = is_array($state) ? $state : [];
        $baseColor = $shirtData['base_color'] ?? '#ffffff';
        $patternType = $shirtData['pattern_type'] ?? '';
        $patternColor = $shirtData['pattern_color'] ?? '#000000';
    @endphp

    <div class="w-full max-w-sm mx-auto">
        <div class="relative w-full h-auto p-4 bg-gray-50 border border-gray-200 rounded-lg shadow-sm">
            <div x-data="shirtDisplay({
                    baseColor: '{{ $baseColor }}',
                    patternType: '{{ $patternType }}',
                    patternColor: '{{ $patternColor }}',
                    logoUrl: '{{ $logoUrl }}'
                })" x-init="renderShirt()" class="w-full h-full">
                <template x-ref="shirtTemplate" style="display: none;">{!! $svgContent !!}</template>
                <div x-html="renderedShirt" class="w-full h-full flex justify-center items-center"></div>
            </div>
        </div>

        @if($getLabel())
            <div class="mt-2 text-center">
                <span class="text-sm font-medium text-gray-700">{{ $getLabel() }}</span>
            </div>
        @endif
    </div>

    @push('scripts')
        <script>
            function shirtDisplay(config) {
                return {
                    baseColor: config.baseColor,
                    patternType: config.patternType,
                    patternColor: config.patternColor,
                    logoUrl: config.logoUrl,
                    renderedShirt: '',

                    renderShirt() {
                        if (!this.$refs.shirtTemplate) return;

                        const parser = new DOMParser();
                        const doc = parser.parseFromString(this.$refs.shirtTemplate.innerHTML, 'image/svg+xml');

                        // Update base color
                        const base = doc.querySelector('#base-color');
                        if (base && this.baseColor) {
                            base.setAttribute('fill', this.baseColor);
                            base.querySelectorAll('path').forEach(p => p.setAttribute('style', `fill: ${this.baseColor}`));
                        }

                        // Update logo
                        const logoElement = doc.querySelector('#club_logo image');
                        if (logoElement && this.logoUrl) {
                            logoElement.setAttribute('xlink:href', this.logoUrl);
                        }

                        // Hide all secondary patterns
                        const allPatternParts = doc.querySelectorAll(`[id^="secondary_pattern_"]`);
                        allPatternParts.forEach(part => part.style.display = 'none');

                        // Show and color the selected pattern
                        if (this.patternType) {
                            const patternParts = doc.querySelectorAll(`[id^="${this.patternType}"]`);
                            patternParts.forEach(part => {
                                part.style.display = 'block';
                                if (this.patternColor) {
                                    part.setAttribute('fill', this.patternColor);
                                    part.querySelectorAll('path').forEach(p => p.setAttribute('style', `fill: ${this.patternColor}`));
                                }
                            });
                        }

                        this.renderedShirt = new XMLSerializer().serializeToString(doc.documentElement);
                    }
                }
            }
        </script>
    @endpush
</x-dynamic-component>
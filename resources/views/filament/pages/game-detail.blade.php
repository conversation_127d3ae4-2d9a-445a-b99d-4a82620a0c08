<x-filament-panels::page>
    <!-- Game Header -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    {{ $game->game_display_name }}
                </h1>
                <p class="text-gray-600 dark:text-gray-400">
                    {{ $game->game_date?->format('l, F j, Y \a\t g:i A') }}
                </p>
            </div>
            <div class="text-right">
                <div class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    {{ $game->home_score ?? 0 }} - {{ $game->away_score ?? 0 }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    {{ $game->gameweek?->name }}
                </div>
            </div>
        </div>

        <!-- Status Badges -->
        <div class="flex items-center space-x-4">
            @php
                $gameStatus = $this->getGameStatusBadge();
                $liveStatus = $this->getLiveStatusBadge();
                $lineupsStatus = $this->getLineupsStatus();
            @endphp

            <x-filament::badge :color="$gameStatus['color']">
                Game: {{ $gameStatus['status'] }}
            </x-filament::badge>

            <x-filament::badge :color="$liveStatus['color']">
                Live: {{ $liveStatus['status'] }}
            </x-filament::badge>

            <x-filament::badge :color="$lineupsStatus['color']">
                Lineups: {{ $lineupsStatus['text'] }}
            </x-filament::badge>

            @if($liveGame?->status?->isActive())
                <x-filament::badge color="success" class="animate-pulse">
                    🔴 LIVE - {{ $liveGame->current_minute }}'
                </x-filament::badge>
            @endif
        </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
        <div class="border-b border-gray-200 dark:border-gray-700">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                @foreach($this->getAvailableTabs() as $tabKey => $tab)
                    @if($tab['available'])
                        <button wire:click="setTab('{{ $tabKey }}')" class="py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200
                                                        @if($currentTab === $tabKey)
                                                            border-primary-500 text-primary-600 dark:text-primary-400
                                                        @else
                                                            border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300
                                                        @endif">
                            <div class="flex items-center space-x-2">
                                <x-filament::icon :icon="$tab['icon']" class="w-5 h-5" />
                                <span>{{ $tab['label'] }}</span>
                            </div>
                        </button>
                    @endif
                @endforeach
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="space-y-6">
        @if($currentTab === 'overview')
            <!-- Overview Tab -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Game Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Game Information</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Home Team</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">{{ $game->homeTeam?->name ?? 'Not assigned' }}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Away Team</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">{{ $game->awayTeam?->name ?? 'Not assigned' }}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Gameweek</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">{{ $game->gameweek?->name ?? 'Not assigned' }}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Scheduled Date</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">
                                {{ $game->game_date?->format('M j, Y H:i') ?? 'Not scheduled' }}
                            </dd>
                        </div>
                    </dl>
                </div>

                <!-- Live Game Status -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Live Management Status</h3>

                    @if(!$liveGame)
                        <div class="text-center py-8">
                            <x-filament::icon icon="heroicon-o-play" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                            <p class="text-gray-500 dark:text-gray-400 mb-4">Live game not set up yet</p>
                            <p class="text-sm text-gray-400 dark:text-gray-500">Click "Setup Live Game" to begin</p>
                        </div>
                    @else
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Live Status</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $liveGame->status->getLabel() }}</dd>
                            </div>
                            @if($liveGame->kick_off_time)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Kick Off</dt>
                                    <dd class="text-sm text-gray-900 dark:text-white">{{ $liveGame->kick_off_time->format('H:i') }}
                                    </dd>
                                </div>
                            @endif
                            @if($liveGame->status->isActive())
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Current Minute</dt>
                                    <dd class="text-sm text-gray-900 dark:text-white">{{ $liveGame->current_minute }}'</dd>
                                </div>
                            @endif
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Events Recorded</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ count($liveGame->events ?? []) }}</dd>
                            </div>
                        </dl>
                    @endif
                </div>
            </div>

            <!-- Next Steps -->
            @if($liveGame)
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">Next Steps</h3>

                    @if($liveGame->status === \App\Enums\LiveGameStatus::PRE_MATCH)
                        @php
                            $confirmedLineups = $liveGame->lineups()->where('is_confirmed', true)->count();
                        @endphp

                        @if($confirmedLineups < 2)
                            <div class="flex items-start space-x-3">
                                <x-filament::icon icon="heroicon-o-users" class="w-6 h-6 text-blue-600 dark:text-blue-400 mt-0.5" />
                                <div>
                                    <p class="text-blue-900 dark:text-blue-100 font-medium">Set up team lineups</p>
                                    <p class="text-blue-700 dark:text-blue-300 text-sm">Go to the Lineups tab to configure starting XI
                                        for both teams.</p>
                                </div>
                            </div>
                        @else
                            <div class="flex items-start space-x-3">
                                <x-filament::icon icon="heroicon-o-play" class="w-6 h-6 text-green-600 dark:text-green-400 mt-0.5" />
                                <div>
                                    <p class="text-green-900 dark:text-green-100 font-medium">Ready to go live!</p>
                                    <p class="text-green-700 dark:text-green-300 text-sm">Both lineups are confirmed. Click "GO LIVE" to
                                        start the match.</p>
                                </div>
                            </div>
                        @endif
                    @elseif($liveGame->status->isActive())
                        <div class="flex items-start space-x-3">
                            <x-filament::icon icon="heroicon-o-bolt" class="w-6 h-6 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                            <div>
                                <p class="text-yellow-900 dark:text-yellow-100 font-medium">Match is live!</p>
                                <p class="text-yellow-700 dark:text-yellow-300 text-sm">Use the Live Control tab to record goals,
                                    cards, and substitutions.</p>
                            </div>
                        </div>
                    @elseif($liveGame->status->isCompleted())
                        <div class="flex items-start space-x-3">
                            <x-filament::icon icon="heroicon-o-check-circle"
                                class="w-6 h-6 text-green-600 dark:text-green-400 mt-0.5" />
                            <div>
                                <p class="text-green-900 dark:text-green-100 font-medium">Match completed</p>
                                <p class="text-green-700 dark:text-green-300 text-sm">Check the Match Summary tab for final
                                    statistics and fantasy points.</p>
                            </div>
                        </div>
                    @endif
                </div>
            @endif
        @endif

        @if($currentTab === 'lineups' && $liveGame)
            <!-- Lineups Tab -->
            <div>
                @livewire('lineup-management-new', ['selectedLiveGameId' => $liveGame->id])
            </div>
        @endif

        @if($currentTab === 'live' && $liveGame?->status?->isActive())
            <!-- Live Control Tab -->
            <div>
                @livewire('live-events-management', ['selectedLiveGameId' => $liveGame->id])
            </div>
        @endif

        @if($currentTab === 'summary' && $liveGame?->status?->isCompleted())
            <!-- Match Summary Tab -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Match Summary</h3>
                <p class="text-gray-600 dark:text-gray-400">
                    Match summary with final statistics and fantasy points will be displayed here.
                </p>

                <!-- Placeholder for future fantasy points integration -->
                <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-2">Fantasy Points Integration</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        This section will show calculated fantasy points using the FantasyPointsCalculationService.
                    </p>
                </div>
            </div>
        @endif
    </div>

    <x-filament-actions::modals />
</x-filament-panels::page>
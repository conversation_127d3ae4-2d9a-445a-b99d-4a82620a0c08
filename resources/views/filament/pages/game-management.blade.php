<x-filament-panels::page x-data="{ tabId: 1 }">
    <x-filament::tabs label="Game Management">
        <!-- Tab Items -->
        <x-filament::tabs.item icon="heroicon-o-play" alpine-active="tabId === 1" x-on:click="tabId = 1">
            Live Games
        </x-filament::tabs.item>

        <x-filament::tabs.item icon="heroicon-o-users" alpine-active="tabId === 2" x-on:click="tabId = 2">
            Lineups
        </x-filament::tabs.item>

        <x-filament::tabs.item icon="heroicon-o-chart-bar" alpine-active="tabId === 3" x-on:click="tabId = 3">
            Statistics
        </x-filament::tabs.item>

        <x-filament::tabs.item icon="heroicon-o-clock" alpine-active="tabId === 4" x-on:click="tabId = 4">
            Events
        </x-filament::tabs.item>
    </x-filament::tabs>

    <!-- Tab Contents -->
    <div x-show="tabId === 1">
        @livewire('live-game-management')
    </div>

    <div x-show="tabId === 2">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Lineup Management</h3>
            <div class="text-center py-8">
                <x-heroicon-o-users class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    Select a specific game to manage lineups
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-500">
                    Click "Manage" on any game in the Live Games tab to set up lineups and go live.
                </p>
            </div>
        </div>
    </div>

    <div x-show="tabId === 3">
        {{-- Statistics component will go here --}}
        <div class="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Game Statistics</h3>
            <p class="text-gray-600 dark:text-gray-400">Statistics component will be implemented here.</p>

            <!-- Placeholder for future statistics -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-900 dark:text-white">Player Performance</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Real-time player statistics</p>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-900 dark:text-white">Team Stats</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Possession, shots, passes</p>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-900 dark:text-white">Fantasy Points</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Live fantasy scoring</p>
                </div>
            </div>
        </div>
    </div>

    <div x-show="tabId === 4">
        @livewire('live-events-management')
    </div>
</x-filament-panels::page>
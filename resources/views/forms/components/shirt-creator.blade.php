<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    @php
        $statePath = $getStatePath();
        $svgContent = file_get_contents(resource_path('js/Components/fantasy/common/shirt-01.svg'));
    @endphp

    <div x-data="shirtCreator({
            state: $wire.entangle('{{ $statePath }}'),
            logo: '/storage/{{ $get('logo') }}'
        })"
         class="flex space-x-4">

        <template x-ref="shirtTemplate" style="display: none;">{!! $svgContent !!}</template>

        <div class="w-1/2">
            <div class="w-full h-auto p-4 border rounded-md">
                <div x-html="shirtPreview" class="w-full h-full"></div>
            </div>
        </div>

        <div class="w-1/2 space-y-4">
            <div>
                <label for="base_color" class="block text-sm font-medium text-gray-700">Base Color</label>
                <input type="color" x-model="state.base_color" id="base_color" class="w-full h-10 px-3 py-2 mt-1 border rounded-md">
            </div>

            <div>
                <label for="pattern_type" class="block text-sm font-medium text-gray-700">Pattern</label>
                <select x-model="state.pattern_type" id="pattern_type" class="w-full px-3 py-2 mt-1 border rounded-md">
                    <option value="">None</option>
                    <option value="secondary_pattern_01">Pattern 1</option>
                    <option value="secondary_pattern_02">Pattern 2</option>
                    <option value="secondary_pattern_03">Pattern 3</option>
                    <option value="secondary_pattern_04">Pattern 4</option>
                    <option value="secondary_pattern_05">Pattern 5</option>
                    <option value="secondary_pattern_06">Pattern 6</option>
                    <option value="secondary_pattern_07">Pattern 7</option>
                    <option value="secondary_pattern_08">Pattern 8</option>
                    <option value="secondary_pattern_09">Pattern 9</option>
                    <option value="secondary_pattern_10">Pattern 10</option>
                    <option value="secondary_pattern_11">Pattern 11</option>
                </select>
            </div>

            <div x-show="state.pattern_type">
                <label for="pattern_color" class="block text-sm font-medium text-gray-700">Pattern Color</label>
                <input type="color" x-model="state.pattern_color" id="pattern_color" class="w-full h-10 px-3 py-2 mt-1 border rounded-md">
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function shirtCreator(config) {
            return {
                state: config.state,
                logo: config.logo,
                shirtSvg: '',
                init() {
                    this.shirtSvg = this.$refs.shirtTemplate.innerHTML;
                    if (typeof this.state !== 'object' || this.state === null) {
                        this.state = {
                            base_color: '#ffffff',
                            pattern_type: '',
                            pattern_color: '#000000'
                        };
                    }
                },
                get shirtPreview() {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(this.shirtSvg, 'image/svg+xml');
                    const base = doc.querySelector('#base-color');
                    if (base && this.state && this.state.base_color) {
                        base.setAttribute('fill', this.state.base_color);
                        base.querySelectorAll('path').forEach(p => p.setAttribute('style', `fill: ${this.state.base_color}`));
                    }

                    const logoElement = doc.querySelector('#club_logo image');
                    if (logoElement && this.logo) {
                        console.log(this.logo);
                        logoElement.setAttribute('xlink:href', this.logo);
                    }

                   // Hide all secondary patterns at once
                    const allPatternParts = doc.querySelectorAll(`[id^="secondary_pattern_"]`);
                    allPatternParts.forEach(part => part.style.display = 'none');

                    // Show and color the selected pattern
                    if (this.state && this.state.pattern_type) {
                        const patternParts = doc.querySelectorAll(`[id^="${this.state.pattern_type}"]`);
                        patternParts.forEach(part => {
                            part.style.display = 'block';
                            if (this.state.pattern_color) {
                                part.setAttribute('fill', this.state.pattern_color);
                                part.querySelectorAll('path').forEach(p => p.setAttribute('style', `fill: ${this.state.pattern_color}`));
                            }
                        });
                    }

                    return new XMLSerializer().serializeToString(doc.documentElement);
                }
            }
        }
    </script>
    @endpush
</x-dynamic-component>